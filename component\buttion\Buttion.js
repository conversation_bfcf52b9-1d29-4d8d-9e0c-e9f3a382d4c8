import React from "react";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import buttion from "@/styles/buttion.module.scss"; // Assuming you are using a CSS module for button styles
import Link from "next/link";

const Buttion = ({ text, href, moduleClass, imageSrc, icn_invert, aosType, aosDuration }) => {
  return (
    <Link  className={`${buttion.buttion} ${buttion.blue} ${comon.button} ${moduleClass}`} href={href} data-aos={aosType}
    data-aos-duration={aosDuration}>
      {text && <span>{text}</span>}
      <Image
        src={imageSrc} // Use the image source prop
        alt="call"
        width={15}

        height={15}
        style={icn_invert ? { filter: `invert(${icn_invert})` } : {}}
      />
    </Link>
  );
};

export default Buttion;
