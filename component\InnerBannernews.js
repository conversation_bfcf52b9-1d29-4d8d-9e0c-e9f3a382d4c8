import React, { useEffect, useState } from "react";
import style from "@/styles/innerBanner.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import Link from "next/link";
import parse from "html-react-parser";

const InnerBanner = ({
  pagename,
  breadcrumb1,
  background,
  backgroundType = "fullbg", // New prop for background type
  videoSrc,
  headerFontSize,
  dateSec,
  paddingTop,
  paddingBottom,
  banner_type,
  tagsnews,
  bannerbutton,
}) => {
  return (
    <div>
      <section
        className={`${style.inner_banner_section} ${
          backgroundType === "fullbg" ? style.full_bg : style.icon_bg
        } inner_banner_section`}
        style={{
          backgroundColor: "#222345",
          ...(background &&
            !videoSrc && {
              backgroundImage: `url(${background})`,
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
              backgroundSize: "cover",
            }),
        }}
      >
        {/* Render video if `videoSrc` is provided */}
        {banner_type === "video" && videoSrc && (
          <video
            className={style.background_video}
            src={videoSrc}
            autoPlay
            loop
            muted
          />
        )}

        <div
          className={`${style.inner_banner_container} ${
            comon.inner_banner_container
          } ${comon.wrap} 
         
          `}
          data-aos="fade-up"
          data-aos-duration="1000"
        >
           {/* class removed from above div ${comon.pb_200} ${paddingTop ? "paddingTop370" : ""} ${paddingBottom ? "paddingBottom150" : ""} */}
          
          
          {tagsnews && (
            <div className={style.inner_banner_breadcrumb_sec}>
              <div className={style.inner_banner_breadcrumb_head}>
                <span>{tagsnews && parse(tagsnews)}</span>
              </div>
            </div>
          )}
          {breadcrumb1 && (
            <div className={style.inner_banner_breadcrumb_sec}>
              {breadcrumb1.map((bread, index) => (
                <div
                  className={`${style.inner_banner_breadcrumb_head} ${comon.inner_banner_breadcrumb_head}`}
                  key={index}
                >
                  <Link href={bread?.page_link || "#."}>
                    {bread?.page_names && parse(bread.page_names)}
                  </Link>
                </div>
              ))}
            </div>
          )}

          {pagename && (
            <h1 className={headerFontSize ? "font70" : ""}>
              {pagename && parse(pagename)}
            </h1>
          )}
          {dateSec && (
            <div className={style.inner_banner_date_wrapper}>
            <span className={style.inner_banner_date}>{dateSec}</span>
            </div>
          )}
          {bannerbutton && bannerbutton?.url && (
            <div className={comon.external_article_button_wrapper}>
              <Link
                href={bannerbutton?.url}
                target={bannerbutton?.target}
                className={comon.external_article}
              >
                <span>
                {bannerbutton?.title && parse(bannerbutton.title)}
                </span>
                <Image src="/images/buttion_arrow_white.svg" width={15} height={15} alt="" />
              </Link>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default InnerBanner;
