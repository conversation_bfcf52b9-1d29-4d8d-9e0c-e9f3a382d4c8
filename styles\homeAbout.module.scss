@import "variable", "base";


.hm_abt{
  margin-top: 1px;
}
.txt_row_01 {
  padding-inline-start: 18%;
  position: relative;
  z-index: 2;
  @media #{$media-1600} {
    padding-inline-start: 27%;
  }
  @media #{$media-1440} {
    padding-inline-start: 30%;
  }
  @media #{$media-1024} {
    padding-inline-start: 35%;
  }
  @media #{$media-820} {
    padding-inline-end: 5%;
    padding-inline-start: 45%;
  }
  @media #{$media-700} { padding-inline-start:0; padding-inline-end: 0;}
 h2{
  color: #ffffff;
  font-family: var(--helveticaNeueMedium);
  text-transform: uppercase;
  @media #{$media-1024} {
    margin-bottom: 0px;
  }
 
 }
 p{
   color: #ffffff;
   font-family: var(--helveticaneueltarabicLight);
   font-size: 1.25rem;
   line-height: 1.975rem;
   padding-inline-end: 160px;
   font-weight: 300;
  @media #{$media-1024} {
    font-size: 1.3rem;
    line-height: 2.3rem;
  }
  @media #{$media-995} {
    font-size: 1.5rem;
    line-height: 2.3rem;
    padding-inline-end: 0;
  }
  @media #{$media-700} {
    font-size: 1.9rem;
    line-height: 2.7rem;
  }
}

}

.txt_row_02 {
  padding-inline-start: 18%;
  position: relative;
  z-index: 2;
  @media #{$media-1600} {
    padding-inline-start: 27%;
  }
  @media #{$media-1440} {
    padding-inline-start: 30%;
  }
  @media #{$media-1024} {
    padding-inline-start: 35%;
  }
  @media #{$media-820} {
    padding-inline-end: 0%;
    padding-inline-start: 35%;
    padding-top: 35px;
    padding-bottom: 50px;
  }
  @media #{$media-700} { padding-inline-start: 0; padding-inline-end: 0;padding-bottom: 10px;}
}
 .home_counder{ display: flex; 
  justify-content: space-between;
  @media #{$media-1024} {
    flex-wrap: wrap;
    // justify-content: flex-end;
    row-gap: 25px;
  }
  li{ list-style: none; 
    min-height: 149px;
    @media #{$media-1600} {
      min-height: 127px;
    }
    @media #{$media-1024} {
      min-height: 97px;
    }
    @media #{$media-767} {
      min-height: 80px;
    }
   > span{color: #ffffff;
     opacity: 1; font-size: 1.35rem;
     font-family: var(--helveticaneueltarabicLight);
     display: inline-block;
     @media #{$media-1600} {
      max-width: 135px;
     }
    @media #{$media-1024} {
      font-size: 1.3rem;
    }
    @media #{$media-700} {
      font-size: 1.9rem;
      line-height: 2.6rem;
    }
  }
   @media #{$media-1024} {
    width: 45%;
  }


img{
  @media #{$media-1024} {
    display: none;
  }
}
&:nth-child(2n+2){
  @media #{$media-1024} {
    display: none;
  }
}

  }

  h3 {
    font-size: 4.688rem;
    font-family: var(--helveticaneueltarabicLight);
    color: #ffffff;
    line-height: 100%;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-end;
    span{
      direction: ltr;
    }
    @media #{$media-1024} {
        margin-bottom: 5px;
     }
     @media #{$media-995} {
      font-size: 4rem;
    }
    .home_counder_countup {

      font-size: 4.688rem;
    }
////
    .small {
      font-size: 2.188rem;
      line-height: 100%;
      padding-left: 10px;
      padding-right: 10px;
      @media #{$media-767} {
        font-size: 2rem;
      }
    }
  }

  @media #{$media-700} {
    flex-wrap: wrap;
    justify-content: flex-start;
    row-gap: 20px;
    column-gap: 25px;
  }

  & > :nth-child(even){
    @media #{$media-700} {
    display: none;
    }
  }
}