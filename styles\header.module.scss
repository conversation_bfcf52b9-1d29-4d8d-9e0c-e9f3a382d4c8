@import "variable", "base";

.header {
  width: 100%;
  padding-top: 30px;
  padding-bottom: 10px;
  position: fixed;
  width: 100%;
  left: 0;
  top: 50px;
  z-index: 100;
  transition: all .3s ease-in-out;

  @media #{$media-767} {
    top: 15px;
  }
}

.header.sticky {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: #2a2656;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  padding: 10px 0;
}

.marqui {
  background: #5e45ff;
}

.marquee {
  display: flex;
  // padding-top: 15px;
  // padding-bottom: 15px;
  height: var(--marq_height);
  background: #5e45ff;
  color: #fff;
  font-size: 16px;
  position: relative;
  text-align: center;
  font-weight: 400;
  line-height: 20px;
  z-index: 999;
  direction: ltr;

  @media #{$media-767} {
    padding: 5px 0;
  }
}

.marquee_item {
  border-right: solid 1px #fff !important;
  padding-left: 15px;
  padding-right: 15px;
  font-size: 16px;
  font-weight: 600;
  display: flex;

  @media #{$media-1440} {
    font-size: 17px;
  }

  @media #{$media-1024} {
    font-size: 14px;
  }

  @media #{$media-767} {
    font-size: 13px;
  }

  span {
    font-size: 16px;
    font-family: var(--helveticaneueltarabicLight);
    font-weight: 400;

    @media #{$media-1440} {
      font-size: 17px;
    }

    @media #{$media-1024} {
      font-size: 14px;
    }

    @media #{$media-767} {
      font-size: 13px;
    }

    b {
      font-family: var(--helveticaneueltarabicBold);

      @media #{$media-767} {
        font-family: var(--helveticaNeue);
      }
    }
  }

  // span+span {
  //   margin-inline-start: 10px;
  // }

}

.about_hero__marquee_row {
  display: flex;
  position: relative;

  .marq_block {}
}

.logo_block {
  width: 10.4%;
  cursor: pointer;

  @media #{$media-820} {
    z-index: 999;
  }

  @media #{$media-767} {
    width: 30%;
  }

  img {
    @media #{$media-820} {
      width: 90px;
    }

    &:focus,
    &:hover,
    &:focus-visible,
    &:active,
    &:target,
    &:focus-within {
      border: none !important;
      outline: none !important;
      background-color: transparent !important;
      background: none !important;
    }
  }

  a {

    &:focus,
    &:hover,
    &:focus-visible,
    &:active,
    &:target,
    &:focus-within {
      border: none !important;
      outline: none !important;
      background-color: transparent !important;
      background: none !important;
    }

  }
}

.digital_header {
  .logo_block {
    img {
      @media #{$media-820} {
        width: 135px;
      }
    }

  }

}

.center_block {
  width: 64%;
  width: calc(89.6% - 265px);

  @media #{$media-820} {
    width: calc(89.6% - 258px);
  }
}

.right_block {
  width: 265px;
  justify-content: flex-end;
  margin-inline-start: auto;

  ul {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-inline-start: 10px;

    li {
      list-style: none;
      padding: 0% 10px;
    }

    @media #{$media-820} {
      width: calc(100% - 30px);
      margin: 0;
    }
  }

  @media #{$media-820} {
    // width: 85%;
    align-items: center;
  }

  @media #{$media-767} {
    width: 95px;
  }
}

.digital_header {
  .logo_block {
    width: 14.4%;
    cursor: pointer;
    // @media #{$media-767} {
    //   height: 35px;
    // }
  }

  .center_block {
    width: calc(85.6% - 290px);

    @media #{$media-1024} {
      width: calc(85.6% - 260px);
    }

    @media #{$media-820} {
      width: calc(85.6% - 275px);
    }
  }

  .right_block {
    @media #{$media-1024} {
      width: 260px;
    }

    @media #{$media-820} {
      width: 275px;
    }

    @media #{$media-767} {
      width: 95px;
    }
  }

}

.nav_bar {
  display: flex;
  flex-wrap: wrap;


  .nav_bar>ul {

    height: 50vh;
    overflow: scroll;


  }

  ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding-top: 10px;

    li {
      list-style: none;
      padding: 0 2%;

      @media #{$media-1024} {
        padding: 0 1.7%;
      }

      @media #{$media-820} {
        width: 100%;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;

      }

      a {
        font-size: 1rem;
        font-family: var(--helveticaNeueMedium);
        color: $white;
        font-weight: 500;

        @media #{$media-1600} {
          font-size: 1.2rem;
        }

        @media #{$media-1024} {
          font-size: 1.1rem;
        }

        @media #{$media-820} {
          color: #fff;
          font-size: 2rem;
          width: auto;
          display: block;
          flex-grow: 1;
        }

        @media #{$media-767} {
          font-family: var(--helveticaNeue);
        }
      }

      &:hover {
        a {
          color: #5e45ff;
        }

        .submenu {
          background-position: 0 -12px;
        }

        @media #{$media-820} {
          a {
            color: #fff;
          }

          .submenu {
            background-position: 0 7px;
          }
        }
      }


      a.active {
        color: #5e45ff;
      }

      .submenu.active {
        background-position: 0 -12px;
      }

      &:last-child {
        @media #{$media-820} {
          border-bottom: none;
        }
      }

      >div {
        @media #{$media-820} {
          justify-content: space-between !important;
        }
      }
    }

    .enq_mob {
      @media #{$media-820} {
        padding-top: 30px;

        a {
          flex-grow: unset;
        }
      }
    }
  }

  @media #{$media-820} {
    transition: all 0.35s ease-in-out;
    position: fixed;
    z-index: 800;
    background-image: url(../public/images/menu_bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    top: 0;
    overflow: hidden;
    right: -100%;
    align-items: flex-start;
    display: flex;
    height: 100vh;
    width: 100%;
  }
}

.togle_menu {
  display: none;
  width: 30px;
  height: 20px;
  position: relative;
  z-index: 999;

  @media #{$media-820} {
    display: block;
  }

  .toggle_line {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: #6856db;
    border-radius: 4px;
    opacity: 1;
    top: 0;
    left: 0;
    transform: rotate(0);
    transition: 0.25s ease-in-out;

    &:nth-child(2) {
      top: 8px;
      width: 25px;
    }

    &:nth-child(3) {
      top: 16px;
    }
  }
}

.submenu {
  width: 11px;
  height: 8px;
  display: inline-block;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(../public/images/submenu_arw_2.png);
  margin-inline-start: 10px;
  cursor: pointer;
  transition: all 0.5s ease;

  &.toggleSubmenu {
    .mob_arrow {
      transform: rotate(180deg);
    }

  }

  @media #{$media-820} {
    // background-position: 0 7px;
    // background-image: url(../public/images/mob_arw.png);
    // width: 10px;
    // height: 15px;
    // flex-grow: 1;
    background: none;
    width: 20px;
    height: 20px;
    background-color: transparent;
    border: 1px solid #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

  }

  .mob_arrow {
    display: none;
    transition: all 0.6s ease;

    @media #{$media-820} {
      display: block;
    }
  }
}

.active>.submenu {
  transform: rotate(180deg);
}

.header .megamenu {
  position: absolute;
  top: 100%;
  /* Adjust based on header height */
  left: 0;
  z-index: 10;
  /* Ensure it layers above but doesn't interfere with navigation */
  display: block;
  width: 100vw;
  /* Adjust width as needed */
  transition: opacity 0.15s ease-out, visibility 0.35s ease-out,
    top 0.15s ease-out, transform 0.15s ease-in-out;
  opacity: 0;
  visibility: hidden;
}

.header .active .megamenu {
  opacity: 1;
  visibility: visible;
}

.advisory_mob {
  @media #{$media-820} {
    padding-inline-start: 10px;
    padding-top: 20px;
    width: 100%;
    display: flex;
    justify-content: flex-start !important;

    
  }
  a {
    @media #{$media-820} {
      display: flex !important;
      align-items: center;
    flex-grow: unset !important;
    height: 35px !important;
    padding: 5px 15px !important;
    }
  }
}









// ===============mobile header=============





@media #{$media-820} {


  .header_submenu_sec {
    display: block;
    opacity: 0;
    width: 100%;
    height: 0;
    transition: all 1s ease;



    &.header_submenu_sec_active {
      padding: 0 0 0 0;
      padding-inline-start: 10px;
      height: auto;
      margin-top: 15px;
      opacity: 1;

      @media #{$media-500} {
        margin-top: 10px;
      }






    }

    .header_submenu_li {
      margin: 0;
      width: 100%;
      display: flex;
      align-items: center;
      // flex-direction: column;
      flex-wrap: wrap;
      color: #6856db;
      justify-content: flex-start;
      font-family: var(--helveticaneuelt_arabic_55);

      font-weight: 600;
      padding: 6px 0;
      border-bottom: 1px solid #5e45ff30;
      transition: all 0.5s ease;

      &:last-child {
        @media #{$media-700} {
          border-bottom: none;
          padding-bottom: 0;
        }
      }

      @media #{$media-700} {
        padding: 17px 0;
      }


      a {

        font-size: 17px !important;

        @media #{$media-500} {
          font-size: 15px !important;
        }

      }

      .submenu {
        width: 10px;
        // height: 15px;
        display: inline-block;
        background-size: 100%;
        background-repeat: no-repeat;
        // background-image: url(../public/images/submenu_arw_mob.png);
        margin-inline-start: 10px;
        cursor: pointer;
        opacity: 0.8;
        transition: all 0.5s ease;

        @media #{$media-820} {
          background: none;
          width: 20px;
          height: 20px;
          background-color: transparent;
          border: 1px solid #ffffff;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;

        }


      }

      ul {
        padding: 0 0 0 10px;
        margin: 0;
        width: 100%;
        height: 0;
        opacity: 0;
        transition: all 0.5s ease;

        &.active {
          height: auto;
          opacity: 1;
          padding-top: 10px;
        }

        li {
          padding: 0;
          margin: 0;
          border: 0;
          width: 100%;
          color: #6856db;
          font-weight: 400;

          a {
            font-size: 15px !important;
            line-height: 18px;
            font-family: var(--helveticaneuelt_arabic_55);

          }

          @media #{$media-767} {
            padding: 15px 0;
          }
        }
      }
    }
  }
}

.enq_mob {
  display: none;

  @media #{$media-820} {
    display: block;
  }

  a {
    display: flex !important;
    align-items: center;
    justify-content: center;
    background-color: transparent !important;
    border: 1px solid #fff;
    color: #fff !important;
    font-size: 13px !important;
    height: 35px !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  img {
    filter: invert(1);
    width: 10px;
  }
}

.enquire {
  padding-inline-end: 0 !important;

  @media #{$media-820} {
    padding-inline-end: 10px !important;
  }
}