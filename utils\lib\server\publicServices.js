import axios from "@/utils/axios";
import CONSTANTS from "@/utils/constants";
import { errorHandler } from "@/utils/utils";
import { getToken } from "@/utils/getToken";

const token = getToken(); // Get the token using the utility function



//IR menu link

export const getIrMenuLinks = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.IrMenuLinks.IrMenus}?lang=${locale.locale || locale} `,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    return errorHandler(error);
  }
};

//Themeoptions

export const getThemeoptions = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.Themeoptions.ThemeoptionsApi}?lang=${locale}`,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    return errorHandler(error);
  }
};


// Home page
export const getHome = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.HomePage.HomeApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.HomeArPage.HomeArApi}?acf_format=standard`, {      
        headers,
      });
    }
//console.log('response', response)
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// about us page
export const getAbout = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.AboutPage.AboutApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.AboutArPage.AboutArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Awards page
export const getAwards = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.AwardsPage.AwardsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.AwardsArPage.AwardsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Awards post

export const getAwardsPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.AwardsPost.AwardsPostapi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.AwardsPost.AwardsPostapi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Team post

export const getTeamPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.TeamsPost.TeamsPostapi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.TeamsPost.TeamsPostapi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


//Team categories page
export const getTeamCategories = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.TeamsTaxonomy.TeamsTaxonomyApi}?acf_format=standard&lang=${locale.locale}`,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Client us page
export const getClients = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.ClientPage.ClientApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.ClientArPage.ClientArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Investors Relations page
export const getInvestorsRelations = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.IrPage.IrApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.IrArPage.IrArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// ShareInfo page
export const getShareInfo = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.IrshareinfoPage.IrshareinfoApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.IrshareinfoArPage.IrshareinfoArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Investors Resources page
export const getInvestorsResources = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.IrInvestorsResPage.IrInvestorsResApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.IrInvestorsResArPage.IrInvestorsResArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
export const getIRslugPage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.IrInvestorsResPage.IrInvestorsResApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.IrInvestorsResArPage.IrInvestorsResArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// InvestorsResources post
export const getInvestorsResourcesPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(
        `${CONSTANTS.InvestorsResourcesPost.InvestorsResourcesPostapi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.InvestorsResourcesPost.InvestorsResourcesPostapi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Media_resourcesPost post
export const getMediaresourcesPostPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.MediaResourcesPost.MediaResourcesPostapi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.MediaResourcesPost.MediaResourcesPostapi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Ir Media Resources 
export const getMediaResources = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.IrmediaPage.IrmediaApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.IrmediaArPage.IrmediaArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Company Announcement
export const getCompanyAnnouncement = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.IrCompanyPage.IrCompanyApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.IrCompanyArPage.IrCompanyArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Digital page
export const getDigitalpage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.DigitalPage.DigitalApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.DigitalArPage.DigitalArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Company Announcement post
export const getCompanyPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(
        `${CONSTANTS.CompanyPost.CompanyPostapi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.CompanyPost.CompanyPostapi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Company Announcement Slug post
export const getCompanySlugPosts = async (slug,locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(
        `${CONSTANTS.CompanyPost.CompanyPostapi}?acf_format=standard&slug=${slug ? slug : ""}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.CompanyPost.CompanyPostapi}?acf_format=standard&per_page=100&lang=ar&slug=${slug ? slug : ""}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Digital post
export const getDigitalPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(
        `${CONSTANTS.DigitalPost.DigitalPostApi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.DigitalPost.DigitalPostApi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

//resources categories
export const getResourcesCategories = async (slug,locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.InvestorsTaxonomy.InvestorsTaxonomyApi}?acf_format=standard&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
export const getResourcesCategoriesList = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.InvestorsTaxonomy.InvestorsTaxonomyApi}?acf_format=standard&per_page=100&lang=${locale}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Advisory Page
export const getAdvisory = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.AdvisoryPage.AdvisoryApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.AdvisoryArPage.AdvisoryArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Advisory Taxonamy slugs

export const getAdvisorytaxSlugs = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.AdvisoryTaxonomy.AdvisoryTaxonomyApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


//Advisory list post categery

export const getAdvisorytaxList = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.ProductstaxPage.ProductstaxApi}?acf_format=standard&per_page=100&lang=${locale}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Advisory post
export const getAdvisoryPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(
        `${CONSTANTS.AdvisoryPost.AdvisoryPostApi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.AdvisoryPost.AdvisoryPostApi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};



// AdvisoryPost slugs

export const getAdvisoryPostSlugs = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${
        CONSTANTS.AdvisoryPost.AdvisoryPostApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};



// Insights post

export const getInsightsPosts = async (locale, query) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(
        `${CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&${query}`,
        {
          headers,
        }
      );
    } else {
      response = await axios.get(
        `${CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&lang=ar&${query}`,
        {
          headers,
        }
      );
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Careers page
export const getCareerspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.CareersPage.CareersApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.CareersArPage.CareersArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Insights page
export const getInsightspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.InsightsPage.InsightsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.InsightsArPage.InsightsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Insights page project
export const getInsightsproject = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.InsightsPage.InsightsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.InsightsArPage.InsightsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


//Insights post categery

export const getInsightstaxonamyList = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.Insightstaxonamy.InsightstaxonamyApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
export const getInsightsRelatedTaxonamy = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.Insightstaxonamy.InsightstaxonamyApi}?acf_format=standard&per_page=100&lang=${locale}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Insights post slugs

export const getInsightsPostSlugs = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${
        CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};



//industriesCategory list post categery

export const getindustriesCatList = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.industriestaxonamy.industriestaxonamyApi}?per_page=100&lang=${locale}`,
      {
        headers,
      }
    );
// console.log('response', response.path)
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

//topicCategory list post categery

export const gettopicCatList = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.topictaxonamy.topictaxonamyApi}?acf_format=standard&per_page=100&lang=${locale}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};



// Privacy Policy page
export const getPrivacypage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.PrivacyPage.PrivacyApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.PrivacyArPage.PrivacyArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Terms of Service page
export const getTermspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.TermsPage.TermsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.TermsArPage.TermsArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Cookies Settings page
export const getCookiespage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale.locale == "en") {
      response = await axios.get(`${CONSTANTS.CookiesPage.CookiesApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.CookiesArPage.CookiesArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};
