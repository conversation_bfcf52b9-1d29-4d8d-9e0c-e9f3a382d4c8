import React, { useEffect, useState, useRef } from "react";
import header from "@/styles/header.module.scss";
import comon from "@/styles/comon.module.scss";
import style from "@/styles/MegaMenu.module.scss";
import Image from "next/image";
import Link from "next/link";
import Buttion from "@/component/buttion/Buttion";
import buttion from "@/styles/buttion.module.scss";
import MegaMenu from "@/component/MegaMenu";
import HeaderMarquee from "@/component/Headermarquee";
import { useRouter } from "next/router";
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Header = (props) => {
  const [activeMenuItem, setActiveMenuItem] = useState(null);
  const menuRef = useRef(null); // Reference for detecting outside clicks
  const megaMenuRef = useRef(null); // Reference for detecting outside clicks
  const [menuOpen, setMenuOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [isMobile, setIsMobile] = useState(null);
  const [language, setLanguage] = useState("");
  const router = useRouter();
  const { pathname, asPath } = router;

  const [isDigitalHeader, setIsDigitalHeader] = useState(false);

  const targetLocale = router.locale === "en" ? "ar" : "en";

  useEffect(() => {
    // Update the state based on the current pathname
    setIsDigitalHeader(pathname === "/digital" || pathname === "ar/digital");
  }, [pathname]);

  const handleMenuToggle = () => {
    setMenuOpen(!menuOpen); // Toggle menu open state
  };

  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };

  const handleMenuItemClick = (index) => {
    setActiveMenuItem(activeMenuItem === index ? null : index);
  };

  const handleClickOutside = (event) => {
    if (menuRef.current && !menuRef.current.contains(event.target)) {
      setActiveMenuItem(null);
    }
  };

   // Handle click for "Inquire Now" button
  const handleInquireNowClick = (e) => {
    // Check if we're on the homepage
    if (pathname !== "/") {
      e.preventDefault();
      
      // Navigate to homepage
      router.push("/").then(() => {
        // After navigation is complete, scroll to #inquire section
        setTimeout(() => {
          const inquireSection = document.getElementById("inquire");
          if (inquireSection) {
            inquireSection.scrollIntoView({ behavior: "smooth" });
          }
        }, 500); // Small delay to ensure page has loaded
      });
    } else {
      // If already on homepage, just let the default anchor behavior work
      // The link will naturally scroll to #inquire
    }
    
    // Close any open menus
    setActiveMenuItem(null);
    setMenuOpen(false);
    menuHide();
  };

  useEffect(() => {
    function handleClickMeagaOutside(event) {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target)) {
        handleMenuItemClick();
      }
    }

    document.addEventListener("mousedown", handleClickMeagaOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickMeagaOutside);
    };
  }, []);

  useEffect(() => {
    // Add or remove the 'active' class on <body> and update <html> attributes
    if (menuOpen) {
      document.body.classList.add("active");
      document.documentElement.removeAttribute("data-scroll-orientation");
    } else {
      document.body.classList.remove("active");
    }

    // Cleanup function to ensure class and attribute are reverted if the component unmounts
    return () => {
      document.body.classList.remove("active");
      document.documentElement.setAttribute(
        "data-scroll-orientation",
        "initial"
      ); // Restore default value if needed
    };
  }, [menuOpen]);

  // ==========mobile view submenu===========

  const [submenu, setSubmenu] = useState(false);

  const subMenuArrow = () => {
    if (window.innerWidth <= 820) {
      setSubmenu((prev) => !prev);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      // Check if .main_head has scrolled out of view
      if (menuRef.current) {
        const headerOffset = menuRef.current.getBoundingClientRect().top;
        setIsSticky(window.scrollY > headerOffset); // Set sticky if page is scrolled past header
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    // Set initial value and add event listener for resizing
    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const [subLinkShow, setSublinkShow] = useState(null);

  const subLinkDisplay = (index) => {
    if (subLinkShow === index) {
      setSublinkShow(null);
    } else {
      setSublinkShow(index);
    }
  };

  // ==============Menu Hide on click=====

  const menuHide = () => {
    document.body.classList.remove("active");
  };

  const [headoption, setOptions] = useState(null);
  useEffect(() => {
    // console.log("Fetching options...");
    const fetchMyAcfOptions = async (locale) => {
      try {
        const footerPostsData = await getThemeoptions(locale);
        //console.log("Fetched options:", footerPostsData);
        setOptions(footerPostsData);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };
    fetchMyAcfOptions(router.locale);

    if (router && router.locale == "ar") {
      setLanguage("ar");

      document.body.classList.add("rtl", comon.rtl);
      document.documentElement.setAttribute("dir", "rtl");
      document.documentElement.setAttribute("lang", "ar");
    } else {
      setLanguage("en");
      document.body.classList.remove("rtl", comon.rtl);
      document.documentElement.setAttribute("dir", "ltl");
      document.documentElement.setAttribute("lang", "en");
    }
  }, [router]);

  if (!headoption) {
    return null;
  }

  return (
    <>
      {/* Marquee Section */}

      <HeaderMarquee />

      {/* Header Section */}
      <header
        className={`${header.header} ${isSticky ? header.sticky : ""}`}
        ref={menuRef}
      >
        <div
          className={`${comon.wrap} ${comon.d_flex} ${
            comon.align_items_center
          } ${
            isDigitalHeader ? `${header.digital_header} digital_header` : ""
          }`}
        >
          <div
            className={`${header.logo_block}`}
            onClick={() => setMenuOpen(false)}
          >
            <Link href={"/"} onClick={() => handleMenuItemClick()}>
              {isDigitalHeader ? (
                <Image
                  className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                  onLoad={handleLoad}
                  src={
                    headoption?.digital_header_logo?.url ||
                    "/images/tam_logo_digital.svg"
                  }
                  alt="logo"
                  width={200}
                  height={53}
                />
              ) : (
                <Image
                  className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                  onLoad={handleLoad}
                  src={headoption?.header_logo?.url || "/images/logo.png"}
                  alt="logo"
                  width={146}
                  height={53}
                />
              )}
            </Link>
          </div>

          <div
            className={`${header.center_block} ${header.nav_bar} ${comon.justify_center} nav_bar`}
          >
            <ul className={`${comon.justify_center} `} ref={megaMenuRef}>
              {headoption?.main_menu &&
                headoption?.main_menu.map((Mainmenu, mindex) =>
                  Mainmenu?.is_dropdown === "dropdown_no" ? (
                    <>
                      <li
                        className={`${
                          activeMenuItem === mindex + 1 ? header.active : ""
                        } `}
                        onClick={() => handleMenuItemClick(mindex + 1)}
                        key={mindex}
                      >
                        {Mainmenu?.main_nav && (
                          <Link
                            href={Mainmenu?.main_nav?.url}
                            target={Mainmenu?.main_nav?.target}
                            onClick={menuHide}
                            className={
                              pathname === `${Mainmenu?.main_nav?.url}`
                                ? header.active
                                : ""
                            }
                          >
                            {Mainmenu?.main_nav?.title &&
                              parse(Mainmenu?.main_nav?.title)}
                          </Link>
                        )}
                      </li>
                    </>
                  ) : (
                    <>
                      <li
                        className={`${
                          activeMenuItem === mindex + 1 ? header.active : ""
                        } ${comon.flex_wrap}`}
                        key={mindex}
                      >
                        {Mainmenu?.main_nav &&
                          Mainmenu?.is_dropdown === "dropdown_yes" && (
                            <div className={comon.space_btw}>
                              {" "}
                              {Mainmenu?.main_nav &&
                                Mainmenu?.is_dropdown === "dropdown_yes" && (
                                  <Link
                                    href={"#."}
                                    onClick={() => {
                                      handleMenuItemClick(mindex + 1);
                                      {
                                        isMobile ? subMenuArrow() : "";
                                      }
                                    }}
                                    className={`${
                                      pathname === Mainmenu?.main_nav?.url
                                        ? header.active
                                        : ""
                                    } ${header.mob_sub}`}
                                  >
                                    {Mainmenu?.main_nav?.title &&
                                      parse(Mainmenu?.main_nav?.title)}
                                  </Link>
                                )}
                              <span
                                className={`${
                                  pathname === Mainmenu?.main_nav?.url
                                    ? header.active
                                    : ""
                                } ${header.submenu} ${
                                  submenu ? header.toggleSubmenu : ""
                                }`}
                                onClick={() => {
                                  handleMenuItemClick(mindex + 1);
                                  subMenuArrow();
                                }}
                              >
                                <Image
                                  className={header.mob_arrow}
                                  src={"/images/mob_arw.png"}
                                  alt={""}
                                  width={11}
                                  height={7}
                                />
                              </span>
                              {Mainmenu?.products_sub_menus && (
                                <div className={header.megamenu}>
                                  <MegaMenu
                                    handleMenuItemClick={handleMenuItemClick}
                                    dropdown_head={
                                      Mainmenu?.is_dropdown_head_menu
                                    }
                                    title={Mainmenu?.head_menu_title}
                                    sub_title={Mainmenu?.head_menu_sub_title}
                                    menu_link={Mainmenu?.head_menu_link}
                                    productmenu={Mainmenu?.products_sub_menus}
                                    medaindex={mindex + 1}
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        {/* ==========submenu========= */}

                        {Array.isArray(Mainmenu?.products_sub_menus) &&
                          submenu && (
                            <>
                              <ul
                                className={`${header.header_submenu_sec} ${
                                  submenu == true
                                    ? header.header_submenu_sec_active
                                    : ""
                                }`}
                              >
                                {/* ==========submenu title ========= */}

                                {Mainmenu?.products_sub_menus &&
                                  Mainmenu?.products_sub_menus.map(
                                    (data, index) => (
                                      <li
                                        key={index}
                                        className={header.header_submenu_li}
                                      >
                                        {/* ==========submenu Link title ========= */}
                                        {data?.section_top_menu && (
                                          <Link
                                            href={data?.section_top_menu?.url}
                                            onClick={menuHide}
                                            target={
                                              data?.section_top_menu?.target
                                            }
                                          >
                                            {data?.section_top_menu?.title &&
                                              parse(
                                                data?.section_top_menu?.title
                                              )}
                                          </Link>
                                        )}
                                        {data.section_sub_menus && (
                                          <span
                                            className={`${header.submenu}  ${
                                              subLinkShow !== null &&
                                              subLinkShow == index
                                                ? header.toggleSubmenu
                                                : ""
                                            }`}
                                            onClick={() =>
                                              subLinkDisplay(index)
                                            }
                                          >
                                            <Image
                                              className={header.mob_arrow}
                                              src={"/images/mob_arw.png"}
                                              alt={""}
                                              width={11}
                                              height={7}
                                            />
                                          </span>
                                        )}

                                        {data.section_sub_menus && (
                                          <ul
                                            className={`${
                                              subLinkShow !== null &&
                                              subLinkShow == index
                                                ? header.active
                                                : ""
                                            }`}
                                          >
                                            {/* ==========submenu  multiple link ========= */}

                                            {subLinkShow !== null &&
                                            subLinkShow == index ? (
                                              <>
                                                {data.section_sub_menus &&
                                                  data.section_sub_menus.map(
                                                    (list, index) => (
                                                      <li key={index}>
                                                        {list.navsub_menus && (
                                                          <Link
                                                            href={
                                                              list.navsub_menus
                                                                ?.url
                                                            }
                                                            target={
                                                              list.navsub_menus
                                                                ?.target
                                                            }
                                                            onClick={menuHide}
                                                          >
                                                            {list.navsub_menus
                                                              ?.title &&
                                                              parse(
                                                                list
                                                                  .navsub_menus
                                                                  ?.title
                                                              )}
                                                          </Link>
                                                        )}
                                                      </li>
                                                    )
                                                  )}
                                              </>
                                            ) : (
                                              ""
                                            )}
                                          </ul>
                                        )}
                                      </li>
                                    )
                                  )}
                              </ul>
                              { Mainmenu?.is_dropdown_head_menu === 'head_yes' && (
                                <div  onClick={menuHide} className={header.advisory_mob} >
                                  <Buttion                                    
                                    text={Mainmenu?.head_menu_link?.title} // Button text
                                    href={Mainmenu?.head_menu_link?.url} // Navigation target for the button
                                    target={Mainmenu?.head_menu_link?.target}
                                    moduleClass={`${buttion.rounded} ${style.header_btn}`} // Custom button styling
                                    imageSrc="/images/buttion_arrow.svg" // Image for button
                                    onClick={handleInquireNowClick}
                                  />
                                </div>
                              )}
                            </>
                          )}
                      </li>
                    </>
                  )
                )}

              {headoption?.enquire_now && (
                <li className={header.enq_mob} onClick={() => {
                    handleMenuItemClick();
                    menuHide();
                  }}>
                  <Buttion
                    text={headoption?.enquire_now?.title}
                    href={headoption?.enquire_now?.url}
                    moduleClass={buttion.white}
                    imageSrc="/images/buttion_arrow_black02.svg"
                    onClick={handleInquireNowClick}
                  />
                </li>
              )}
            </ul>
          </div>
          <div
            className={`${header.right_block} ${comon.d_flex_wrap} right_block`}
          >
            <ul>
              {/* language  */}
              <li>
                <Link
                  href={asPath}
                  locale={targetLocale}
                  onClick={() => {
                    handleMenuItemClick();
                    menuHide();
                  }}
                  className={comon.lanaguage_switch}
                >
                  {headoption?.language_name &&
                    parse(headoption?.language_name)}
                </Link>
              </li>
              {/* language end  */}

              {headoption?.enquire_now && (
                <li
                  className={`${comon.mobile_hide} ${header.enquire}`}
                  onClick={() => {
                    handleMenuItemClick();
                    menuHide();
                  }}
                >
                  <Buttion
                    text={headoption?.enquire_now?.title}
                    href={headoption?.enquire_now?.url}
                    moduleClass={buttion.white}
                    imageSrc="/images/buttion_arrow_black02.svg"
                  />
                </li>
              )}
            </ul>

            <div
              className={`${header.togle_menu} togle_menu`}
              onClick={handleMenuToggle}
            >
              <span className={`${header.toggle_line} toggle_line`}></span>
              <span className={`${header.toggle_line} toggle_line`}></span>
              <span className={`${header.toggle_line} toggle_line`}></span>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
