@import "variable", "base";




// {/* -----------Company Announcement section----------- */}
.media_resources_card_section {
    width: 100%;
    background-color: #FFFFFF;

    .media_resources_card_container {
        width: 100%;

        h3 {

            color: #2a2656;
            // font-size: 40px;
            font-size: 2.5rem;
            line-height: 100%;


            @media #{$media-950} {

                font-size: 3.3rem;

            }

        }

        .media_resources_card_list {
            width: 100%;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1%;
            row-gap: 20px;
            list-style: none;

            @media #{$media-950} {
                gap: 20px;
            }

            .media_resources_card_list_img {
                width: calc(33.33% - 1%);
                position: relative;
                border: 1px solid #dddddd;
                border-radius: 15px;
                overflow: hidden;
                min-height: 260px;

                @media #{$media-950} {
                    width: 48.5%;
                }

                @media #{$media-767} {
                    width: 100%;
                }
                &:hover{
                    img:not(.media_resources_card_list_img_play_btn img){
                        scale: (1.1);
                    }
                }

                img {
                    height: 100%;
                    max-width: 100%;
                    display: block;
                    object-fit: cover;
                    transition: 0.3s all ease;
                    @media #{$media-950} {
                        width: 100%;
                    }
                }

                .media_resources_card_list_img_play_btn {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);

                    img {
                        width: 70px;
                        height: 70px;

                       

                        @media #{$media-1400} {
                            width: 60px;
                            height: 60px;
                        }

                        @media #{$media-950} {
                            width: 70px;
                            height: 70px;
                        }
                    }
                }

                .media_resources_card_list_data {

                    width: 100%;
                    left: 0%;
                    bottom: 20px;
                    box-sizing: border-box;

                    padding-left: 10%;
                    padding-right: 5%;
                    position: absolute;

                    @media #{$media-767} {
                        padding-left: 4%;
                        padding-right: 4%;
                        bottom: 20px;

                    }

                    h5 {
                        color: #000000;
                        font-size: 1.563rem;
                        line-height: 1.8rem;
                        font-weight: 400;
                        display: block;
                        font-family: var(--helveticaNeue);

                        @media #{$media-950} {
                            font-size: 2rem;
                            line-height: 20px;
                        }

                        @media #{$media-700} {
                            font-size: 2.563rem;
                            line-height: 20px;
                        }
                    }

                    span {
                        padding-top: 8px;
                        color: #000000;
                        font-size: 15px;
                        line-height: 100%;
                        font-weight: 400;
                        display: block;

                        
                        @media #{$media-767} {
                            font-size: 15px;
                        }
                    }

                    .action_text {
                        color: #5e45ff;
                        font-weight: 600;
                        margin-top: 8px;
                        font-size: 15px;
                    
                        @media #{$media-767} {
                            font-size: 15px;
                        }
                    }
                }
            }
        }

        .loading {
            font-family: var(--helveticaneueltarabicBold);
            ;
            color: #353a3f;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
        }

    }
}