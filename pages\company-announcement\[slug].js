import React, { useEffect, useState } from "react";
import comon from "@/styles/comon.module.scss";

import styles from "@/styles/anouncementDetails.module.scss";
import InnerBanner from "@/component/InnerBanner";
import Image from "next/image";
import Link from "next/link";
import TabSection from "@/component/Tab-section";

import { useRouter } from "next/router";
import Yoast from "@/component/yoast";
import {
  getCompanyPosts,getCompanySlugPosts,getCompanyAnnouncement,getIrMenuLinks
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

 
const NewsDetails = (props) => {

   const router = useRouter();
     
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
     useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
     }, []);
    
    
    const yoastData = props?.CompanySlugData[0]?.yoast_head_json;

  if (!props.CompanySlugData[0]) {
    return null;
  }
    
   

  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}
      
      
      
       {props &&
        props?.IrCompanyData &&
        props?.IrCompanyData?.acf &&
        props?.IrCompanyData?.acf?.banner_title &&
        (props?.IrCompanyData?.acf?.mob_banner_image ||
          props?.IrCompanyData?.acf?.banner_image ||
          props?.IrCompanyData?.acf?.breadcrumbs ||
          props?.IrCompanyData?.acf?.banner_viedo ||          
          props?.IrCompanyData?.acf?.banner_type) ? (
          <div className="banner_overlay">  
             
        <InnerBanner
          pagename={props?.IrCompanyData?.acf?.banner_title}
          breadcrumb1={props?.IrCompanyData?.acf?.active_breadcrumbs ==='yes' ? props?.IrCompanyData?.acf?.breadcrumbs : '' } 
          background={`${isMobile ? props?.IrCompanyData?.acf?.mob_banner_image?.url : props?.IrCompanyData?.acf?.banner_image?.url}`}          
          videoSrc={props?.IrCompanyData?.acf?.banner_viedo?.url}
          banner_type={props?.IrCompanyData?.acf?.banner_type}          

        />
            </div>
            ) : null
      }
      
    
      {props &&
        props?.IRMenuData &&
                props?.IRMenuData?.investors_relations_menu &&
                <TabSection tabs={props?.IRMenuData?.investors_relations_menu} />
      }
      
       {props &&
        props?.CompanySlugData[0] &&      
        props?.CompanySlugData[0]?.acf &&
        props?.CompanySlugData[0]?.acf?.page_content &&
        <section         
          className={`${comon.company_section}`}
        >
          <div
            className={`${comon.about_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100} ${styles.anouncement_details}`}
            data-aos="fade-up"
            data-aos-duration="1000"
          >

            <h1 >
              {props?.CompanySlugData[0]?.title && parse(props?.CompanySlugData[0]?.title?.rendered)}
            </h1>
            <div className={styles.anouncement_content}>
            {props?.CompanySlugData[0]?.acf?.page_content && parse(props?.CompanySlugData[0]?.acf?.page_content)}
            </div>
           
          </div>
        </section>
      }

      

 
      
      
    </div>
  );
};

export default NewsDetails;



export async function getStaticPaths() {

  const query = "";
  const CompanyPosts = await getCompanyPosts();
  // Generate paths for all taxonomies and their associated posts
  const paths = CompanyPosts.map((post) => ({
    params: { slug: post.slug },
  }));
  
  //console.log("Generated Paths:", paths);

  return {
    paths,
    fallback: "blocking", // Allow SSR for paths not generated at build time
  };
}


export async function getStaticProps({ params, locale }) { 
  const slug = params.slug;
   const CompanySlugArray = await getCompanySlugPosts(slug, locale);
    const IRMenuData = await getIrMenuLinks(locale);
    const IrCompanyData = await getCompanyAnnouncement(locale);

  return {
    props: {
      CompanySlugData: CompanySlugArray || [],
      IRMenuData: IRMenuData || null,
      IrCompanyData: IrCompanyData || null,
      
    },
    revalidate: 10,
  };
}

