"use client";
import React from "react";
import overview from "@/styles/Overview.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";

// import required modules
import { Navigation } from "swiper/modules";
import parse from "html-react-parser";

const OverviewTwoCol = ({
    head,
    paragraph,    
    equalCol
}) => {
    return (
        
         

            <section className={`${overview.overview_section_2} ${comon.pt_80} ${comon.pb_80} ${equalCol ? overview.equal_col :''}`}>
                <div  className={`${comon.wrap} ${comon.d_flex_wrap} over_wrap`}  >
                {head &&
                    <div className={`${overview.overview_left_cl} ${comon.overview_left_cl} overview_left_cl`} data-aos="fade-up"
                    data-aos-duration="1000">
                        <h3>{head && parse(head)}</h3>
                    </div>
                }
                {paragraph &&
                    <div className={`${overview.overview_right_cl} overview_right_cl`} data-aos="fade-up"
                    data-aos-duration="1000">
                        {paragraph && parse(paragraph)}
                    </div>
                }
                </div>
 
            </section>
        
    );
};

export default OverviewTwoCol;
