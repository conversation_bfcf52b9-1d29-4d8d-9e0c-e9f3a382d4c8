import React from "react";
import Link from "next/link"; // Import Link for navigation
import But<PERSON> from "@/component/buttion/Buttion"; // Import custom Button component
import comon from "@/styles/comon.module.scss"; // Import common styles
import news from "@/styles/news.module.scss"; // Import specific styles for inquire section
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import { useRouter } from "next/router"; // Import useRouter hook for routing
import Image from "next/image";
import parse from "html-react-parser";

import { Swiper, SwiperSlide } from "swiper/react";
// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import { Pagination, Navigation, Autoplay } from "swiper/modules";

const LatestNews = ({ insights_sub_text, insights_title, insights }) => {
  // Remove opacity class after image loads
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };
  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";
  const newsurl = router.locale === "ar" ? "/ar" : "";

  return (
    <>
      <div
        className={`${news.wrap} ${comon.pt_100} ${comon.pb_100} ${news.home_news_sec} ${comon.home_news_sec}`}
      >
        {insights_sub_text && (
          <h6
            data-aos="fade-up"
            data-aos-duration="1000"
            className={`${comon.sub_txt} ${comon.mb_10}`}
          >
            {insights_sub_text}
          </h6>
        )}
        {insights_title && (
          <h2
            data-aos="fade-up"
            data-aos-duration="1000"
            className={`${comon.h2} ${comon.text_white} ${news.sec_title}`}
          >
            {insights_title}
          </h2>
        )}
        <div
          className={`${comon.w_100} ${comon.pt_40} ${comon.p_relative}  swipper_overflow`}
        >
          <span
            data-aos="fade-up"
            data-aos-duration="1000"
            className={`${comon.custom_prev} ${comon.custom_btn}  custom_next_1`}
          >
            <Image
              loading="lazy"
              src="/images/next.svg"
              alt="Picture of the author"
              width={12}
              height={11}
              className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
              onLoad={handleLoad}
            />
          </span>
          <span
            data-aos="fade-up"
            data-aos-duration="1000"
            className={`${comon.custom_next} ${comon.custom_btn} custom_prev_1`}
          >
            <Image
              loading="lazy"
              src="/images/prev.svg"
              alt="Picture of the author"
              width={12}
              height={11}
              className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
              onLoad={handleLoad}
            />
          </span>

          <Swiper
            data-aos="fade-up"
            data-aos-duration="1000"
            navigation={{
              prevEl: ".custom_prev_1",
              nextEl: ".custom_next_1",
            }}
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
            }}
            slidesPerView={"auto"}
            loop={true}
            // autoplay={{
            //   delay: 2000,
            //   disableOnInteraction: true,
            // }}
            speed={1000}
            dir={language === "ar" ? "rtl" : "ltr"}
            key={language}
            breakpoints={{
              640: {
                spaceBetween: 5,
              },
              768: {
                spaceBetween: 20,
              },
              1024: {
                spaceBetween: 30,
              },
              1400: {
                spaceBetween: 45,
              },
            }}
            spaceBetween={10}
            pagination={{
              clickable: true,
            }}
            modules={[Navigation, Autoplay]}
            className="mySwiper"
          >
            {insights &&
              insights.map((data, rindex) => (
                <SwiperSlide className={`${news.slider_block}`} key={rindex}>
                  <div className={`${news.news_img_mian}  ${comon.rel}`}>
                    <div className={`${news.news_txt_block}`}>
                      <div className={`${news.news_txt_block}`}>
                        <div className={`${comon.w_100} ${comon.pt_30}`}>
                          {data?.acf.listing_tag && (
                            <h5>{parse(data?.acf?.listing_tag)}</h5>
                          )}
                          {data?.title && <p>{parse(data?.title?.rendered)}</p>}
                        </div>
                        <Link
                          className={`${comon.link_round} ${comon.ml_auto}`}
                          href={
                            data?.acf?.external_news_link
                              ? data?.acf?.external_news_link?.url
                              : `${newsurl}/insights/${data.catslug}/${data.slug}`
                          }
                          target={
                            data?.acf?.external_news_link
                              ? data?.acf?.external_news_link?.target
                              : "_self"
                          }
                        >
                          {/* {data?.acf?.external_news_link?.url}
                          {data.slug} */}
                          <Image
                            className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                            onLoad={handleLoad}
                            src={`/images/arrow_news.svg`}
                            alt="call"
                            width={11}
                            height={11}
                          />
                        </Link>
                      </div>
                    </div>
                    {data.acf.insights_page_image || data.acf.listing_image ? (
                      <Image
                        className={`${comon.img_mx_fluid} hm_news_img transition_opacity opacity-0`}
                        onLoad={handleLoad}
                        src={
                          data.acf.insights_page_image?.url ||
                          data.acf.listing_image?.url
                        }
                        alt="call"
                        width={338}
                        height={512}
                      />
                    ) : (
                      <Image
                        className={`${comon.img_mx_fluid} hm_news_img transition_opacity opacity-0`}
                        onLoad={handleLoad}
                        src={"/images/no-image.jpg"}
                        alt="call"
                        width={338}
                        height={512}
                      />
                    )}
                  </div>
                </SwiperSlide>
              ))}
          </Swiper>
        </div>
      </div>
    </>
  );
};

export default LatestNews;
