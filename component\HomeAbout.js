import React, { useState } from "react";
import Link from "next/link";
import But<PERSON> from "@/component/buttion/Buttion";
import buttion from "@/styles/buttion.module.scss";
import comon from "@/styles/comon.module.scss";
import about from "@/styles/homeAbout.module.scss";
import { useInView } from "react-intersection-observer"; // Import useInView
import Image from "next/image";
import CountUp from "react-countup";
import parse from "html-react-parser";

const HomeAbout = ({about_us_title,about_us_button,about_us_content, about_us_image,counter_details}) => {
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };

  // Initialize InView for each counter
  const [fundingRef, fundingInView] = useInView({ triggerOnce: false });
  const [awardsRef, awardsInView] = useInView({ triggerOnce: false });
  const [reachedRef, reachedInView] = useInView({ triggerOnce: false });
  const [engagedRef, engagedInView] = useInView({ triggerOnce: false });

  return (
    <>
      <section
      className={`${about.hm_abt}`}
        style={{
          background: "url(../images/history_new_bg.png) top left no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "right center",
        }}
      >
      
        <div
          className={`${comon.pt_100} ${comon.align_items_center} ${comon.d_flex} ${comon.home_about_sec} ${comon.pb_100}`}
          style={{ "--image": `url(${about_us_image?.url})` }}
          // style={{
          //   background: `url(${about_us_image?.url}) top center no-repeat`,
          //   backgroundSize: "contain",
          //   backgroundPosition: "left center",
          // }}
        >
          <div className={`${comon.wrap}`}>
            <div className={`${about.txt_row_01}`}>
              {about_us_title &&
                <h2
                  data-aos="fade-up"
                  data-aos-duration="1000"
                  className={`${comon.h2} ${comon.color_02}`}
                >
                  {parse(about_us_title)}
                </h2>
              }
              {about_us_content &&
                <div
                  className={`${comon.font_18} ${comon.mt_20} ${comon.mb_30}`}
                  data-aos="fade-up"
                  data-aos-duration="1000"
                >
                  {parse(about_us_content)}
                </div>
              }
              {about_us_button &&
              <Buttion
                aosType="fade-up"
                aosDuration={1500}
                text={about_us_button?.title}
                href={about_us_button?.url}
                target={about_us_button?.target}
                 moduleClass={buttion.strock}
                imageSrc="/images/buttion_arrow_white.svg"
              />
              }
            </div>
            {counter_details &&
              <div className={`${about.txt_row_02} ${comon.pt_150}`}>
                <ul className={`${about.home_counder} `}>
                  {counter_details && counter_details.map((counter, cIndex) => (
                    <>
                      <li data-aos="fade-up" data-aos-duration="1000" key={cIndex}>
                        <h3 ref={fundingRef}>
                          {/* Funding Counter */}
                          {fundingInView && (
                            <CountUp
                              start={0}
                              end={counter?.counter_number}
                              duration={2}
                              prefix={counter?.counter_symbol}
                              suffix={counter?.counter_suffix}
                              key={fundingInView ? "fundingInView" : "fundingOutView"}
                            />
                          )}
                          {counter?.counter_sub_text &&
                            <span className={about.small}> {counter?.counter_sub_text}</span>
                          }
                        </h3>
                        {counter?.counter_title && <span>{counter?.counter_title && parse(counter?.counter_title)}</span>}
                      </li>
                      {cIndex < counter_details.length - 1 && (
                        <li className={`${comon.diamond_hm}`}>
                          <Image
                            className={` transition_opacity opacity-0`}
                            onLoad={handleLoad}
                            src="/images/diamond_2.png"
                            alt="call"
                            width={20}
                            height={30}
                          />
                        </li>
                      )}
                    </>
                  ))
                  }
                
                </ul>
              </div>
            }
          </div>
        </div>
      </section>
    </>
  );
};

export default HomeAbout;

