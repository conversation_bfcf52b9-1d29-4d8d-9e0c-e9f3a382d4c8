@import "variable", "base";

.news_card_body {
    background-color: rgb(255, 255, 255);
    border-radius: 10px;
    // box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
    list-style: none;

    .news_card_img {
        width: 100%;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        overflow: hidden;

        img {
            display: block;
            object-fit: cover;
            height: 300px;
            width: 100%;
        }
    }

    .news_card_content {
        background: linear-gradient(180deg, rgba(186, 178, 234, 0.2) 0%, rgba(171, 163, 223, 1) 100%);
        padding: 1px;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .news_card_data {
        padding: 30px 20px 50px 20px;
        box-sizing: border-box;
        width: 100%;
        background-color: #fff;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;

        @media #{$media-767} {
            padding: 25px 15px 35px 15px;
        }

        >div {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            justify-content: space-between;

            p {

                background: #eeeeee;
                border-radius: 5px;
                padding: 2px 18px;
                color: #5b5b5b;
                font-size: 12px;
                line-height: 24px;
                letter-spacing: 0.31px;
                text-transform: uppercase;
                font-weight: 600;

            }
        }

        span {
            color: #474646;
            font-size: 15px;
            line-height: 24px;
            letter-spacing: 0.31px;
            font-weight: 400;
            font-family: var(--helveticaNeue);

            @media #{$media-1024} {
                font-size: 14px;
                line-height: 20px;
            }
        }

        h4 {
            color: #272727;
            font-size: 20px;
            line-height: 28px;
            font-weight: 400;
            padding-top: 15px;
            font-family: var(--helveticaNeue);
            display: block;

            @media #{$media-1024} {
                font-size: 17px;
                line-height: 25px;
            }

            @media #{$media-767} {
                font-size: 16px;
                line-height: 23px;
            }
        }
    }
}

.news_card_link {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;

    .news_card_content {
        flex-grow: 1;

        .news_card_data {
            height: 100%;
        }
    }

}