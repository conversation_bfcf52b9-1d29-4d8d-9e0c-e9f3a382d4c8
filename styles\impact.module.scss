@import "variable", "base";

.imact_section_home {
  width: 35.9%;
  position: relative;
  @media #{$media-767} {
    width: 100%;
  }
  >img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    @media #{$media-767} {
      display: none;
    }
  
  }
  

  .imact_section_home_container {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    @media #{$media-767} {
      display: none;
    }
  
    
  }


  

}

.impact_sub_title {
  p {
    color: $white;

    @media #{$media-700} {
      line-height: 2.3rem;
      font-size: 1.8rem;
    }
  }
}


.imact_section_left {
  width: 32.05%;

  @media #{$media-700} {
    width: 100%;
  }
}

.imact_section_right {
  width: 32.05%;

  @media #{$media-700} {
    width: 100%;
  }

}

.imact_txt_block_01 {
  padding: 4% 10%;
  position: absolute;
  left: 0;
  width: 100%;
  cursor: pointer;

  h3 {
    font-size: 1.563rem;
    line-height: 1.863rem;
    color: $white;
    font-weight: 400;
    text-transform: capitalize;
    width: 80%;

    @media #{$media-950} {
      font-size: 1.5rem;
      font-family: var(--helveticaNeue);
    }

    @media #{$media-700} {
      font-size: 2rem;
      padding-inline-end: 10px;
    }

  }


  .left_line {
    content: "";
    display: block;
    width: 0.08vw;
    left: 0;
    top: 25px;
    bottom: 25px;
    position: absolute;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#9574dd+0,7e62bb+100 */
    background: linear-gradient(to bottom, #9574dd 0%, #7e62bb 100%);

    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    @media #{$media-820} {
      display: none;
    }

  }

  .right_line {
    content: "";
    display: block;
    width: 0.08vw;
    right: 0;
    top: 25px;
    bottom: 25px;
    position: absolute;
    background: linear-gradient(to bottom, #9574dd 0%, #7e62bb 100%);

    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    @media #{$media-820} {
      display: none;
    }

  }



  &.position_01 {
    top: 9%;
  }

  &.position_03 {
    top: 11%;

    @media #{$media-1440} {
      top: 12%;
    }
  }

  &.position_02 {
    top: 69%;

    @media #{$media-1440} {
      top: 70%;
    }
  }

  &.position_05 {
    top:calc(100% + 70px);
    left: 50%;
    transform: translate(-50%);
    width: 95%;
    @media #{$media-1600} {
      top:calc(100% + 60px);
    }
    @media #{$media-1400} {
      top:calc(100% + 55px);
    }
    @media #{$media-1024} {
      top:calc(100% + 50px);
    }
    @media #{$media-700} {
      transform: unset;
      width: 100%;
    }
  
    &::before{
      content: '';
      position: absolute;
      bottom: 100%;
      left: 50%;
      width: 1px;
      height: 70px;
      background-color: #4838b1;
      z-index: 10;
      @media #{$media-1600} {
        height: 60px;
      }
      @media #{$media-1400} {
        height: 55px;
      }
      @media #{$media-1024} {
        height: 50px;
      }
      @media #{$media-700} {
        display: none;
      }
    
    }

  }



  @media #{$media-820} {
    border: solid 1px #7e62bb;
    border-radius: 10px;
  }

  @media #{$media-700} {
    position: unset;
    margin-bottom: 15px;
    padding: 4% 6%;
  }

  &::after {
    content: "";
    border-radius: 15px;
    border: 1px solid transparent;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(1deg, rgb(171, 134, 255) 0%, rgb(103, 80, 153) 100%);
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;

    @media #{$media-995} {
      border-radius: 9px;
    }

    @media #{$media-700} {
      display: none !important;
    }
  }

  &:nth-child(2) {
    &:after {
      background: linear-gradient(179deg, rgb(94, 69, 255) -18%, rgb(168, 131, 255) 100%)
    }
  }

}

// .imact_section_right{
//   .imact_txt_block_01{
//   &:nth-child(1){
//     &:after{
//       background:linear-gradient(
//         270deg,
//         rgba(171, 134, 255, 1) 0%,
//         rgba(94, 69, 255, 1) 100%
//       );
//     }
//   }
//   &:nth-child(2){
//     &:after{
//       background:linear-gradient(
//         270deg,
//         rgba(171, 134, 255, 1) 0%,
//         rgba(94, 69, 255, 1) 100%
//       );
//     }
//   }
// }
// }
.border_top {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;

  img {
    width: 100%;
    height: auto;
    display: block;
  }

  @media #{$media-1024} {
    top: 3px;
  }

  @media #{$media-820} {
    display: none;
  }
}

.border_bot {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;

  img {
    width: 100%;
    height: auto;
    display: block;
  }

  @media #{$media-1024} {
    bottom: 3px;
  }

  @media #{$media-820} {
    display: none;
  }
}

.txt_block {
  position: relative;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {}

  p {
    color: $white;
    opacity: 0.5;
    font-size: 1.2rem;
    line-height: 1.5rem;
    margin-top: 15px;
    font-family: var(--segoe-ui);

    @media #{$media-1024} {
      margin-top: 3px;
    }

    @media #{$media-767} {
      font-size: 1.5rem;
      line-height: 2rem;
      padding-inline-end: 20px;
      margin-top: 10px;
    }

    @media #{$media-500} {
      font-size: 1.9rem;
      line-height: 2.4rem;
    }
  }
}


// .imact_txt_block_01 {
//   position: relative;
// }

.hover_txt_block {
  display: none;
}

// .imact_txt_block_01:hover .hover_txt_block {
//   display: block;
// }


.activeBlock {
  .hover_txt_block {
    display: block;
  }

  .impact_arrow {
    background: #fcb136;
    border-color: #fcb136;

    img {
      transform: rotate(90deg);

      @media #{$media-767} {
        height: 20px;
        width: 6px;
      }
    }
  }

  .txt_block {
    display: block;
  }

  .impact_arrow {
    position: absolute;
  }



  @media #{$media-820} {
    background: #242240;
    border-radius: 10px;
    z-index: 800;
  }


  @media #{$media-700} {
    background: transparent;
  }


}

.txt_block_01,
.txt_block_04 {
  h3 {
    max-width: 35%;

    @media #{$media-700} {
      width: 100%;
      max-width: unset;
    }
  }
}


.impact_link {
  color: $white;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10px;
  font-size: 15px;

  img {
    margin-inline-start: 10px;
    margin-top: 5px;

    @media #{$media-700} {
      width: 12px;
      margin-inline-start: 6px;
      margin-top: 5px;
    }
  }

  @media #{$media-1024} {
    margin-top: 0px;
  }

  @media #{$media-767} {
    margin-top: 10px;
    font-size: 13px;
  }
}

.impact_arrow {
  // position: absolute; 
  cursor: pointer;
  inset-inline-end: 0px;
  top: 13px;
  width: 24px;
  height: 24px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  @media #{$media-1440} {
    top: 7px;
  }

  @media #{$media-820} {
    inset-inline-end: -10px;
    top: 5px;

  }

  @media #{$media-700} {
    inset-inline-end: 0;
    top: 0;
  }

  img {
    @media #{$media-700} {
      width: 25px;
      height: 11px;
    }
  }

}

.shadow_block {
  position: absolute;
  bottom: -20%;
  left: 0;
  width: 100%;
  img{
    width: 100%;
    height: auto;
    display: block;
  }
  @media #{$media-767} {
    display: none;
  }

}

.video {
  // width: 451px;
  // height: 447px;
  width: 89%;
  height: 98%;
  border-radius: 50%;
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
  object-fit: cover;
  top: 5px;
  mix-blend-mode: lighten;

  @media #{$media-950} {
    width: 89%;
    height: 97%;
    top: 3px;
  }

  @media #{$media-767} {
    display: none;
  }
}