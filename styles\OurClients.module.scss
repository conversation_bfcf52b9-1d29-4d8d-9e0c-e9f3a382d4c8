@import "variable", "base";


.clients_logo_section {
    width: 100%;
    background-color: rgb(255, 255, 255);

    .clients_logo_container {

        width: 100%;

        h3 {
            color: #2a2656;
            // font-size: 55px;
            font-size: 3.4375rem;
            font-weight: 400;
        }

        p {
            color: #404040;
            font-size: 1.25rem;
            line-height: 1.938rem;
            font-weight: 400;

            @media #{$media-767} {
                font-size: 1.8rem;
                line-height: 2.6rem;
            }

            @media #{$media-500} {
                font-size: 2rem;
                line-height: 2.6rem;
            }
        }

        .clients_logo_ul_list {
            display: flex;
            flex-wrap: wrap;
            gap: 1%;
            row-gap: 15px;

            li {
                width: 19%;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                object-fit: cover;
                border: 1px solid #ABA3DF;
                border-radius: 5px;
                padding: 25px 1%;
                box-sizing: border-box;

                .clients_logo_img {
                    max-width: 100%;
                    position: relative;
                    z-index: 200;
                    min-width: 50%;

                    img {
                        width: 100%;
                        display: block;
                        object-fit: contain;
                    }

                }

                @media #{$media-1024} {
                    width: 24%;
                    padding: 20px 1%;

                }

                @media #{$media-1024} {
                    width: 32%;
                    padding: 20px 1%;

                }

                @media #{$media-500} {
                    width: 49%;
                }


                &::after {
                    content: "";
                    display: block;
                    z-index: 100;
                    position: absolute;
                    left: -1px;
                    top: -1px;
                    width: calc(100% + 2px);
                    height: calc(100% + 2px);
                    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#ffffff+0,ffffff+100&0.68+0,0+100 */
                    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.68) 0%, rgba(255, 255, 255, 0) 100%);
                    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */

                }
            }

            @media #{$media-500} {
                row-gap: 8px;
            }
        }

    }
}