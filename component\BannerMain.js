import React, { useEffect } from "react";
import { gsap } from "gsap";
import banner from "@/styles/banner.module.scss";
import Link from "next/link";
import comon from "@/styles/comon.module.scss";
import Buttion from "@/component/buttion/Buttion";
import buttion from "@/styles/buttion.module.scss";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, EffectFade, Autoplay, Navigation } from "swiper/modules";
import "swiper/css";
import parse from "html-react-parser";

const BannerMain = ({ bannerSlide }) => {
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };

  useEffect(() => {
    // Any additional GSAP animations can go here
  }, []);

  return (
    <>
      <section className={`${banner.banner_main}`}>
        <Swiper className={`${banner.banner_main} mySwiper`}>
          {bannerSlide &&
            bannerSlide.map((slider, bIndex) => (
              <SwiperSlide
                className={`${banner.banner_slide_main}`}
                key={bIndex}
              >
                {slider?.banner_type === "video" &&
                (slider?.banner_video?.url || slider?.mb_banner_video?.url) ? (
                  <>
                    <video
                      className={`${banner.main_banner_video} ${banner.main_banner_normal}`}
                      src={slider?.banner_video?.url}
                      autoPlay
                      loop
                      muted
                      playsInline
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    {/* <video
                      className={`${banner.main_banner_video} ${banner.main_banner_mob} `}
                      src={slider?.mb_banner_video?.url}
                      autoPlay
                      loop
                      muted
                      playsInline
                      style={{ objectFit: "cover", width: "100%", height: "100%" }}
                    /> */}
                  </>
                ) : (
                  <div
                    className={`${banner.banner_image}`}
                    style={{
                      backgroundImage: `url(${slider?.banner_image?.url})`,
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                    }}
                  > 
                  <div className={banner.banner_shape}>
                  <Image  src="/images/tam_banner_shape_2.png" width={1600} height={229} alt="shape"/>
                  </div>
                  </div>
                )}
                {slider?.banner_type === "video" &&
                (slider?.banner_video?.url || slider?.mb_banner_video?.url) ? (
                  <div className={`${comon.wrap} ${banner.baner_txt_wrap}`}>
                    <div
                      className={`${banner.banner_txt_block}  ${comon.banner_txt_block}`}
                    >
                      {slider?.banner_text && (
                        <h1
                          data-aos="fade-up"
                          data-aos-duration="1000"
                          className={`${comon.h1} ${comon.mb_50}`}
                        >
                          {slider?.banner_text && parse(slider?.banner_text)}
                        </h1>
                      )}
                      {slider?.banner_condent && (
                        <p data-aos="fade-up" data-aos-duration="1000">
                          {slider?.banner_condent &&
                            parse(slider?.banner_condent)}
                        </p>
                      )}
                      {slider?.learn_more_button && (
                        <Buttion
                          aosType="fade-up"
                          aosDuration={1500}
                          text={slider?.learn_more_button?.title}
                          href={slider?.learn_more_button?.url}
                          target={slider?.learn_more_button?.target}
                          moduleClass={buttion.strock}
                          imageSrc="/images/buttion_arrow_white.svg"
                        />
                      )}
                    </div>
                  </div>
                ) : (
                  <div className={`${comon.wrap} ${banner.baner_txt_wrap} ${banner.img_baner_txt_wrap}`}>
                    <div
                      className={`${banner.banner_txt_block} ${banner.banner_img_txt_block} ${comon.banner_txt_block} ${comon.banner_img_txt_block}`}
                    >
                      {slider?.banner_text && (
                        <h1
                          data-aos="fade-up"
                          data-aos-duration="1000"
                          className={`${comon.h1}`}
                        >
                          {slider?.banner_text && parse(slider?.banner_text)}
                        </h1>
                      )}
                      {slider?.banner_condent && (
                        <p data-aos="fade-up" data-aos-duration="1000">
                          {slider?.banner_condent &&
                            parse(slider?.banner_condent)}
                        </p>
                      )}
                      {slider?.learn_more_button && (
                        <Buttion
                          aosType="fade-up"
                          aosDuration={1500}
                          text={slider?.learn_more_button?.title}
                          href={slider?.learn_more_button?.url}
                          target={slider?.learn_more_button?.target}
                          moduleClass={buttion.strock}
                          imageSrc="/images/buttion_arrow_white.svg"
                        />
                      )}
                    </div>
                  </div>
                )}
              </SwiperSlide>
            ))}
        </Swiper>

        {/* <Link
          href="https://wa.me/966556007482"
          target="_blank"
          className={banner.help}
        >
          <Image src="/images/help.png" width={120} height={19} alt="image" />
        </Link> */}
      </section>
    </>
  );
};

export default BannerMain;
