import InnerBanner from "@/component/InnerBanner";
import { useState, useEffect, useRef } from "react";
import style from "@/styles/Publications.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import NewsCard from "@/component/NewsCenterCard";
import TabSection from "@/component/Tab-section";
import InquireNow from "@/component/InquireNow";
import TabSectionInsights from "@/component/TabSectionInsights";
import filterstyle from "@/styles/innerBanner.module.scss";
import { useRouter } from "next/router"


import Yoast from "@/component/yoast";
import {
  getPublicationspage,
  getIrMenuLinks,
  getInsightstaxonamyList,
  getInsightsPosts,
  getindustriesCatList,
  getInsightsproject,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Publications = (props) => {  

  const router = useRouter();
  const { slug } = router.query; // Get the slug from the query parameters
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  const [visiblePosts, setVisiblePosts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const loaderRef = useRef(null); 
  const postsPerPage = 12;
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  useEffect(() => {
    if (props.filteredPosts?.length) {
      // Load initial posts
      setVisiblePosts(props.filteredPosts.slice(0, postsPerPage));
    }
  }, [props.filteredPosts]);

  useEffect(() => {
  if (!loading) return;

  const loadMorePosts = () => {
    if (!props.filteredPosts) return;

    const nextPosts = props.filteredPosts.slice(
      visiblePosts.length,
      visiblePosts.length + postsPerPage
    );

    if (nextPosts.length === 0) {
      setHasMore(false);
    } else {
      setVisiblePosts((prev) => [...prev, ...nextPosts]);
    }
    
    setLoading(false);
  };

  loadMorePosts();
}, [loading]);

  useEffect(() => {
  if (!loaderRef.current || !hasMore || loading) return;

  const observer = new IntersectionObserver(
    ([entry]) => {
      if (entry.isIntersecting) {
        setLoading(true);
      }
    },
    { threshold: 1.0 }
  );

  observer.observe(loaderRef.current);
  
  return () => observer.disconnect();
}, [visiblePosts, hasMore, loading]);
  

  // filter code start  
  useEffect(() => {
    function handleOutsideClick(event) {
      const targetElement = document.getElementById("insights_tabs");
      if (targetElement && !targetElement.contains(event.target)) {
        setTabactive(null);
      }
    }
    document.addEventListener("click", handleOutsideClick);
    return () => document.removeEventListener("click", handleOutsideClick);
  }, []);

 const [tabActive, setTabactive] = useState(null);
  const [selectedTab, setSelectedTab] = useState(null);

  const [filteredPosts, setFilteredPosts] = useState(
    props?.publicationsPostsData || []
  );

  // console.log('testingdata', filteredPosts)

  const filterPostsByCategory = (taxonomyId) => {
    const filtered = props?.filteredPosts.filter((post) =>
      post.industries_category && post.industries_category.includes(taxonomyId)
    );
    //console.log('testingdata', filteredPosts)
    setFilteredPosts(filtered);
    setVisiblePosts(filtered.slice(0, postsPerPage)); // Reset visible posts
    setCurrentPage(1); // Reset pagination
    setHasMore(filtered.length > postsPerPage); // Check if more posts are available
  };

  const tabSelect = (index) => setTabactive(tabActive === index ? null : index);
  const tabActiveSelect = (taxonomyId, index) => {
    setSelectedTab(index);
    filterPostsByCategory(taxonomyId);
  };
  // filter code end
  
  const yoastData = props?.PublicationsData?.yoast_head_json;

  if (!props.PublicationsData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

     {yoastData && <Yoast meta={yoastData} />}

      {props &&
                props?.PublicationsData &&
                props?.PublicationsData?.acf &&
                props?.PublicationsData?.acf?.banner_title &&
                (props?.PublicationsData?.acf?.mob_banner_image ||
                props?.PublicationsData?.acf?.banner_image ||
                props?.PublicationsData?.acf?.breadcrumbs ||
                props?.PublicationsData?.acf?.banner_viedo ||                
                props?.PublicationsData?.acf?.banner_type) ? (
                <InnerBanner
                pagename={props?.PublicationsData?.acf?.banner_title}
                breadcrumb1={props?.PublicationsData?.acf?.active_breadcrumbs ==='yes' ? props?.PublicationsData?.acf?.breadcrumbs : '' }
                background={`${isMobile ? props?.PublicationsData?.acf?.mob_banner_image?.url : props?.PublicationsData?.acf?.banner_image?.url}`}          
                videoSrc={props?.PublicationsData?.acf?.banner_viedo?.url}
                banner_type={props?.PublicationsData?.acf?.banner_type}              

                />
            ) : null
      }

      {/* =========Tab Section====== */}
   
      {props &&
        props?.IRMenuData &&
                props?.IRMenuData?.insights_menu &&
        <TabSectionInsights
        tabs={props?.IRMenuData?.insights_menu}
        slug={props?.slug} />
            }
      
     
      
      {/* filter code */}
      
     {(props?.slug === "projects-case-studies" || props?.slug === "projects-case-studies-ar") && (
        <section className={`${comon.pt_60}`}>
          <div className={`${comon.wrap} ${filterstyle.filtertabs}`} data-aos="fade-up"
            data-aos-duration="1000">
            <ul
              className={filterstyle.inner_banner_insight_ul}
              id="insights_tabs"
              style={{ display: "flex" }}
            >
              {/* Industry Section */}
              {props?.InsightsData?.acf?.industry_text && (
                <li
                  className={`${filterstyle.inner_banner_insight_li} ${tabActive === "industry" ? filterstyle.active : ""
                    }`}
                  onClick={() => tabSelect("industry")}
                >
                  <span>
                    {props?.InsightsData?.acf?.industry_text &&
                      parse(props?.InsightsData?.acf?.industry_text)}
                  </span>
                  <div
                    className={`${filterstyle.inner_banner_insight_ul_icon} ${tabActive === "industry" ? filterstyle.toggle : ""
                      }`}
                  >
                    <Image
                      src={"/images/purple_dwn_arrow.svg"}
                      height={20}
                      width={15}
                      alt=""
                    />
                  </div>
                  {tabActive === "industry" && (
                    <ul className={filterstyle.inner_banner_insight_inner_sec}>
                      {props?.IndustriesCategory &&
                        props?.IndustriesCategory.map((item, index) => (
                          <li
                            key={index}
                            className={`${selectedTab === index ? filterstyle.active : ""
                              }`}
                          >
                            <Link
                              href="#."
                              onClick={() => tabActiveSelect(item.id, index)}
                              test={item.id}
                            >
                              {item.name && parse(item.name)}
                            </Link>
                          </li>
                        ))}
                    </ul>
                  )}
                </li>
              )}
              {/* Content Type Section */}
              {props?.InsightsData?.acf?.content_type_text &&
                props?.InsightsData?.acf?.content_type_link && (
                  <li
                    className={`${filterstyle.inner_banner_insight_li} ${tabActive === "contentType" ? filterstyle.active : ""
                      }`}
                    onClick={() => tabSelect("contentType")}
                  >
                    <span>
                      {props?.InsightsData?.acf?.content_type_text &&
                        parse(props?.InsightsData?.acf?.content_type_text)}
                    </span>
                    <div
                      className={`${filterstyle.inner_banner_insight_ul_icon} ${tabActive === "contentType" ? filterstyle.toggle : ""
                        }`}
                    >
                      <Image
                        src={"/images/purple_dwn_arrow.svg"}
                        height={20}
                        width={15}
                        alt=""
                      />
                    </div>
                    {tabActive === "contentType" && (
                      <ul className={filterstyle.inner_banner_insight_inner_sec}>
                        {props?.InsightsData?.acf?.content_type_link &&
                          props?.InsightsData?.acf?.content_type_link.map(
                            (item, index) => (
                              <li
                                key={index}
                                className={`${selectedTab === index ? filterstyle.active : ""
                                  }`}
                              >
                                <Link href={item?.menu_link?.url}>
                                  {item?.menu_link?.title}
                                </Link>
                              </li>
                            )
                          )}
                      </ul>
                    )}
                  </li>
                )}
            </ul>
          </div>
        </section>
       )}
      {/* fiter code end  */}
      
      {/* ==========================News Section=============================== */}

       {visiblePosts.length > 0 ? (
        <section className={`${style.publication_news_section} ${comon.pt_100}`}>
          {/* <div className={`${comon.wrap}`}>
            <h5>Showing {visiblePosts.length} of {props.filteredPosts.length}</h5>
          </div> */}
          <ul
            className={`${style.publication_news_container} ${comon.wrap} ${comon.pt_30}  ${comon.pb_100}`}
          >
            <NewsCard
              cardData={visiblePosts}
              // cardData={visiblePosts || filteredPosts}
              className={`${style.publication_news_card_body}`}
              data-aos="fade-up"
              data-aos-duration="1000"
              slug={props?.slug}
            />
          </ul>
          {hasMore && (
            <p
              ref={loaderRef}
              className={`${style.loading} ${comon.pt_60}`}
            >
              {/* Loading more posts... */}
            </p>
          )}
        </section>
      ) : (
        <p className={`${style.no_posts} ${comon.pt_60}`}>
          No posts available.
        </p>
      )}

     <InquireNow formtitle={props?.PublicationsData?.acf?.banner_title} />
    </div>
  );
};

export default Publications;


export async function getStaticPaths(locale) {
 
  const ProductlistData = await getInsightstaxonamyList(locale.locales);

  const paths = ProductlistData.map((product) => ({
    params: { insightPost: product.slug },
  }));
  //console.log('fdshfsd', locale);
  return {
    paths: paths,
    fallback: "blocking", // false or 'blocking'
  };
}


export const getStaticProps = async ({ params, locale }) => {
  
  const slug = params.insightPost;
  // console.log('slug', locale)
  const query = "";
  //const Insightstaxonamy = await getInsightstaxonamyList(locale);
  const InsightsPostData = await getInsightsPosts(locale, query);
  const IRMenuData = await getIrMenuLinks(locale);   
  const Insightstaxonamy = await getInsightstaxonamyList(slug, locale);  
  const taxonomyId = Insightstaxonamy[0]?.id; // Assuming `id` is directly accessible  
    if (!taxonomyId) {
      return {
        notFound: true, // Optionally return a 404 if taxonomy ID is not found
      };
    }    
  // Filter posts based on the taxonomy ID and advisory category
  const filteredPosts = InsightsPostData?.filter(
    (post) =>
      Array.isArray(post?.categories) && post?.categories.includes(taxonomyId)
  ); 
  // console.log('testiyuyt', locale)
  // Filter section
  const InsightsData = await getInsightsproject(locale);
  const industriesCategory = await getindustriesCatList(locale);
  // Filter out items with count: 0
  const filteredIndustriesCategory = industriesCategory.filter(
    (category) => category.count > 0
  );

  return {
    props: {
      InsightsData: InsightsData || null,
      PublicationsData: Insightstaxonamy[0] || null,
      filteredPosts: filteredPosts || [],
      IRMenuData: IRMenuData || null,
      slug: slug || null, 
      IndustriesCategory: filteredIndustriesCategory || [],

    },
    revalidate: 10,
  };
};