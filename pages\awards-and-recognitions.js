import React, { useState, useEffect, useRef } from "react";
import aboutus from "@/styles/aboutus.module.scss";
import awardscss from "@/styles/awards.module.scss";
import comon from "@/styles/comon.module.scss";
import InquireNow from "@/component/InquireNow";
import Image from "next/image";
import style from "@/styles/MediaResources.module.scss";
import Overview from "@/component/Overview";
import InnerBanner from "@/component/InnerBanner";
import Link from "next/link";

import { useRouter } from "next/router";




import Yoast from "@/component/yoast";
import {
  getAwards,
  getAwardsPosts,  
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Index = (props) => { 
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  const [isClient, setIsClient] = useState(false); 
  const [visiblePosts, setVisiblePosts] = useState(16); // Number of visible posts
  const [isLoading, setIsLoading] = useState(false); // Track loading state
  const loaderRef = useRef(null); // Reference for the loader element
  const router = useRouter();
 

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);
  useEffect(() => {
    setIsClient(true); // Ensures Swiper only loads on the client
  }, []);


  // Implement infinite scroll using Intersection Observer
  useEffect(() => {
    if (!isClient || !props.AwardsArray) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !isLoading && visiblePosts < props.AwardsArray.length) {
          setIsLoading(true);
          // Simulate network delay
          setTimeout(() => {
            setVisiblePosts((prev) => prev + 8);
            setIsLoading(false);
          }, 500);
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
      }
    );

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [isClient, props.AwardsArray, visiblePosts, isLoading]);


  if (!isClient) return null; // Avoid rendering on the server

    const handleLoadMore = () => {
    setVisiblePosts((prev) => prev + 8);
  };

  // ============our team============

  const yoastData = props?.aboutData?.yoast_head_json;

  if (!props.aboutData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.aboutData &&
      props?.aboutData?.acf &&
      props?.aboutData?.acf?.banner_title &&
      (props?.aboutData?.acf?.mob_banner_image ||
        props?.aboutData?.acf?.banner_image ||
        props?.aboutData?.acf?.banner_viedo ||
        props?.aboutData?.acf?.breadcrumbs ||
        props?.aboutData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.aboutData?.acf?.banner_title}
          breadcrumb1={
            props?.aboutData?.acf?.active_breadcrumbs === "yes"
              ? props?.aboutData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.aboutData?.acf?.mob_banner_image?.url
              : props?.aboutData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.aboutData?.acf?.banner_viedo?.url}
          banner_type={props?.aboutData?.acf?.banner_type}
        />
      ) : null}
      
      {/* -----------Awards section----------- */}

      {props && props.AwardsArray && (
        <section section className={`${awardscss.about_recognition_section} ${awardscss.about_awards_section}`}>
          <div
            className={`${awardscss.about_recognition_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100} `}
          >
        
            <div
              className={`${awardscss.about_recognition_swiper_sec}  ${comon.mt_50}`}
            >
             
                {props.AwardsArray &&
                 props?.AwardsArray.slice(0, visiblePosts).map((awards, aIndex) => (
                    <div
                      className={`${awardscss.awards_slider}`}
                      key={aIndex}
                       data-aos="fade-up"
                  data-aos-duration="1000"
                    >
                      <div
                        className={`${awardscss.about_recognition_swiper_card}  `}
                      >
                        {awards?.acf?.awards_image && (
                          <div
                            className={`${awardscss.about_recognition_swiper_card_logo} `}
                          >
                            <Image
                              src={awards?.acf?.awards_image?.url}
                              alt=""
                              width="248"
                              height="154"
                            />
                          </div>
                        )}

                        <div
                          className={`${awardscss.about_recognition_swiper_card_title} ${comon.about_recognition_swiper_card_title} `}
                        >
                          {awards?.acf?.awards_year && (
                            <h5>
                              {awards?.acf?.awards_year &&
                                parse(awards?.acf?.awards_year)}
                            </h5>
                          )}
                        </div>
                        {awards?.title && (
                          <p className={`${comon.pt_10} `}>
                            {awards?.title && parse(awards?.title?.rendered)}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
              
               {/* Load More Button */}          
              {visiblePosts < props?.AwardsArray.length && (
                <div 
                  ref={loaderRef} 
                  className={`${awardscss.loading} ${awardscss.pt_60}`}
                >
                  {isLoading && (
                    <div className={awardscss.loader}>
                      {router.locale === "ar" ? "جاري التحميل..." : "Loading..."}
                    </div>
                  )}
                </div>
              )}
            </div>

            
          </div>
        </section>
      )}

      <InquireNow formtitle={props?.aboutData?.title?.rendered} />
    </div>
  );
};

export default Index;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const AwardsDatapage = await getAwards(locale);
  const query = "";
  const AwardsData = await getAwardsPosts(locale, query);

  //  console.log('testingdata', aboutData)

  return {
    props: {
      aboutData: AwardsDatapage || null,      
      AwardsArray: AwardsData || [],
    },
    revalidate: 10,
  };
};
