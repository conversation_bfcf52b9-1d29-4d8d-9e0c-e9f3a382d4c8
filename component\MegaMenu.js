import React, { useState } from "react";
import style from "@/styles/MegaMenu.module.scss";
import Buttion from "@/component/buttion/Buttion"; // Fixed Button import
import comon from "@/styles/comon.module.scss";
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import Link from "next/link";
import parse from "html-react-parser";

  const menuHide = () => {
    document.body.classList.remove("active");
  };

const Index = ({ handleMenuItemClick,dropdown_head,title,sub_title,menu_link,productmenu,medaindex }) => {
  return (
    <div>
      
      {/* Parent div wrapping the entire JSX */}
      <div
        className={`${style.megamenu_main_body} ${comon.pt_50} ${comon.pb_50}`}
      >
        <div className={`${comon.wrap}`}>
          {dropdown_head === "head_yes" && (title || sub_title || menu_link) ? (
            <div className={`${style.megamenu_advisary_sec}`}>
              <div onClick={() => menuHide()}> 
                {title &&
                  <h3>{title && parse(title)}</h3>
                }
                {sub_title &&
                  <p className={``}>{sub_title && parse(sub_title)}</p>
                }
              </div>
              {menu_link &&
                <span onClick={() => handleMenuItemClick(medaindex)}>
                  <Buttion
                    text={menu_link?.title} // Button text
                    href={menu_link?.url} // Navigation target for the button
                    target={menu_link?.target}
                    moduleClass={`${buttion.rounded} ${style.header_btn}`} // Custom button styling
                    imageSrc="/images/buttion_arrow.svg" // Image for button
                  />
                </span>
              }
            </div>
          ) : null}
          {productmenu &&
            <ul className={`${style.megamenu_ul_body}`}>
              {productmenu && productmenu.map((subMenuList, index) => (
                <li className={`${style.megamenu_li_body}`} key={index}>
                  {subMenuList.section_top_menu &&
                    <Link href={subMenuList.section_top_menu?.url}
                      target={subMenuList.section_top_menu?.target}
                      className={`${style.menu_title} ${comon.menu_title}`} onClick={() => handleMenuItemClick(medaindex)}>
                      {subMenuList.section_top_menu?.title && parse(subMenuList.section_top_menu?.title)}
                    </Link>
                  }
                  {subMenuList.section_sub_menus &&
                    <ul className={`${style.megamenu_sub_ul_sec}`}>
                      {subMenuList.section_sub_menus && subMenuList.section_sub_menus.map((menuItem, index) => (
                        <li key={index}>
                          <Link
                            href={menuItem.navsub_menus?.url}
                            target={menuItem.navsub_menus?.target}
                            onClick={() => handleMenuItemClick(medaindex)}>
                            {menuItem.navsub_menus?.title && parse(menuItem.navsub_menus?.title)}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  }
                </li>
              ))}
            
            </ul>
          }
        </div>
      </div>
    </div>
  );
};

export default Index;
