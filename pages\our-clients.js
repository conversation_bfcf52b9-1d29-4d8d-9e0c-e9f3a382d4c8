import React, { useState, useEffect } from "react";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import ourClients from "@/styles/OurClients.module.scss";
// import required modules
import InnerBanner from "@/component/InnerBanner";
import TabSection from "@/component/Tab-section";

import Yoast from "@/component/yoast";
import { getClients,getIrMenuLinks } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const OurClients = (props) => {
    const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
     useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);
 

    const yoastData = props?.ClientData?.yoast_head_json;

  if (!props.ClientData) {
    return null;
  }
    return (
        <div>
            {/* -----------Banner section----------- */}

           
      {yoastData && <Yoast meta={yoastData} />}

      {props &&
        props?.ClientData &&
        props?.ClientData?.acf &&
        props?.ClientData?.acf?.banner_title &&
        (props?.ClientData?.acf?.mob_banner_image ||
          props?.ClientData?.acf?.banner_image ||
          props?.ClientData?.acf?.breadcrumbs ||
          props?.ClientData?.acf?.banner_viedo ||
          props?.ClientData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.ClientData?.acf?.banner_title}
          breadcrumb1={props?.ClientData?.acf?.active_breadcrumbs ==='yes' ? props?.ClientData?.acf?.breadcrumbs : '' } 
          background={`${isMobile ? props?.ClientData?.acf?.mob_banner_image?.url : props?.ClientData?.acf?.banner_image?.url}`}
          videoSrc={props?.ClientData?.acf?.banner_viedo?.url}
          banner_type={props?.ClientData?.acf?.banner_type}
        />
      ) : null
      }

            {/* =========Tab Section====== */}
          {props &&
        props?.IRMenuData &&
                props?.IRMenuData?.about_us_menu &&
                <TabSection tabs={props?.IRMenuData?.about_us_menu} />
            }
            {/* -----------Our Clients section----------- */}

            <section className={`${ourClients.clients_logo_section}`}>
                <div
                    className={`${ourClients.clients_logo_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_130} `}
                >
                    {props && props?.ClientData && props?.ClientData?.acf && props?.ClientData?.acf?.our_clients_title &&
                    <div className={comon.mb_30}>
                        <h3 data-aos="fade-up" data-aos-duration="1000">{props?.ClientData?.acf?.our_clients_title && parse(props?.ClientData?.acf?.our_clients_title)}</h3>
                        </div>
                    }
                    {props && props?.ClientData && props?.ClientData?.acf && props?.ClientData?.acf?.our_clients_content &&
                        <>{props?.ClientData?.acf?.our_clients_content && parse(props?.ClientData?.acf?.our_clients_content)}</>
                    }
                    <ul className={`${ourClients.clients_logo_ul_list} ${comon.mt_50}`}>
                        {props && props?.ClientData &&
                          props?.ClientData?.acf &&
                          props?.ClientData?.acf?.our_clients &&
                          props?.ClientData?.acf?.our_clients.map((data, index) => (
                            <li  data-aos="fade-up" data-aos-duration="1000" key={index}>
                                <div className={`${ourClients.clients_logo_img}`}>
                                    <Image src={data?.url} height={100} width={306} alt="" />
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            </section>
        </div>
    );
};

export default OurClients;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const ClientData = await getClients(locale); 
   const IRMenuData = await getIrMenuLinks(locale);
  
  return {
    props: {
      ClientData: ClientData || null,  
       IRMenuData: IRMenuData || null,
    },
    revalidate: 10,
  };
};