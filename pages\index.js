import React, { useState, useEffect, useRef } from "react";
// ---buttion-import--start
import BannerMain from "@/component/BannerMain";
import InquireNow from "@/component/InquireNow";
import HomeAbout from "@/component/HomeAbout";
import VideoBlock from "@/component/VideoBlock";
import LatestNews from "@/component/LatestNews";
import Clients from "@/component/MarqueeClient";
import Impact from "@/component/Impact"

import Yoast from "@/component/yoast";
import parse from "html-react-parser";
import {
  getHome,getInsightsPosts,getInsightsRelatedTaxonamy,
} from "@/utils/lib/server/publicServices";

export default function Home(props) {



  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const yoastData = props?.homeData?.yoast_head_json;

  if (!props.homeData) {
    return null;
  }


  return (
    <>
     

     {yoastData && <Yoast meta={yoastData} />}
      {props &&
        props?.homeData &&
        props?.homeData?.acf &&
        props?.homeData?.acf?.home_page_banner &&
        <BannerMain
          bannerSlide={props?.homeData?.acf?.home_page_banner}
        />
      } 
      {props &&
        props?.homeData &&
        props?.homeData?.acf &&
        ( props?.homeData?.acf?.about_us_title ||
          props?.homeData?.acf?.about_us_button ||
          props?.homeData?.acf?.about_us_content ||
          props?.homeData?.acf?.about_us_image) ? (
        <HomeAbout
          about_us_title={props?.homeData?.acf?.about_us_title}
          about_us_button={props?.homeData?.acf?.about_us_button}
          about_us_content={props?.homeData?.acf?.about_us_content}
          about_us_image={props?.homeData?.acf?.about_us_image}
          counter_details={props?.homeData?.acf?.counter_details}
        />
      ):null
      }
      
      {/* <VideoBlock videos={"home_video.mp4"} videoPoster={"video_poster.jpg"} /> */}
{props &&
        props?.homeData &&
        props?.homeData?.acf &&
        ( props?.homeData?.acf?.video_file ||          
          props?.homeData?.acf?.video_coverposter ) ? (
          <VideoBlock
            videos={props?.homeData?.acf?.video_file?.url}
            videoPoster={props?.homeData?.acf?.video_coverposter?.url} />
        ) : null
      }
      
      <section
        style={{
          background: "#1e1e2c ",
          // background: "#1e1e2c url(../images/bg_03.png) top center no-repeat",
          backgroundSize: "100%",
          backgroundPosition: "top center",
        }}
        className='home_bg_color'
      >
        {props &&
          props?.homeData &&
          props?.homeData?.acf &&
          (props?.homeData?.acf?.core_domains_sub_text ||
            props?.homeData?.acf?.core_domains_title ||
            props?.homeData?.acf?.core_domains_content ||
            props?.homeData?.acf?.core_domains_list) ? (
          <Impact
            core_domains_sub_text={props?.homeData?.acf?.core_domains_sub_text}
            core_domains_title={props?.homeData?.acf?.core_domains_title}
            core_domains_content={props?.homeData?.acf?.core_domains_content}
            core_domains_list={props?.homeData?.acf?.core_domains_list}
          />
        ) : null
        }
        {props &&
          props?.homeData &&
          props?.homeData?.acf &&
          (props?.homeData?.acf?.our_clients_title ||
          props?.homeData?.acf?.our_clients ) ? (
            <Clients
              our_clients_title = {props?.homeData?.acf?.our_clients_title}
              our_clients={props?.homeData?.acf?.our_clients}
              our_button={props?.homeData?.acf?.our_clients_button}              
            />
          ): null
        }
        {props &&
          props?.InsightsPostsData &&
          <LatestNews
            insights_sub_text={props?.homeData?.acf?.insights_sub_text}
            insights_title={props?.homeData?.acf?.insights_title}
            insights={props?.InsightsPostsData}
          />
        }
      </section>
<div id="inquire"></div>
      <InquireNow formtitle={props?.homeData?.title?.rendered} />
    </>
  );
}


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const homeData = await getHome(locale);
  

  const query = "";
  const InsightsPostsData = await getInsightsPosts(locale.locale, query);
  const InsightstaxonamyList = await getInsightsRelatedTaxonamy(locale.locale); // Fetch taxonomy data
  
    let InsightsPosts = [];
    if (homeData && homeData?.acf && Array.isArray(homeData?.acf?.insights)) {
    InsightsPosts = homeData?.acf?.insights;
    }

    // Format Investors Resources  for use in the component
    let InsightsPostsRelation = [];
    if (InsightsPosts.length > 0) {
    InsightsPostsRelation = InsightsPostsData.filter(
        (post) => InsightsPosts.includes(post.id) // Assuming post.id is the identifier
    );
  }

  
  // Add `catslug` to each post in InsightsPostsRelation
  InsightsPostsRelation = InsightsPostsRelation.map((post) => {
    const categorySlugs = post.categories
      ?.map((catId) => {
        const matchingCategory = InsightstaxonamyList.find(
          (taxonomy) => taxonomy.id === catId
        );
        return matchingCategory?.slug; // Return the slug if a match is found
      })
      .filter(Boolean); // Remove undefined values

    return {
      ...post,
      catslug: categorySlugs, // Add category slugs to the post
    };
  });
  
  // console.log('homeData', InsightsPostsRelation)

  return {
    props: {
      homeData: homeData || null,
      InsightsPostsData: InsightsPostsRelation || [],
    },
    revalidate: 10,
  };
};
