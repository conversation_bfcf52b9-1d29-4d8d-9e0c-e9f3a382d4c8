// pages/api/tadawul.js
import xml2js from "xml2js";

let cache = {
  timestamp: 0,
  data: null,
};

export default async function handler(req, res) {
  try {
    const now = Date.now();
    const cacheDuration = 10 * 1000; // 10 seconds

    // Use cache if it's still valid
    if (cache.data && now - cache.timestamp < cacheDuration) {
      return res.status(200).json({
        success: true,
        data: cache.data,
        cached: true,
      });
    }

    const soapRequest = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://services.RSS.tadawul.com">
      <soapenv:Header/>
      <soapenv:Body>
          <ser:getDetailQuoteForCompany>
              <companyId>9570</companyId>
              <secureKey>*********</secureKey>
          </ser:getDetailQuoteForCompany>
      </soapenv:Body>
    </soapenv:Envelope>`;

    const response = await fetch("https://webservices.tadawul.com.sa/Tadawul_WebAPI/services/GetDetailQuote", {
      method: "POST",
      headers: {
        "Content-Type": "text/xml",
        "SOAPAction": "getDetailQuoteForCompany",
      },
      body: soapRequest,
    });

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const xmlText = await response.text();
    const parser = new xml2js.Parser({ explicitArray: false });
    const jsonData = await parser.parseStringPromise(xmlText);

    const returnData = jsonData["soapenv:Envelope"]["soapenv:Body"]["p451:getDetailQuoteForCompanyResponse"].getDetailQuoteForCompanyReturn;

    if (!returnData) {
      throw new Error("Could not find return data in response");
    }

    // Store response in cache
    cache = {
      timestamp: now,
      data: returnData,
    };

    res.status(200).json({
      success: true,
      data: returnData,
      cached: false,
    });
  } catch (error) {
    console.error("API route error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch data",
      message: error.message,
    });
  }
}
