@import "variable", "base";

 .news_img_mian{ width: 100%; border-radius: 10px; overflow: hidden;

  img{
    @media #{$media-767} {
      height: 350px;
    }
  }
}
.home_news_sec{position: relative;z-index: 9;}
 .slider_block{ width: 25%;

  .news_txt_block{ width: 100%; height: 100%; position: absolute; top: 0; left: 0; padding: 11%; display: flex; flex-wrap: wrap;
    @media #{$media-1440} {
      padding: 20px 11%;
    }
    @media #{$media-1024} {
      padding: 20px 7%;
    }
    h5{ font-family:var(--helveticaneuelt_arabic_55);  font-size: 0.938rem; color: #FBB036;   font-weight: 400; text-transform: uppercase; margin-bottom: 25px;
    
      @media #{$media-1024} {
        margin-bottom: 10px;
      }
      @media #{$media-820} {
        font-size:1.50rem;
      }
    }
    p{
      font-family:var(--helveticaneuelt_arabic_55); font-size:1.25rem;  line-height: 1.75rem; color: $white;   font-weight: 400;


      @media #{$media-820} {
        font-size:1.50rem;  line-height: 1.80rem;
      }
      @media #{$media-700} {
        font-size:2.25rem;  line-height: 2.75rem;
      }
    }
    
  }
  @media #{$media-1024} {
    width: 35%;
  }
 
  @media #{$media-700} {
    width: 75%;
  }
  
  
}
// .sec_title{
//   font-family: var(--helveticaneueltarabic_light);
//   font-weight: 300;
// }