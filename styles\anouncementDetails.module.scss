@import "variable", "base";

.anouncement_details {
    h1 {
        color: #2a2656;
        font-size: 40px;
        line-height: 120%;

        @media #{$media-767} {
            font-size: 25px;
        }
    }

    .anouncement_content {
        margin-top: 40px;

        table {
            border-collapse: collapse;
            /* Ensures no gaps between cells */
            width: 100%;
            border: 1px solid #dbdbdb;
            border-radius: 10px;

            tbody {
                tr {

                    td {
                        padding: 10px 20px;
                        border: 1px solid #dbdbdb;
                        color: #3e3e3e;
                        font-size: 16px;
                        line-height: 22px;
                        font-family: var(--helveticaNeue);
                    }
                    @media #{$media-767} {
                        font-size: 15px;
                        line-height: 20px;
                    }
                }

            }

            thead {
                tr {

                    th {
                        padding: 15px 20px;
                        border: 1px solid #dbdbdb;
                        color: #3c3c3c;
                        font-size: 18px;
                        line-height: 24px;
                        font-weight: 600;
                        font-family: var(--helveticaNeue);
                        @media #{$media-767} {
                            font-size: 16px;
                            line-height: 22px;
                        }
                    }
                }
            }
        }
    }

}