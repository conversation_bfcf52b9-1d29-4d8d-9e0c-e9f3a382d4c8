import React from 'react'
import news from "@/styles/NewsCard.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from 'next/image';
import Link from 'next/link';

const NewsCard = ({ cardData, className }) => {
    return (
        <>
            {cardData.map((data, index) => (

                <li className={`${news.news_card_body} ${className}`} key={index} >
                    <Link
                        href={data.link ? data.link : "#"}
                    >
                    <div className={`${news.news_card_img}`}>
                        <Image
                            src={data.Image}
                            height={240}
                            width={340}
                            alt={""}
                        />
                    </div>
                    <div className={`${news.news_card_data}`}>
                        {data.date &&
                            <span>{data.date}</span>

                        }
                        <h4>
                            {data.Content}
                        </h4>
                    </div>
                    </Link>
                </li>
            ))}
        </>
    )
}

export default NewsCard