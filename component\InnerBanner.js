import React, { useEffect, useState } from "react";
import style from "@/styles/innerBanner.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import Link from "next/link";
import parse from "html-react-parser";
import { useRouter } from "next/router";

const InnerBanner = ({
  pagename,
  breadcrumb1,
  background,
  backgroundType = "fullbg", // New prop for background type
  videoSrc,
  headerFontSize,
  paddingTop,
  paddingBottom,
  banner_type,
  sharechart,
}) => {
  const [tabActive, setTabactive] = useState(null);
  const [selectedTab, setSelectedTab] = useState(null);

  const tabSelect = (index) => setTabactive(tabActive === index ? null : index);
  const tabActiveSelect = (index) => setSelectedTab(index);
  const router = useRouter();
  useEffect(() => {
    function handleOutsideClick(event) {
      const targetElement = document.getElementById("insights_tabs");
      if (targetElement && !targetElement.contains(event.target)) {
        setTabactive(null);
      }
    }
    document.addEventListener("click", handleOutsideClick);
    return () => document.removeEventListener("click", handleOutsideClick);
  }, []);

  const pathtrim = sharechart;

   const changeAmount = parseFloat(pathtrim?.changeAmount || 0).toFixed(2);
  const changePercentage = parseFloat(pathtrim?.changePercentage || 0).toFixed(2);

  return (
    <div>
      <section
        className={`${style.inner_banner_section} ${
          backgroundType === "fullbg" ? style.full_bg : style.icon_bg
        } inner_banner_section`}
        style={{
          backgroundImage: `url(/images/history_new_bg.png)`,
          backgroundRepeat: "no-repeat",
          backgroundPosition: "bottom center",
          backgroundSize: "cover",
          backgroundColor: "rgb(34, 35, 69)",
          ...(background &&
            banner_type === "image" && {
              backgroundImage: `url(${background})`,
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
              backgroundSize: "cover",
            }),
        }}
      >
        {banner_type === "video" && videoSrc && (
          <video
            className={style.background_video}
            src={videoSrc}
            autoPlay
            loop
            muted
            playsInline
          />
        )}

        <div
          className={`${style.inner_banner_container} ${
            comon.inner_banner_container
          } ${comon.wrap} ${comon.pb_200} ${
            paddingTop ? "paddingTop370" : ""
          } ${paddingBottom ? "paddingBottom150" : ""}`}
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          {breadcrumb1 && (
            <div className={style.inner_banner_breadcrumb_sec}>
              {breadcrumb1.map(
                (bread, index) =>
                  bread?.page_names && (
                    <div
                      className={`${style.inner_banner_breadcrumb_head} ${comon.inner_banner_breadcrumb_head}`}
                      key={index}
                    >
                      <Link href={bread?.page_link || "#."}>
                        {bread?.page_names && parse(bread.page_names)}
                      </Link>
                    </div>
                  )
              )}
            </div>
          )}
          {pagename && (
            <h1 className={`${headerFontSize ? "font70" : ""} ${sharechart ? style.share_banner_title : ''}`}>
              {pagename && parse(pagename)}
            </h1>
          )}

          {sharechart && (
            <div className={`${style.inner_banner_share_chart} ${style.share_details}`}>
              {/* <Image src={sharechart?.url} height={315} width={380} alt="" className={style.sharechart_img} /> */}
              <div className={style.diasmond}>
                <Image
                  src="/images/diamond_new.svg"
                  alt=""
                  width={25}
                  height={40}
                />
              </div>
              <div className={`${style.stock_info} ${comon.stock_info}`}>
               {router.locale === "ar" ?
                  <h5>{pathtrim?.companyLongNameAr && parse(pathtrim?.companyLongNameAr)}</h5>
                  :
                  <h5>{pathtrim?.companyLongName && parse(pathtrim?.companyLongName)}</h5>
                }
                <p>{pathtrim?.id && parse(pathtrim?.id)}</p>
                 <h6 className={`${style.stockData} ${style.down}`}>
          <span>{pathtrim?.lastTradePrice && parseFloat(pathtrim?.lastTradePrice).toFixed(2)} </span>
          
          {/* {changeAmount > 0 ?
            <Image src="/images/stock_up.svg" alt="" width={28} height={28} />
            : changeAmount <= 0 ?
              <Image src="/images/stock_down.svg" alt="" width={28} height={28} />
              : null
          } */}
          <span className={`changeTxt ${changeAmount > 0 ? "upTxt" : changeAmount <= 0 ? "downTxt" : "normalTxt"}`}>     
            ({changeAmount}) ({changePercentage}%)
          </span>                           
        </h6>
                
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default InnerBanner;
