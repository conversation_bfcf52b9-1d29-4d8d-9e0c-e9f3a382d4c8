import React, { useEffect, useState } from "react";
import comon from "@/styles/comon.module.scss";
import InquireNow from "@/component/InquireNow";
import Image from "next/image";
import publicEngagement from "@/styles/PublicEngagement.module.scss";
// import required modules
import InnerBanner from "@/component/InnerBanner";
import Link from "next/link";
import OverviewTwoCol from "@/component/OverviewTwoCol";
import RelatedNews from "@/component/relatedNews";
import styles from "@/styles/awards.module.scss";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css/navigation";
import { Navigation, Autoplay } from "swiper/modules";
// Import Swiper styles
import "swiper/css";

import { useRouter } from "next/router";
import Yoast from "@/component/yoast";
import {
  getAdvisorytaxSlugs,
  getAdvisorytaxList,
  getAdvisoryPosts,
  getInsightsPosts,
  getInsightsRelatedTaxonamy,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const publicEngagementPage = (props) => {
  const router = useRouter();
  const { slug } = router.query; // Get the slug from the query parameters

  const language = router.locale === "ar" ? "ar" : "en";

  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const yoastData = props?.AdvisoryTaxData?.yoast_head_json;

  if (!props.AdvisoryTaxData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.AdvisoryTaxData &&
      props?.AdvisoryTaxData?.acf &&
      props?.AdvisoryTaxData?.acf?.banner_title &&
      (props?.AdvisoryTaxData?.acf?.mob_banner_image ||
        props?.AdvisoryTaxData?.acf?.breadcrumbs ||
        props?.AdvisoryTaxData?.acf?.banner_image ||
        props?.AdvisoryTaxData?.acf?.banner_viedo ||
        props?.AdvisoryTaxData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.AdvisoryTaxData?.acf?.banner_title}
          breadcrumb1={
            props?.AdvisoryTaxData?.acf?.active_breadcrumbs === "yes"
              ? props?.AdvisoryTaxData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.AdvisoryTaxData?.acf?.mob_banner_image?.url
              : props?.AdvisoryTaxData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.AdvisoryTaxData?.acf?.banner_viedo?.url}
          banner_type={props?.AdvisoryTaxData?.acf?.banner_type}
        />
      ) : null}

      {/* -----------Overview section----------- */}
      {props &&
      props?.AdvisoryTaxData &&
      props?.AdvisoryTaxData?.acf &&
      (props?.AdvisoryTaxData?.acf?.overview_title ||
        props?.AdvisoryTaxData?.acf?.overview_content) ? (
        <OverviewTwoCol
          head={props?.AdvisoryTaxData?.acf?.overview_title}
          paragraph={props?.AdvisoryTaxData?.acf?.overview_content}
        />
      ) : null}

      {/* /*---------------Why Belive section-------------- */}
       {props &&
      props?.AdvisoryTaxData &&
      props?.AdvisoryTaxData?.acf &&
      (props?.AdvisoryTaxData?.acf?.why_believe_titles ||
        props?.AdvisoryTaxData?.acf?.why_believe_content ||
        props?.AdvisoryTaxData?.acf?.whats_benefit_titles ||
        props?.AdvisoryTaxData?.acf?.whats_benefit_content) ? (
      <section className={`${publicEngagement.why_section}`}>
        <div
          className={`${comon.public_engagement_sub_domain_container}  ${comon.wrap} ${comon.pt_100}  ${comon.pb_100} `}
        >
          {(props?.AdvisoryTaxData?.acf?.why_believe_titles || props?.AdvisoryTaxData?.acf?.why_believe_content) &&(
          <div className={publicEngagement.why_items}>
            {props?.AdvisoryTaxData?.acf?.why_believe_titles && (
            <div className={publicEngagement.why_items_left}>
              <h3  data-aos="fade-up"
                data-aos-duration="1000">{parse(props?.AdvisoryTaxData?.acf?.why_believe_titles)}</h3>
            </div>
            )}
            {props?.AdvisoryTaxData?.acf?.why_believe_content && (
            <div className={publicEngagement.why_items_right} data-aos="fade-up" data-aos-duration="1000">
              {parse(props?.AdvisoryTaxData?.acf?.why_believe_content)}
              </div>
            )}            
            </div>
             )}
        {(props?.AdvisoryTaxData?.acf?.whats_benefit_titles || props?.AdvisoryTaxData?.acf?.whats_benefit_content) &&(
          <div className={publicEngagement.why_items}>
            {props?.AdvisoryTaxData?.acf?.whats_benefit_titles && (
              <div className={publicEngagement.why_items_left}>
                <h3 data-aos="fade-up" data-aos-duration="1500">{parse(props?.AdvisoryTaxData?.acf?.whats_benefit_titles)}</h3>
              </div>
            )}
            {props?.AdvisoryTaxData?.acf?.whats_benefit_content && (
            <div className={publicEngagement.why_items_right} data-aos="fade-up" data-aos-duration="1500">
              {parse(props?.AdvisoryTaxData?.acf?.whats_benefit_content)}
              </div>
            )}
            
            </div>
            )}
        </div>
          </section>
          
          ) : null}
      {/* /*--------------End of Why Belive section--------------- */}

      {/* -----------Public Engagement Sub Domains section----------- */}

      {props &&
        props?.AdvisoryPostArray &&
        props?.AdvisoryPostArray?.length > 0 && (
          <section
            className={`${publicEngagement.public_engagement_sub_domain_section}`}
          >
            <div
              className={`${publicEngagement.public_engagement_sub_domain_container} ${comon.public_engagement_sub_domain_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_130} `}
            >
              {props?.AdvisoryTaxData?.acf?.advisory_listing_title && (
                <h3 data-aos="fade-up" data-aos-duration="1000">
                  {props?.AdvisoryTaxData?.acf?.advisory_listing_title &&
                    parse(props?.AdvisoryTaxData?.acf?.advisory_listing_title)}
                </h3>
              )}
              <ul
                className={`${publicEngagement.public_engagement_sub_domain_card_sec} `}
              >
                {props?.AdvisoryPostArray &&
                  props?.AdvisoryPostArray?.length > 0 &&
                  props?.AdvisoryPostArray.map((data, adindex) => (
                    <li
                      data-aos="fade-up"
                      data-aos-duration="1000"
                      key={adindex}
                    >
                      <Link
                        href={`/advisory/${props?.AdvisoryTaxData?.slug}/${data?.slug}`}
                        className={`${publicEngagement.public_engagement_card} `}
                      >
                        <div
                          className={`${publicEngagement.public_engagement_sub_domain_card_logo_sec} `}
                        >
                          <div
                            className={`${publicEngagement.public_engagement_sub_domain_card_logo}`}
                          >
                            {data?.acf?.related_advisory_listing_icons && (
                              <Image
                                src={
                                  data?.acf?.related_advisory_listing_icons?.url
                                }
                                height={120}
                                width={120}
                                alt=""
                              />
                            )}
                          </div>
                          {data?.acf?.related_advisory_listing_title ? (
                            <h4>{data?.acf?.related_advisory_listing_title}</h4>
                          ) : (
                            <h4>
                              {data?.title && parse(data?.title?.rendered)}
                            </h4>
                          )}
                        </div>
                        <div
                          className={`${publicEngagement.public_engagement_sub_domain_card_footer_sec} `}
                        >
                          <div
                            className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} ${publicEngagement.arrow_btn} custom_next_1`}
                          >
                            <Image
                              className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                              src={`/images/next_black_ic.svg`}
                              alt="call"
                              width={11}
                              height={11}
                            />
                          </div>
                        </div>{" "}
                      </Link>
                    </li>
                  ))}
              </ul>
            </div>
          </section>
        )}

      {props &&
      props?.AdvisoryTaxData &&
      props?.AdvisoryTaxData?.acf &&
      props?.InsightsPostsData?.length > 0 &&
      (props?.AdvisoryTaxData?.acf?.related_insights_title ||
        props?.AdvisoryTaxData?.acf?.related_insights) ? (
        <section
          className={styles.related_inisights}
          style={{ backgroundColor: "#ffffff" }}
        >
          <div className={`${comon.wrap}`}>
            <div className={styles.expert_top}>
              {props?.AdvisoryTaxData?.acf?.related_insights_title && (
                <h2 className={``} data-aos="fade-up" data-aos-duration="1000">
                  {props?.AdvisoryTaxData?.acf?.related_insights_title &&
                    parse(props?.AdvisoryTaxData?.acf?.related_insights_title)}
                </h2>
              )}
              <div className={`swiper_custom_arws`}>
                <div className={`swiper_button_prev_case_study`}>
                  <Image
                    src="/images/left_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
                <div className={`swiper_button_next_case_study`}>
                  <Image
                    src="/images/right_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
              </div>
            </div>
            <Swiper
              slidesPerView={3}
              spaceBetween={10}
              speed={1000}
              loop={true}
              navigation={{
                nextEl: ".swiper_button_next_case_study",
                prevEl: ".swiper_button_prev_case_study",
              }}
              modules={[Navigation, Autoplay]}
              autoplay={{
                delay: 1000,
                disableOnInteraction: false,
              }}
              dir={language === "ar" ? "rtl" : "ltr"}
              key={language}
              className={`${styles.news_slider} news_slider`}
              breakpoints={{
                0: {
                  slidesPerView: 1,
                  spaceBetween: 10,
                },
                600: {
                  slidesPerView: 2,
                  spaceBetween: 10,
                },
                1200: {
                  slidesPerView: 3,
                  spaceBetween: 15,
                },
              }}
            >
              {props?.InsightsPostsData &&
                props?.InsightsPostsData.map((rlns, reindex) => (
                  <SwiperSlide key={reindex}>
                    <RelatedNews
                      pic={rlns?.acf?.listing_image?.url}
                      tag={rlns?.acf?.listing_tag}
                      title={rlns?.title?.rendered}
                      link={
                        rlns.slug
                          ? `/insights/${rlns.catslug[0]}/${rlns.slug}`
                          : "#"
                      }
                    />
                  </SwiperSlide>
                ))}
            </Swiper>
          </div>
        </section>
      ) : null}

      {/* <InquireNow formtitle={props?.AdvisoryTaxData?.acf?.banner_title} /> */}
      <InquireNow
        formtitle={
          props?.AdvisoryTaxData?.name ||
          props?.AdvisoryTaxData?.acf?.banner_title
        }
        optionaltitle={props?.AdvisoryTaxData?.acf?.inq_title}
        optionalcontent={props?.AdvisoryTaxData?.acf?.inq_content}
      />
    </div>
  );
};

export default publicEngagementPage;

export async function getStaticPaths(locale) {
  const ProductlistData = await getAdvisorytaxList(locale);

  const paths = ProductlistData.map((product) => ({
    params: { taxonamySlug: product.slug },
    //locale, // Ensure locale is part of the paths (for multi-language support)
  }));
  //console.log('fdshfsd', locale);
  return {
    paths: paths,
    fallback: "blocking", // false or 'blocking'
  };
}

export async function getStaticProps({ params, locale }) {
  const slug = params.taxonamySlug;
  // console.log('fdshfsd', slug);
  //console.log('getStaticProps', locale);
  const ProductSlugData = await getAdvisorytaxSlugs(slug, locale);

  const taxonomyId = ProductSlugData[0]?.id; // Assuming `id` is directly accessible

  if (!taxonomyId) {
    return {
      notFound: true, // Optionally return a 404 if taxonomy ID is not found
    };
  }

  const query = "";
  const AdvisoryPostData = await getAdvisoryPosts(locale, query);

  // Filter posts based on the taxonomy ID and advisory category
  const AdvisoryPostArray = AdvisoryPostData.filter(
    (post) =>
      Array.isArray(post?.advisory_category) &&
      post?.advisory_category.includes(taxonomyId)
  );

  //  console.log('testingdata', InsightsPostsArray)

  const InsightsPostsData = await getInsightsPosts(locale, query);
  const InsightstaxonamyList = await getInsightsRelatedTaxonamy(locale); // Fetch taxonomy data

  let InsightsPosts = [];
  if (
    ProductSlugData[0] &&
    ProductSlugData[0]?.acf &&
    Array.isArray(ProductSlugData[0]?.acf?.related_insights)
  ) {
    InsightsPosts = ProductSlugData[0]?.acf?.related_insights;
  }

  // Format Investors Resources  for use in the component
  let InsightsPostsArray = [];
  if (InsightsPosts.length > 0) {
    InsightsPostsArray = InsightsPosts.map((id) =>
      InsightsPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  // Add `catslug` to each post in InsightsPostsRelation
  InsightsPostsArray = InsightsPostsArray.map((post) => {
    const categorySlugs = post.categories
      ?.map((catId) => {
        const matchingCategory = InsightstaxonamyList.find(
          (taxonomy) => taxonomy.id === catId
        );
        return matchingCategory?.slug; // Return the slug if a match is found
      })
      .filter(Boolean); // Remove undefined values

    return {
      ...post,
      catslug: categorySlugs, // Add category slugs to the post
    };
  });

  // console.log('testingdata', InsightsPostsArray)
  return {
    props: {
      AdvisoryTaxData: ProductSlugData[0] || null,
      InsightsPostsData: InsightsPostsArray || [],
      AdvisoryPostArray: AdvisoryPostArray || [],
    },
    revalidate: 10,
  };
}
