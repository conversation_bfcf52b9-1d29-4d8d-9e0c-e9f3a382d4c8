@import "variable", "base";

//   {/* -----------Overview section----------- */}

.overview_section {
  width: 100%;
  background-color: #ffffff;

  .overview_container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .overview_left_sec {
      padding-right: 0%;
      width: 100%;

      h3 {
        font-size: 3.4375rem;
        line-height: 100%;
        font-weight: 400;
        display: inline-block;
        color: #2a2656;
      }

      @media #{$media-1280} {
        padding-right: 2%;
      }
    }

    .overview_left_investor_relation_sec {
      width: 50%;
    }

    .overview_right_sec {
      width: 100%;

      p {
        color: #404040;
        font-size: 1.25rem;
        line-height: 1.938rem;
        font-weight: 400;
        padding-bottom: 30px;

        span {
          color: #5E45FF;
          font-family: var(--helveticaneueltarabicBold);
        }

        // &:nth-child(1) {
        //   color: #404040;
        //   font-size: 20px;
        //   line-height: 26px;
        //   font-weight: 400;

        //   @media #{$media-1366} {
        //     font-size: 19px;
        //   }

        //   @media #{$media-1024} {
        //     font-size: 18px;
        //   }

        //   @media #{$media-820} {
        //     font-size: 16px;
        //   }
        // }

        @media #{$media-1024} {
          padding-bottom: 20px;
        }

        @media #{$media-820} {
          font-size: 1.6rem;
          line-height: 2.3rem;
          width: 80%;
        }
        @media #{$media-767} {
          width: 100%;
        }
        @media #{$media-500} {
          font-size: 2rem;
          line-height: 2.5rem;
        }
      }

      .overview_swiper_sec {
        width: 100%;

        @media #{$media-1024} {
          margin-top: 20px;
        }

        .overview_swiper_card {
          width: 80%;
          @media #{$media-1500} {
            width: 100%;
          }

          .overview_swiper_card_head_sec {
            padding: 25px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h4 {
              color: #2a2656;
              // font-size: 35px;
              font-size: 2.1875rem;
              line-height: 45px;
              font-weight: 400;
              display: block;

            }

            .overview_swiper_btn_sec {
              display: flex;
              gap: 20px;

              span {
                position: unset;
                display: inline-flex;

                margin: 0;

                img {
                  width: 34%;
                  display: block;
                  height: auto;
                  @media #{$media-767} {
                    width: auto;
                  }
                }

                &:hover {

                  background-color: #5e45ff !important;
                }

                &:hover img {
                  filter: invert(0) brightness(4) !important;
                }
              }

              @media #{$media-1440} {
                gap: 15px;
              }

              @media #{$media-768} {
                gap: 10px;
              }
            }

            @media #{$media-1600} {
              padding: 20px 0;
            }

            @media #{$media-500} {
              align-items: flex-end;
            }
          }

          .overview_swiper_card_data_sec {
            list-style: none;
            justify-content: space-between;
            display: flex;

            // margin-left: -6.5%;
            // margin-right: -6.5%;
            @media #{$media-767} {
              display: flex;
              flex-wrap: wrap;
              column-gap: 20px;
              justify-content: flex-start;
            }
            &.overview_even{
              justify-content: flex-start;
              .dot_icon{
                padding-inline: 9%;
              }
            }

            li {
              display: inline-flex;
              position: relative;
              flex-direction: column;
              justify-content: flex-start;
              padding: 0 0%;
              // max-width: 225px;

              h4 {
                background: linear-gradient(-90deg,
                    rgba(227, 181, 255, 1) -10%,
                    rgba(94, 69, 255, 1) 50%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                // font-size: 65px;
                font-size: 5.938rem;
                font-family: var(--helveticaneuelt_arabic_55);
                line-height: 115%;
                font-weight: 600;
                display: inline-flex;
                justify-content: flex-end;
                align-items: flex-end;
                direction: ltr;
                //  .big{
                //   background: linear-gradient(
                //   -90deg,
                //   rgba(227, 181, 255, 1) -10%,
                //   rgba(94, 69, 255, 1) 50%
                // ) ;
                // background-clip: text;
                // -webkit-background-clip: text;
                // -webkit-text-fill-color: transparent;
                //   // font-size: 65px;
                //   font-size: 5.938rem;
                //   font-family: var(--helveticaneuelt_arabic_55);
                //   line-height: 100%;
                //   font-weight: 400;
                //   display: inline-flex;
                //   align-items: flex-end;
                //  }
                // span {
                //   color: #2c275d;
                //   // font-size: 30px;
                //   font-size: 1.875rem;
                //   line-height: 100%;
                //   font-weight: 400;
                //   padding-left: 10px;
                // }
                @media #{$media-1280} {
                  font-size: 3.938rem;
                }
              }

              p {

                font-size: 1.25rem;
                padding-top: 10px;
                line-height: 150%;
                font-weight: 400;
                text-transform: capitalize;
                font-family: var(--helveticaneuelt_arabic_55);

                text-align: end;
                display: inline-flex;
                align-self: flex-end;
                margin-bottom: 0;

                @media #{$media-767} {
                  text-align: start;
                  align-self: center;
                }




                background: linear-gradient(-90deg,
                  rgba(227, 181, 255, 1) 0%,
                  rgba(94, 69, 255, 1) 100%),
                ;
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;


                @media #{$media-1600} {
                  font-size: 18px;
                }

                @media #{$media-1440} {
                  font-size: 16px;
                }

                @media #{$media-820} {
                  font-size: 15px;
                }

                @media #{$media-450} {
                  font-size: 14px;
                }
              }

              &:nth-child(1) {
                border: 0;
              }

              // @media #{$media-1280} {
              //   padding: 0 40px;
              // }
              // @media #{$media-1024} {
              //   padding: 0 20px;
              // }

              @media #{$media-767} {
                padding: 0;
                width: 45%;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-start;
                box-sizing: border-box;
              }



              // &:after {
              //   content: "";
              //   display: block;
              //   width: 25px;
              //   height: 40px;
              //   background: url(../public/images/dot_icn.png) no-repeat center center;
              //   position: absolute;
              //   right: -65%;
              //   top: 43%;
              //   margin-top: -20px;

              //   @media #{$media-1440} {
              //     right: -100%;
              //   }

              //   @media #{$media-1024} {
              //     right: -10px;
              //   }

              //   @media #{$media-767} {
              //     display: none;
              //   }
              // }


            }

            .dot_icon {
              padding-top: 50px;
              @media #{$media-1600} {
                padding-top: 30px;
              }
              @media #{$media-1280} {
                padding-top: 13px;
              }
              @media #{$media-1024} {
                padding-top: 18px;
                padding-inline: 20px;
              }
              @media #{$media-767} {
                display: none;
              }
              // &:last-child {
              //   display: none;
              // }
              img{
                @media #{$media-1024} {
                  width: 20px;
                  height: auto;
                }
              }
            }

            // @media #{$media-1280} {
            //   margin-left: -40px;
            //   margin-right: -40px;
            // }
            // @media #{$media-1024} {
            //   margin-left: -20px;
            //   margin-right: -20px;
            // }

            @media #{$media-500} {
              margin: 0;
            }



            &> :last-child {
              &::after {
                display: none;
              }
            }
          }
          @media #{$media-767} {
            width: 100%;
          }
        }
      }

      @media #{$media-1440} {
        width: 80%;
      }

      @media #{$media-1280} {
        width: 75%;
      }

      @media #{$media-820} {
        width: 100%;
      }
    }

    @media #{$media-820} {
      gap: 20px;
      flex-direction: column;
    }

    @media #{$media-767} {
      gap: 5px;
    }
  }

  .overview_video_container {

    .overview_video_sec {
      width: 100%;
      display: flex;
      gap: 2%;

      .overview_video_card {

        width: 49%;


        .overview_video_card_img {
          width: 100%;
          border-radius: 10px;
          overflow: hidden;

          img {
            display: block;
            object-fit: cover;
            height: auto;
            width: 100%;
          }
        }

        h4 {
          color: #000000;
          text-align: center;
          font-size: 20px;
          line-height: 56px;
          font-weight: 400;
          font-family: var(--helveticaneuelt_arabic_55);

        }

      }



    }





  }
}

.overview_left_cl {
  width: 35%;

  @media #{$media-767} {
    width: 100%;
  }

  h3 {
    font-size: 3.438rem;
    line-height: normal;
    color: #2A2656;

    @media #{$media-1024} {
      font-size: 3rem;
    }
  }
}

.overview_right_cl {
  width: 65%;

  @media #{$media-767} {
    width: 100%;
    margin-top: 15px;
  }

  p {
    font-family: var(--helveticaNeue);
    font-size: 1.25rem;
    line-height: 1.625rem;
    color: #4F4F4F;

    @media #{$media-767} {
      font-size: 1.8rem;
      line-height: 2.6rem;
    }
    @media #{$media-500} {
      font-size: 2rem;
      line-height: 2.6rem;
    }

    &+p {
      margin-top: 20px;
    }
  }
}

.equal_col {
  >div {
    justify-content: space-between;
  }

  .overview_left_cl {
    width: 43%;

    @media #{$media-767} {
      width: 100%;
    }
  }

  .overview_right_cl {
    width: 53%;

    @media #{$media-767} {
      width: 100%;
    }
  }
}
.extra_item{
  visibility: hidden;
  opacity: 0;
  @media #{$media-767} {
    display: none !important;
  }
}
.overview_even{
     li:nth-last-child(2) {
      display: none !important;
  }
}