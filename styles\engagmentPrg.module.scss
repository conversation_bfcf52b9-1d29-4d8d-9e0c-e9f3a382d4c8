@import "variable", "base";
.eng_prg_list{
    --icon_width:135px;
    padding: 30px 25px 20px 25px;
    display: block;
    @media #{$media-767} {
        --icon_width:100px;
        padding: 20px;
    }
    &:hover{
       .link{
            transform: translateX(10px);
       } 
    }
    .link{
        width: 46px;
        height: 46px;
        border-radius: 50%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-inline-start:auto ;
        margin-top: -15px;
        transition: all .3s ease-in-out;
        img{
            width: 37%;
        }
        
    }
    .icon{
        width:var(--icon_width);
        img{
            @media #{$media-767} {
                width: 85px;
                height: auto;
            }
        }
    }
    h3{
        width: calc(100% - var(--icon_width));
        padding-inline-start: 10px;
        color: #fff;
        font-size: 1.365rem;
        line-height: 1.7rem;
        font-weight: 300;
        font-family: var(--helveticaneueltarabicLight);
        @media #{$media-1024} {
            font-size: 1.6rem;
            line-height: 2rem;
        }
        @media #{$media-767} {
            font-size:2rem;
        }
        @media #{$media-500} {
            font-size:2.2rem;
            line-height: 2.7rem;
        }
    }
    .prg_top{
        display: flex;
        align-items: center;
       
    }
}