@import "variable", "base";

.test_height {
  height: 1000px;
  font-family: var(--helveticaneuelt_arabic_55);
}

.h1 {
  font-size: 4.688rem;
  font-weight: normal;
  font-family: var(--helveticaneueltarabicBold);
  line-height: 100%;


  @media #{$media-767} {
    font-size: 4.5rem;
  }


  span {
    // background: linear-gradient(-90deg, rgba(227, 181, 255, 1) 0%, rgba(94, 69, 255, 1) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: left;
    display: inline;
    background-image: linear-gradient(to right, #5e45ff, #5e45ff, #b27afd, #5e45ff, #8f5efe, #b27afd);
  }
}

.rtl .h1 {
  font-weight: 700;
  line-height: 130%;
}

.h2 {
  font-size: 3.438rem;
  color: #1E1E2C;
}

.fld_01 {
  width: 100%;
  background: #fff;
  color: #81809E;
  border-radius: 10px;
  padding: 0 25px;
  border: none;
  outline: none;
  height: 40px;
  font-size: 0.938rem;
  line-height: 100%;
  font-family: var(--helveticaNeue);

  @media #{$media-820} {
    font-size: 14px;
  }

  @media #{$media-767} {
    font-size: 17px;
    padding: 15px;
  }
}

.fld_01::placeholder {
  color: #81809E;
}

select.fld_01 {
  background-image: url(../public/images/select_arw.png);
  background-position: 95% 50%;
  background-repeat: no-repeat;
  -webkit-appearance: none;
  appearance: none;
  color: #81809E;
  font-size: 0.938rem;
  font-family: var(--helveticaNeue);
  line-height: 106%;

  @media #{$media-820} {
    font-size: 14px;
  }

  @media #{$media-767} {
    font-size: 17px;
    padding: 0 15px;
  }
}




textarea.fld_01 {
  height: calc(100% - 25px);
  padding: 15px 25px;
  font-family: var(--helveticaNeue);
  resize: none;

  @media #{$media-767} {
    height: 100px;
    margin-bottom: 15px;
    padding: 15px;
  }
}


.color_02 {
  color: #2A2656;
}

.font_18 {
  font-size: 1.125rem;
  color: #4F4F4F;

  @media #{$media-820} {
    font-size: 1.6rem;
  }

  @media #{$media-768} {
    font-size: 1.9rem;
  }
}

.text_white {
  color: $white;
}

.text_black {
  color: $black;
}

.text_black {
  color: $white;
}

.swipper_nav_block {
  margin-left: -6px;
  margin-right: -6px;

  .custom_btn {
    position: unset;
    padding: 2%;
    border: none;
    background: $white !important;
    margin: 6px;

    img {
      width: 50%;
    }

    &:hover {
      background: #e9e9e9 !important;

      img {
        filter: invert(0) !important;
      }
    }
  }

  .white_border_btn {
    border: 1px solid #ffffff;
    background: transparent !important;

    img {
      filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(159deg) brightness(102%) contrast(105%);
    }
  }

}

.overflow_hidden {
  overflow: hidden !important;
}

.overflow_scroll {
  overflow-y: scroll !important;
  // scrollbar-width: none;
}

.sub_txt {
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.25rem;
  font-weight: 300;
  width: 100%;
  letter-spacing: 1px;
  font-family: var(--helveticaneueltarabicLight);

  @media #{$media-820} {
    font-size: 1.6rem;
  }

  @media #{$media-767} {
    font-size: 1.8rem;
  }
}

.link_round {
  align-self: flex-end;
  width: 46px;
  height: 46px;
  border-radius: 100%;
  background: rgba(32, 32, 46, 0.3);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px;

  &:hover {
    background: rgba(32, 32, 46, 1);
  }


  @media #{$media-820} {
    width: 35px;
    height: 35px;
    padding: 13px
  }
}

.custom_btn {
  top: 50%;
  // margin-top: -30px;
  cursor: pointer;
  outline: none;
  position: absolute;
  z-index: 800;
  padding: 1.3%;
  width: 60px;
  height: 60px;
  background: $white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;

  &:hover {
    background: $blue;
    border-color: $blue ;

    img {
      filter: invert(1);
      transition: all 0.3s ease;

    }
  }

  @media #{$media-1600} {
    width: 55px;
    height: 55px;
    padding: 1%;
  }

  @media #{$media-1024} {
    width: 45px;
    height: 45px;
  }

  @media #{$media-768} {
    width: 40px;
    height: 40px;
  }
}

.custom_next {
  inset-inline-start: 27px;

  img {
    @media #{$media-767} {
      width: auto;
    }
  }
}

.custom_prev {
  inset-inline-end: 27px;

  img {
    @media #{$media-767} {
      width: auto;
    }
  }
}

.custom_btn_border {
  border: 1px solid blue;
  background-color: transparent !important;
}

.min_100 {
  min-height: 100vh;
}

.home_about_sec {
  min-height: 100vh;
}

.home_about_sec {
  min-height: 100vh;
  position: relative;
  z-index: 0;

  @media #{$media-1024} {
    min-height: 75vh;
  }

  @media #{$media-820} {
    min-height: 681px;
    padding-bottom: 0;
  }

  @media #{$media-700} {
    background-image: none !important;
    padding-bottom: 35px;
    min-height: unset;
  }

  &:after {
    content: '';
    position: absolute;
    width: 100%;
    top: 0;
    bottom: 0;
    height: 100%;
    background: var(--image) top center no-repeat;
    background-size: contain;
    background-position: left center;
    z-index: 1;

    @media #{$media-700} {
      background: none !important;
    }
  }
}


.text_white {

  h2,
  p {
    color: $white;
  }
}

@media #{$media-820} {

  .space_btw {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center !important;
  }

  .flex_wrap {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }
}

.mobile_hide {
  @media #{$media-767} {
    display: none;
  }
}

.title_block_row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

.position_unset {
  position: unset;
}

.slider_buttion_white {
  border-color: #CDCDCD;

  &:hover {
    img {
      filter: invert(0) !important;
      transition: all 0.3s ease;
    }
  }
}

.slider_but_ul {
  margin: 0 -5px;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    padding: 0% 5px;

    span {
      display: inline-flex;
      padding: 30%;
    }
  }
}

.awards_slider {
  width: 27% !important;
  height: auto !important;
  display: flex !important;


  @media #{$media-1024} {
    width: 33% !important;
  }

  @media #{$media-820} {
    width: 37% !important;
  }

  @media #{$media-767} {
    width: 80% !important;
  }
}

.history_slider {
  padding: 0 2%;

  @media #{$media-1024} {
    padding: 0 3% 35px 3%;
  }
}

.lanaguage_switch {
  font-size: 1rem;
  font-family: var(--almarai-arabic);
  color: $white;
  font-weight: 500;

  @media #{$media-1024} {
    font-size: 13px;

  }
}

.privacy_section {
  .privacy_container {
    h2 {
      font-size: 30px;
      color: #272727;
      margin-bottom: 25px;
      display: block;

      @media #{$media-767} {
        font-size: 24px;
        margin-bottom: 20px;
      }
    }

    p {
      @media #{$media-767} {
        font-size: 14px;
      }
    }
  }
}

.advisory_core_title {
  font-size: 1.8rem;

  @media #{$media-767} {
    font-size: 2.5rem;
  }
}

.diamond_hm {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2%;
}

.view_all_bottom {
  display: flex;
  justify-content: flex-end;
}

.home_news_sec {
  .pt_40 {
    @media #{$media-1024} {
      padding-top: 25px;
    }

    @media #{$media-500} {
      padding-top: 20px;
    }
  }
}

.external_article_button_wrapper {
  margin-top: 25px;

  .external_article {
    height: 50px;
    padding-left: 30px;
    padding-right: 30px;
    border-radius: 35px;
    display: inline-block;
    justify-content: center;
    font-size: 15px;
    display: inline-flex;
    font-family: var(--helveticaNeueMedium);
    align-items: center;
    line-height: 20px;
    border: 1px solid #fff;
    color: #fff;
    background: transparent;
    transition: 0.4s all ease;

    @media #{$media-1600} {
      font-size: 1.2rem;
      padding-left: 25px;
      padding-right: 25px;
    }

    @media #{$media-767} {
      font-size: 13px;
      height: 43px;
    }

    &:hover {
      background: #5e45ff;
      color: #fff;
    }

    img {
      margin-inline-start: 10px;

      @media #{$media-1600} {
        width: 13px;
      }
    }
  }
}

.not_found_sec {
  background: url('/images/history_new_bg.png');
  background-repeat: no-repeat;
  background-size: cover;

  .not_found {
    text-align: center;
    min-height: 100dvh;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    ;

    h1 {
      font-size: 6rem;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 20px;

      @media #{$media-1600} {
        margin-bottom: 15px;

      }
    }

    h4 {
      font-size: 2rem;
      font-weight: 400;
      color: #ffffff;
      margin-bottom: 20px;

      @media #{$media-1600} {
        margin-bottom: 15px;

      }
    }
  }
}

.sitemp_sec {
  background-color: #F5F3FF;

  .sitemap_list {
    a {
      color: #212142;
      font-size: 1.25rem;
      line-height: 1.825rem;
      font-family: var(--helveticaNeue);

      &:hover {
        color: #5e45ff;
      }
    }

    >li {
      position: relative;
      padding-inline-start: 30px;

      ::after {
        position: absolute;
        content: "";
        height: 10px;
        width: 10px;
        background-color: #5e45ff;
        inset-inline-start: 0;
        top: 0.625rem;
        border-radius: 50%;
      }

      ul {
        padding-inline-start: 20px;
        margin: 10px 0;

      }
    }
  }
}
.marq_data{
  display: flex;
  align-items: center;
  gap: 8px;
  &+.marq_data{
    margin-inline-start: 8px;
  }
}



.rtl .lanaguage_switch {
  font-size: 1rem;
  font-family: var(--helveticaNeueMedium);
  color: #fff;
  font-weight: 500;

  @media #{$media-1024} {
    font-size: 15px;

  }
}

.rtl {
  .banner_txt_block {
    width: auto !important;
    display: flex !important;
    flex-direction: column !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    margin-right: auto;

    @media #{$media-450} {
      margin-right: unset;
    }

    &.banner_img_txt_block {
      width: 100% !important;
      display: flex !important;
      flex-direction: column !important;
      flex-wrap: wrap !important;
      align-items: center !important;
      margin-right: unset;
    }
  }
}


.rtl .button img {
  transform: scaleX(-1);
}

.rtl .home_about_sec {
  background-position: right center !important;
}

.rtl .home_about_sec {
  &::after {
    transform: scaleX(-1);
  }

}

.rtl .impact_arrow {
  transform: scaleX(-1);
}

.rtl .impact_link img {
  transform: scaleX(-1);
}

.rtl .contact_form_input_submit_btn {
  font-family: "Almarai", sans-serif !important;

  img {
    transform: scaleX(-1);
  }
}

.rtl select.fld_01 {
  background-position: 5% 50%;
}

.rtl .link_round {
  img {
    transform: scaleX(-1);
  }
}

.rtl .menu_title {
  line-height: 26px;
}

.rtl {
  .abt_vector {
    transform: scaleX(-1);
  }
}

// .rtl .overview_swiper_btn_sec{
//   flex-direction: row-reverse;
// }
.rtl .mission_vission_block_inside:after {
  transform: translateY(-50%) scaleX(-1) !important;
}

.rtl .custom_btn {
  img {
    transform: scaleX(-1) !important;
  }
}

.rtl .custom_latesst_next {
  img {
    transform: rotate(0deg) !important;
  }
}

.rtl .solution_set p {
  line-height: 1.8rem;
}

.rtl .eng_prg_link {
  img {
    transform: scaleX(-1);
  }
}

.rtl .digi_slider {
  background: linear-gradient(272deg, #252242 23%, #352c78) !important;

  h3 {
    line-height: 35px;

    @media #{$media-820} {
      line-height: 31px !important;
    }
  }
}

.rtl .learnmore_btn_img {
  transform: scaleX(-1) !important;
}

.rtl .digi_arws img {
  transform: scaleX(-1) !important;
}

.rtl .digi_side_bar_footer_btn img {
  transform: scaleX(-1) !important;
}

.rtl .side_image_logo {
  transform: scaleX(-1) !important;
}

.rtl .contact_form_input_submit_btn img {
  transform: scaleX(-1) !important;
}

.rtl .careers_latest_opening_section body {
  direction: rtl;
}

.rtl .inner_banner_breadcrumb_head {
  &::after {
    inset-inline-start: 0px !important;
  }
}

.rtl .about_our_team_content_sec {
  background-position: left top !important;
}

.rtl .position_01 {
  top: 14% !important;

  h3 {
    max-width: unset !important;
  }
}

.rtl .position_02 {
  top: 72% !important;
}

.rtl .position_03 {
  top: 11% !important;
}

.rtl .impact_arrow {
  top: 0;
}

.rtl .tab_txt_block {
  p {
    line-height: 23px;
  }
}

.rtl {

  .overview_left_sec h3,
  .overview_swiper_card_head_sec h4,
  .about_title_sec h3,
  .time_line_block h3,
  .time_line_block h4,
  .about_our_team_section h3,
  .about_our_team_left_title h4,
  .about_our_team_content_profile_data_sec h4,
  .about_recognition_swiper_card_title h5,
  .public_engagement_sub_domain_container h3,
  .digital_what_we_offer_container h3,
  .latest_publications_header h3,
  .news_center_header h3,
  .news_center_left_card_data h4,
  .knowledge_center_header h3,
  .knowledge_center_left_card_data h4,
  .investor_relation_card_block_head_sec h4,
  .investor_relation_media_resource_head_sec h4,
  .share_info_company_announcement_container h4,
  .txt_block h3 {
    font-weight: 600 !important;
  }

  .rtl .about_our_team_left_title h6 {
    font-weight: 400 !important;
  }

  .inner_banner_container h2 {
    font-weight: 700 !important;
    line-height: 125% !important;
  }

}

.rtl .community_sec {
  line-height: 125%;
}

.rtl .txt_block h3 {
  max-width: unset !important;
}

.rtl .footer_contact {
  img {
    transform: scaleX(-1) !important;
  }
}

.rtl .phone_icon {
  img {
    transform: scaleX(-1) !important;
  }
}


.rtl .fld_01::placeholder {
  line-height: 1.2rem;
  height: 40px;
}

.rtl .fld_01::-ms-input-placeholder {
  /* Edge 12 -18 */
  line-height: 1.2rem;
  height: 40px;
}

.rtl .fld_01 {
  font-family: "Almarai", sans-serif !important;
  line-height: 1.2rem;
}

.rtl .learnmore_btn {
  font-family: "Almarai", sans-serif !important;
}

.rtl select.fld_01 {
  line-height: 40px;
}

.rtl .value_head {
  margin-bottom: 10px;
}

.rtl .overview_swiper_card_data_sec li p {
  align-self: flex-start !important;
  text-align: start !important;
}

.rtl .margin_top_arabic {
  padding-top: 10px;
}

.team_arabic {
  display: none;
}

.rtl .team_english {
  display: none;
}

.rtl .team_arabic {
  display: block;
}

.rtl .external_article {
  img {
    transform: rotate(180deg);
  }
}
.rtl{
  .overview_left_cl h3{
    line-height: 5.4rem;
    @media #{$media-1024} {
      line-height: 5rem;
    }
  }
}
.rtl .stock_info{
  h5,span{
    font-weight: 700 !important;
  }
}
// .rtl .marq_data{
//   margin-inline-start: 0px;
// }
.rtl .marquee_item{
  font-family:  "Almarai", sans-serif !important;
  direction: rtl;
  span{
    // margin-right: 5px;
    display: inline-block;
    &:first-child{
      // margin: 0 10px;
    }
  }
}
.rtl .marq_data{
  span{
    &:first-child{
      // margin: 0 5px;
    }
  }
}