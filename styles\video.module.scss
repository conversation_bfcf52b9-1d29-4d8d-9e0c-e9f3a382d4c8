@import "variable", "base";

 .video_block{ height: 719px; width: 100%; display: flex;  flex-wrap: wrap; justify-content: center; align-items: center;
  position: relative;
  @media #{$media-1440} {
    height: 650px;
  }
  @media #{$media-1024} {
    height: 600px;
  }
  @media #{$media-767} {
    height: 235px;
  }
  .video_play_but{ cursor: pointer; display: flex; flex-wrap: wrap; align-items: center; justify-content: center; width: 126px; height: 126px; background: $blue; border-radius: 100%;
    img{ width: 25%;}
  
    &:hover{
      background: $blueh;
    }

    @media #{$media-820} {
      width:100px; height:100px;
    }
    @media #{$media-700} {
      width:80px; height:80px;
    }
  }
    .mute_btn{
      position: absolute;
      bottom: 20px;
      inset-inline-end: 40px;
      z-index: 1;
      cursor: pointer;
    }


}
.videoPlayer{
   width: 100%;
   height: 720px;
   position: absolute;
   object-fit: cover;
   @media #{$media-1440} {
    height: 650px;
  }
   @media #{$media-1024} {
    height: 600px;
  }
  @media #{$media-767} {
    height:235px;
  }
}