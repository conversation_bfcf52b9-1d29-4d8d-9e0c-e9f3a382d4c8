import React, { useEffect, useRef, useState } from "react";
import style from "@/styles/TabSectionInsights.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";
import parse from "html-react-parser";

const TabSectionInsights = ({ tabs, slug }) => {
  const route = useRouter();
  const { locale, pathname } = route;

  // Function to remove locale from pathname for comparison
  const normalizePath = (path) => {
    return path.startsWith(`/${locale}`) ? path.replace(`/${locale}`, "") : path;
  };

  const tabRefs = useRef([]);
  const tabWrapperRef = useRef(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

   useEffect(() => {
       const checkOverflow = () => {
         if (tabWrapperRef.current) {
           const tabWrapper = tabWrapperRef.current;
           setIsOverflowing(tabWrapper.scrollWidth > tabWrapper.clientWidth);
         }
       };
   
       checkOverflow(); // Check initially
       window.addEventListener("resize", checkOverflow); // Check on resize
   
       return () => {
         window.removeEventListener("resize", checkOverflow); // Cleanup event listener
       };
     }, [tabs]);

  useEffect(() => {
    const activeTab = tabRefs.current.find(
      (tab) => tab && tab.classList.contains(style.tab_active)
    );

    if (activeTab && tabWrapperRef.current) {
      tabWrapperRef.current.scrollTo({
        left: activeTab.offsetLeft - tabWrapperRef.current.offsetWidth / 2 + activeTab.offsetWidth / 2,
        behavior: "smooth",
      });
    }
  }, [pathname]);


  return (
    <div>
      {/* -----------Tab section----------- */}
      
      <section className={`${style.tab_section_insights}`}>
        <div className={`${comon.wrap}`}>
          <div  className={`${style.tab_wrapper} ${isOverflowing ? style.tab_wrapper_overflowing : ""}`}
          ref={tabWrapperRef} // Set the ref for scrolling
            data-aos="fade-up"
            data-aos-duration="1000" >
          <ul >
           
               {tabs && tabs.map((data, index) => (
              <li key={index} ref={(el) => (tabRefs.current[index] = el)} >
                   {data?.ir_menu &&                      
                     <Link href={data.ir_menu?.url} 
                     className={
                      normalizePath(data?.ir_menu?.url) === normalizePath(`/insights/${slug}`) ||
                      normalizePath(data?.ir_menu?.url) === normalizePath(pathname)
                        ? style.tab_active
                        : ""
                    }    /* to get active class in arabic as well*/


                      //  className={
                      //     (data?.ir_menu?.url === `/insights/${slug}` || data?.ir_menu?.url === pathname)
                      //       ? style.tab_active
                      //       : ''
                      //   }
                     
                       target={data?.ir_menu?.target}
                       onClick={(e) => {
                        if (tabRefs.current[index] && tabWrapperRef.current) {
                          tabWrapperRef.current.scrollTo({
                            left:
                              tabRefs.current[index].offsetLeft -
                              tabWrapperRef.current.offsetWidth / 2 +
                              tabRefs.current[index].offsetWidth / 2,
                            behavior: "smooth",
                          });
                        }
                      }}
                       >    
                        {data?.ir_menu?.title && parse(data?.ir_menu?.title)}
                  </Link>
                }
              </li>
            ))}            
          </ul>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TabSectionInsights;
