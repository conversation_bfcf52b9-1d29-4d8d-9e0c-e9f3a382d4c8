import { getAwardsPosts } from "./lib/server/publicServices";

const CONSTANTS = {
  
  IrMenuLinks: {
    IrMenus: `wp-json/options/v2/alloptions`,
  },

  Themeoptions: {
    ThemeoptionsApi: `wp-json/options/v2/alloptions`,
  },

  HomePage: {
    HomeApi: `wp-json/wp/v2/pages/2/`,
  },
  HomeArPage: {
    HomeArApi: `wp-json/wp/v2/pages/1641/`,
  },
  
  AboutPage: {
    AboutApi: `wp-json/wp/v2/pages/21/`,
  },
  
  AboutArPage: {
    AboutArApi: `wp-json/wp/v2/pages/1629/`,
  }, 

  AwardsPage: {
    AwardsApi: `wp-json/wp/v2/pages/2330/`,
  },
  
  AwardsArPage: {
    AwardsArApi: `wp-json/wp/v2/pages/2344/`,
  }, 
  
  TeamsTaxonomy: {
    TeamsTaxonomyApi: `wp-json/wp/v2/team_category/`,
  },
  
  ClientPage: {
    ClientApi: `wp-json/wp/v2/pages/343/`,
  },
  
  ClientArPage: {
    ClientArApi: `wp-json/wp/v2/pages/1651/`,
  },
  
  AdvisoryPage: {
    AdvisoryApi: `wp-json/wp/v2/pages/654/`,
  },

  AdvisoryArPage: {
    AdvisoryArApi: `wp-json/wp/v2/pages/1632/`,
  },

  IrPage: {
    IrApi: `wp-json/wp/v2/pages/712/`,
  },

  IrArPage: {
    IrArApi: `wp-json/wp/v2/pages/1646/`,
  },
  
  IrmediaPage: {
    IrmediaApi: `wp-json/wp/v2/pages/796/`,
  },

  IrmediaArPage: {
    IrmediaArApi: `wp-json/wp/v2/pages/1649/`,
  },

  IrCompanyPage: {
    IrCompanyApi: `wp-json/wp/v2/pages/770/`,
  },

  IrCompanyArPage: {
    IrCompanyArApi: `wp-json/wp/v2/pages/1647/`,
  },

  IrshareinfoPage: {
    IrshareinfoApi: `wp-json/wp/v2/pages/751/`,
  },

  IrshareinfoArPage: {
    IrshareinfoArApi: `wp-json/wp/v2/pages/1650/`,
  },

  IrInvestorsResPage: {
    IrInvestorsResApi: `wp-json/wp/v2/pages/777/`,
  },

  IrInvestorsResArPage: {
    IrInvestorsResArApi: `wp-json/wp/v2/pages/1648/`,
  },

  DigitalPage: {
    DigitalApi: `wp-json/wp/v2/pages/413/`,
  },

  DigitalArPage: {
    DigitalArApi: `wp-json/wp/v2/pages/1638/`,
  },

  CareersPage: {
    CareersApi: `wp-json/wp/v2/pages/577/`,
  },

  CareersArPage: {
    CareersArApi: `wp-json/wp/v2/pages/1635/`,
  },

   AwardsPost: {
    AwardsPostapi: `wp-json/wp/v2/our_awards/`,
  },

  TeamsPost: {
    TeamsPostapi: `wp-json/wp/v2/our_teams/`,
  },

  InvestorsResourcesPost: {
    InvestorsResourcesPostapi: `wp-json/wp/v2/investors_resources/`,
  },
  InvestorsTaxonomy: {
    InvestorsTaxonomyApi: `wp-json/wp/v2/resources_category/`,
  },

  MediaResourcesPost: {
    MediaResourcesPostapi: `wp-json/wp/v2/media_resources/`,
  },

  CompanyPost: {
    CompanyPostapi: `wp-json/wp/v2/company_announcement/`,
  },

  DigitalPost: {
    DigitalPostApi: `wp-json/wp/v2/digital_products/`,
  },


  AdvisoryTaxonomy: {
    AdvisoryTaxonomyApi: `wp-json/wp/v2/advisory_category/`,
  },
   ProductstaxPage: {
    ProductstaxApi: `wp-json/wp/v2/advisory_category/`,
  },
   
   AdvisoryPost: {
    AdvisoryPostApi: `wp-json/wp/v2/our_advisory/`,
  },
   
  InsightPost: {
    InsightPostApi: `wp-json/wp/v2/posts/`,
  },

  Insightstaxonamy: {
    InsightstaxonamyApi: `wp-json/wp/v2/categories/`,
  },
  
  InsightsPage: {
    InsightsApi: `wp-json/wp/v2/pages/627/`,
  },

  InsightsArPage: {
    InsightsArApi: `wp-json/wp/v2/pages/1644/`,
  },
    
  topictaxonamy: {
    topictaxonamyApi: `wp-json/wp/v2/topic_category/`,
  },
  industriestaxonamy: {
    industriestaxonamyApi: `wp-json/wp/v2/industries_category/`,
  },

  PrivacyPage: {
    PrivacyApi: `wp-json/wp/v2/pages/3/`,
  },
  PrivacyArPage: {
    PrivacyArApi: `wp-json/wp/v2/pages/1652/`,
  },

  TermsPage: {
    TermsApi: `wp-json/wp/v2/pages/1922/`,
  },
  TermsArPage: {
    TermsArApi: `wp-json/wp/v2/pages/1923/`,
  },

  CookiesPage: {
    CookiesApi: `wp-json/wp/v2/pages/1927/`,
  },  
  CookiesArPage: {
    CookiesArApi: `wp-json/wp/v2/pages/1928/`,
  },

};

export default CONSTANTS;
