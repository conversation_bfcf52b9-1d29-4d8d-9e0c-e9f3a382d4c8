@import "variable", "base";

.grow_sec{
    h3{
        color: #413887;
    }
}
.careers_latest_opening_section {
    width: 100%;
    background-color: #f5f3ff;

    .careers_latest_opening_container {
        width: 100%;
    }

    h3 {
        color: #2a2656;
        text-align: left;
        font-size: 3.125rem;
        font-weight: 400;
        line-height: normal;
        display: inline-block;
    }

    .careers_latest_opening_List {
        list-style: none;


        // {/* ======Form Section======== */}

        .careers_latest_opening_List_form_sec {
            width: 100%;
            box-sizing: border-box;
            padding: 30px 2.5%;
            border-radius: 10px;
            z-index: 0;
            background-image: linear-gradient(180deg,
                    rgba(186, 178, 234, 0.2) 0%,
                    rgba(171, 163, 223, 1) 100%);
            position: relative;

            &::after {
                z-index: 0;

                position: absolute;
                content: "";
                height: calc(100% - 2px);
                width: calc(100% - 2px);
                top: 1px;
                left: 1px;
                background-color: white;
                border-radius: 9px;
            }

            ul {
                display: flex;
                flex-wrap: wrap;
                z-index: 5;
                position: relative;
                row-gap: 20px;
                column-gap: 1%;
                @media #{$media-820} {
                    justify-content: space-between;
                    column-gap: 0.5%;
                }


                .careers_latest_opening_List_form_search {
                    list-style: none;
                    width: 100%;
                    background: #ffffff;
                    border-radius: 30px;
                    border: 1px solid #dbdbdb;
                    position: relative;

                    input {
                        width: 100%;
                        padding: 20px 10px 20px 5%;
                        background-color: white;
                        color: black;
                        display: inline-block;
                        border: 0;
                        border-radius: 30px;

                        color: rgba(79, 79, 79, 0.774);
                        font-size: 15px;
                        line-height: 100%;
                        font-weight: 300;

                        &:focus {
                            outline: none;
                        }

                        &::placeholder {
                            color: rgba(79, 79, 79, 0.3);
                        }
                        @media #{$media-1440} {
                            padding: 16px 10px 16px 5%;
                          }
                          @media #{$media-1024} {
                            padding: 16px 10px 16px 60px;
                          }
                          @media #{$media-767} {
                            font-size: 14px;
                          }
                    }

                    .careers_latest_opening_List_form_search_icon {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: 2%;
                        width: 26px;

                        img {
                            display: block;
                            object-fit: cover;
                            height: auto;
                            width: 100%;
                        }
                        
                    }
                }

                // ===============Dropdown====

                .careers_latest_opening_List_form_dropdown {
                    list-style: none;
                    width: 24%;
                    background: #ffffff;
                    border-radius: 30px;
                    border: 1px solid #dbdbdb;
                    position: relative;
                    @media #{$media-820} {
                        width: 49%;
                      }
                      @media #{$media-600} {
                        width: 100%;
                      }

                    select {
                        width: 100%;
                        padding: 20px 15% 20px 8%;
                        background-color: white;
                        color: black;
                        display: inline-block;
                        border: 0;
                        border-radius: 30px;

                        color: rgba(79, 79, 79, 0.774);
                        font-size: 15px;
                        line-height: normal;
                        font-weight: 300;
                        appearance: none;

                        -webkit-appearance: none;
                        -moz-appearance: none;
                        background-image: none;

                        &:focus {
                            outline: none;
                        }

                        &::placeholder {
                            color: rgba(79, 79, 79, 0.3);
                        }
                        @media #{$media-1440} {
                            padding: 16px 15% 16px 8%;
                          }
                          @media #{$media-767} {
                            font-size: 14px;
                            line-height: 18px;
                          }
                    }

                    .careers_latest_opening_List_form_search_icon {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        right: 7%;
                        width: 15px;

                        img {
                            display: block;
                            object-fit: cover;
                            height: auto;
                            width: 100%;
                        }
                    }

                    .rotate {
                        transform: translateY(-50%) rotate(180deg); // Rotates the icon 180 degrees
                    }
                }
            }
        }


        // ======Job card Section=======


        .careers_latest_opening_List_job_card_sec {
            width: 100%;
            box-sizing: border-box;
            padding: 30px 2.5%;
            margin-top: 30px;
            border-radius: 10px;
            z-index: 0;
            background-image: linear-gradient(180deg,
                    rgba(186, 178, 234, 0.2) 0%,
                    rgba(171, 163, 223, 1) 100%);
            position: relative;

            ul {
                z-index: 5;
                list-style: none;
                position: relative;
                display: flex;
                gap: 5%;
                @media #{$media-400} {
                    gap: 2%;
                }

                li {

                    width: auto;
                    padding-top: 20px;
                    padding-left: 2%;
                    padding-right: 2%;
                    @media #{$media-1440} {
                        padding-top: 15px;
                      }
                      @media #{$media-600} {
                        padding-top: 10px;
                      }


                    &:nth-child(1) {
                        padding: 0;
                    }

                    &:nth-child(2) {
                        flex-grow: 1;
                        .career_info{
                            display: flex;
                            justify-content: space-between;
                            gap: 5%;
                            @media #{$media-768} {
                                flex-direction: column;
                              }
                            .career_title{
                                width: 35%;
                                @media #{$media-768} {
                                    width: 100%;
                                }
                            }
                            .career_description{
                                width: 60%;
                                @media #{$media-768} {
                                    width: 100%;
                                    margin-top: 15px;
                                }
                            }
                            
                        }
                    }

                    &:nth-child(3) {
                        display: flex;
                        align-items: center;
                        padding-top: 0;
                    }

                    .careers_latest_opening_List_job_icon {
                        width: 120px;
                        @media #{$media-1440} {
                            width: 105px;
                          }
                          @media #{$media-1024} {
                            width: 90px;
                          }
                          @media #{$media-767} {
                            width: 65px;
                          }
                          @media #{$media-400} {
                            width: 55px;
                          }

                        img {
                            display: block;
                            object-fit: cover;
                            height: auto;
                            width: 100%;
                        }
                    }

                    span {
                        color: #53676e;
                        font-family: var(--helveticaNeue);
                        font-size: 1rem;
                        font-weight: 500;
                        @media #{$media-1024} {
                            font-size: 1.1rem;
                          }
                          @media #{$media-820} {
                            font-size: 14px;
                             @media #{$media-768} {
                            font-size: 14px;
                            line-height: 18px;
                          }
                          }

                    }

                    h4 {
                        color: #0e0e0e;
                        font-family: var(--helveticaNeue);
                        font-size: 1.563rem;
                        line-height: 2.25rem;
                        font-weight: 700;
                        display: block;
                        margin-top: 10px;
                        @media #{$media-1440} {
                            margin-top: 5px;
                          }
                          @media #{$media-1024} {
                            font-size:20px;
                            line-height: 26px;
                          }
                    }

                    p {
                         font-family: var(--helveticaNeue);
                        color: rgba(0,0,0,0.8);
                        font-size: 1rem;
                        line-height: 1.563rem;
                        letter-spacing: 0.03em;
                        font-weight: 400;
                        @media #{$media-1024} {
                            font-size: 1.1rem;
                          }
                          @media #{$media-820} {
                            font-size: 14px;
                            line-height: 20px;
                          }
                    }

                    .careers_latest_opening_btn_link {
                        border-radius: 50%;
                        background-color: #F5F3FF;
                        height: 50px;
                        width: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        @media #{$media-1440} {
                            height: 45px;
                        width: 45px;
                          }

                    }





                }
            }

            &::after {
                z-index: 0;

                position: absolute;
                content: "";
                height: calc(100% - 2px);
                width: calc(100% - 2px);
                top: 1px;
                left: 1px;
                background-color: white;
                border-radius: 9px;
            }

        }
    }
}