import React from "react";
import common from "@/styles/comon.module.scss";
import Buttion from "@/component/buttion/Buttion";
import buttion from "@/styles/buttion.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";

const NotFound = () => {
  const router = useRouter();
  return (
    <div className={common.not_found_sec}>
      <div className={`${common.container} ${common.not_found}`}>
        <h1>404</h1>
        <h4>
          {router.locale == "ar"
            ? "لم يتم العثور على هذه الصفحة."
            : "This page could not be found."}
        </h4>
        <div className={common.mt_20}>
        <Buttion
          aosType="fade-up"
          aosDuration={1500}
          text={router.locale == "ar"
            ? "العودة الى الصفحة الرئيسية"
            : "Back to Home"}
          href={"/"}
          moduleClass={buttion.strock}
          imageSrc="/images/buttion_arrow_white.svg"
        />
        </div> 
      </div>
    </div>
  );
};

export default NotFound;
