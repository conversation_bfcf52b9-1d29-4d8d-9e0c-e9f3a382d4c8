import InnerBanner from "@/component/InnerBannernews";
import React, { useEffect, useState } from "react";
import style from "@/styles/Insights.module.scss";
import filterstyle from "@/styles/innerBanner.module.scss";
import comon from "@/styles/comon.module.scss";
import Buttion from "@/component/buttion/Buttion";
import buttion from "@/styles/buttion.module.scss"; // Assuming you are using a CSS module for button styles
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";

import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";

// import required modules
import { Navigation, Pagination } from "swiper/modules";
import InquireNow from "@/component/InquireNow";
import TabSectionInsights from "@/component/TabSectionInsights";

import Yoast from "@/component/yoast";
import {
  getInsightspage,
  getInsightsPosts,
  getInsightstaxonamyList,
  getIrMenuLinks,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import { parseISO, format } from "date-fns";

const DateConvert = (datetimeString) => {
  if (!datetimeString || typeof datetimeString !== "string") {
    console.error("Invalid input for DateConvert:", datetimeString);
    return "Invalid Date";
  }

  try {
    const parsedDate = parseISO(datetimeString);
    const formattedDate = format(parsedDate, "dd-MM-yyyy");
    return formattedDate;
  } catch (error) {
    console.error("Error parsing or formatting date:", error);
    return "Invalid Date";
  }
};

const Insights = (props) => {
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  //const [tabActive, setTabactive] = useState(null);
  //const [selectedTab, setSelectedTab] = useState(null);

  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";
  const newsurl = router.locale === "ar" ? "/ar" : "";

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

 




  const yoastData = props?.InsightsData?.yoast_head_json;

  if (!props.InsightsData) {
    return null;
  }

  return (
    <div>
      <div className={style.banner_sec}>
        {yoastData && <Yoast meta={yoastData} />}

        {props &&
        props?.InsightsData &&
        props?.InsightsData?.acf &&
        props?.InsightsData?.acf?.banner_title &&
        (props?.InsightsData?.acf?.mob_banner_image ||
          props?.InsightsData?.acf?.banner_image ||
          props?.InsightsData?.acf?.breadcrumbs ||
          props?.InsightsData?.acf?.banner_viedo ||
          props?.InsightsData?.acf?.banner_type) ? (
          <InnerBanner
            pagename={props?.InsightsData?.acf?.banner_title}
            breadcrumb1={
              props?.InsightsData?.acf?.active_breadcrumbs === "yes"
                ? props?.InsightsData?.acf?.breadcrumbs
                : ""
            }
            background={`${
              isMobile
                ? props?.InsightsData?.acf?.mob_banner_image?.url
                : props?.InsightsData?.acf?.banner_image?.url
            }`}
            videoSrc={props?.InsightsData?.acf?.banner_viedo?.url}
            banner_type={props?.InsightsData?.acf?.banner_type}
            backgroundType={"icon_bg"}
            //tabShow={true}
            // {...FilterValues}
          />
        ) : null}            
      </div>
 
      {props &&
        props?.IRMenuData &&
                props?.IRMenuData?.insights_menu &&
        <TabSectionInsights
        tabs={props?.IRMenuData?.insights_menu}
        slug={props?.slug} />
            }
      {/* ========News Center section======== */}
      {props &&
      props?.InsightsData &&
      props?.InsightsData?.acf &&
      props?.newsCenterArray.length > 0 &&
      (props?.InsightsData?.acf?.news_center_title ||
        props?.InsightsData?.acf?.news_center_button) ? (
        <section className={`${style.news_center_section}`}>
          <div
            className={`${style.news_center_container} ${comon.wrap} ${comon.pt_130}  ${comon.pb_130}`}
          >
            <div className={`${style.news_center_header} ${comon.news_center_header}`}>
              {props?.InsightsData?.acf?.news_center_title && (
                <h3 data-aos="fade-up" data-aos-duration="1000">
                  {props?.InsightsData?.acf?.news_center_title &&
                    parse(props?.InsightsData?.acf?.news_center_title)}
                </h3>
              )}
              {props?.InsightsData?.acf?.news_center_button && (
                <Buttion
                  text={props?.InsightsData?.acf?.news_center_button?.title} // Button text
                  href={props?.InsightsData?.acf?.news_center_button?.url} // Navigation target for the button
                  moduleClass={buttion.blue} // Custom button styling
                  imageSrc="/images/buttion_arrow.svg" // Image for button
                />
              )}
            </div>
            <div className={`${style.news_center_card_sec}`}>
              {props?.newsCenterArray && props?.newsCenterArray.length > 0 && (
                <>
                  {/* Left Card Section */}
                  <div
                    className={`${style.news_center_left_card_sec}`}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    <div className={`${style.news_center_left_card}`}>
                      {props?.newsCenterArray[0].acf.listing_tag && (
                        <div
                          className={`${style.news_center_left_card_notification}`}
                        >
                          {props?.newsCenterArray[0].acf.listing_tag &&
                            parse(props?.newsCenterArray[0].acf.listing_tag)}
                        </div>
                      )}
                      {props?.newsCenterArray[0].acf.insights_page_image ||
                      props?.newsCenterArray[0].acf.listing_image ? (
                        <div className={`${style.news_center_left_card_img}`}>
                          <Image
                            src={
                              props?.newsCenterArray[0].acf.insights_page_image
                                ?.url ||
                              props?.newsCenterArray[0].acf.listing_image?.url
                            }
                            height={400}
                            width={900}
                            alt=""
                          />
                        </div>
                      ) : (
                        <div className={`${style.news_center_left_card_img}`}>
                          <Image
                            src={"/images/noimage-insights.png"}
                            height={400}
                            width={900}
                            alt=""
                          />
                        </div>
                      )}

                      <div className={`${style.news_center_left_card_data} ${comon.news_center_left_card_data}`}>
                        <Link
                           href={
                              props?.newsCenterArray[0]?.acf?.external_news_link?.url
                                ? props?.newsCenterArray[0]?.acf?.external_news_link?.url
                                : `${newsurl}/insights/news-center/${props?.newsCenterArray[0].slug}`
                            }
                            target={
                              props?.newsCenterArray[0]?.acf?.external_news_link
                              ? props?.newsCenterArray[0]?.acf?.external_news_link?.target
                              : "_self"
                            }                        
                        >
                          {props?.newsCenterArray[0].date && (
                            <span>
                              {DateConvert(props?.newsCenterArray[0].date)}
                            </span>
                          )}
                          {props?.newsCenterArray[0].title && (
                            <h4>
                              {parse(props?.newsCenterArray[0].title.rendered)}
                            </h4>
                          )}
                          {props?.newsCenterArray[0].acf.short_content && (
                            <p>
                              {props?.newsCenterArray[0].acf.short_content
                                .replace(/<\/?[^>]+(>|$)/g, "")
                                .substring(0, 100)}
                              ...
                            </p>
                          )}
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Right Card Section */}
                  <div className={`${style.news_center_right_card_sec}`}>
                    {props?.newsCenterArray.slice(1).map((item) => (
                      <div
                        key={item.id}
                        className={`${style.news_center_right_card}`}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <Link
                          href={
                              item?.acf?.external_news_link?.url
                                ? item?.acf?.external_news_link?.url
                                : `${newsurl}/insights/news-center/${item.slug}`
                            }
                            target={
                              item?.acf?.external_news_link
                              ? item?.acf?.external_news_link?.target
                              : "_self"
                            }                            
                        >
                          {item.acf.listing_tag && (
                            <div
                              className={`${style.news_center_right_card_notification}`}
                            >
                              {item.acf.listing_tag &&
                                parse(item.acf.listing_tag)}
                            </div>
                          )}
                          {item.date && <span>{DateConvert(item.date)}</span>}
                          {item.title.rendered && (
                            <p>
                              {item.title.rendered &&
                                parse(item.title.rendered)}
                            </p>
                          )}
                        </Link>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </section>
      ) : null}

      {/* ========Knowledge Center section======== */}
      {props &&
      props?.InsightsData &&
      props?.InsightsData?.acf &&
      props?.knowledgecenterArray.length > 0 &&
      (props?.InsightsData?.acf?.knowledge_center_title ||
        props?.InsightsData?.acf?.knowledge_center ||
        props?.knowledgecenterArray) ? (
        <section className={`${style.knowledge_center_section}`}>
          <div
            className={`${style.knowledge_center_container} ${comon.wrap} ${comon.pt_130}  ${comon.pb_130}`}
          >
            <div className={`${style.knowledge_center_header} ${comon.knowledge_center_header}`}>
              {props?.InsightsData?.acf?.knowledge_center_title && (
                <h3 data-aos="fade-up" data-aos-duration="1000">
                  {props?.InsightsData?.acf?.knowledge_center_title &&
                    parse(props?.InsightsData?.acf?.knowledge_center_title)}
                </h3>
              )}
              {props?.InsightsData?.acf?.knowledge_center_url && (
                <Buttion
                  text={props?.InsightsData?.acf?.knowledge_center_url?.title} // Button text
                  href={props?.InsightsData?.acf?.knowledge_center_url?.url} // Navigation target for the button
                  moduleClass={buttion.blue} // Custom button styling
                  imageSrc="/images/buttion_arrow.svg" // Image for button
                />
              )}
            </div>

            {props?.knowledgecenterArray &&
              props?.knowledgecenterArray.length > 0 && (
                <div className={`${style.knowledge_center_card_sec}`}>
                  {/* Left Card Section */}
                  <div
                    className={`${style.knowledge_center_left_card_sec}`}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    <div className={`${style.knowledge_center_left_card}`}>
                      {props?.knowledgecenterArray[0].acf.insights_page_image ||
                      props?.knowledgecenterArray[0].acf.listing_image ? (
                        <div
                          className={`${style.knowledge_center_left_card_img}`}
                        >
                          <Image
                            src={
                              props?.knowledgecenterArray[0].acf
                                .insights_page_image?.url ||
                              props?.knowledgecenterArray[0].acf.listing_image
                                ?.url
                            }
                            height={400}
                            width={900}
                            alt=""
                          />
                        </div>
                      ) : (
                        <div
                          className={`${style.knowledge_center_left_card_img}`}
                        >
                          <Image
                            src={"/images/noimage-insights.png"}
                            height={400}
                            width={900}
                            alt=""
                          />
                        </div>
                      )}

                      <div
                        className={`${style.knowledge_center_left_card_data} ${comon.knowledge_center_left_card_data}`}
                      >
                        <Link                         
                          href={
                              props?.knowledgecenterArray[0]?.acf?.external_news_link?.url
                                ? props?.knowledgecenterArray[0]?.acf?.external_news_link?.url
                                : `${newsurl}/insights/knowledge-center/${props?.knowledgecenterArray[0].slug}`
                            }
                            target={
                              props?.knowledgecenterArray[0]?.acf?.external_news_link
                              ? props?.knowledgecenterArray[0]?.acf?.external_news_link?.target
                              : "_self"
                            }
                        >
                          {props?.knowledgecenterArray[0].date && (
                            <span>
                              {DateConvert(props?.knowledgecenterArray[0].date)}
                            </span>
                          )}
                          {props?.knowledgecenterArray[0].title.rendered && (
                            <h4>
                              {parse(
                                props?.knowledgecenterArray[0].title.rendered
                              )}
                            </h4>
                          )}                          
                          {props?.knowledgecenterArray[0].acf.short_content && (
                            <p>
                              {props?.knowledgecenterArray[0].acf.short_content
                                .replace(/<\/?[^>]+(>|$)/g, "")
                                .substring(0, 100)}
                              ...
                            </p>
                          )}
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Right Card Section */}
                  <div className={`${style.knowledge_center_right_card_sec}`}>
                    {props?.knowledgecenterArray.slice(1).map((item) => (
                      <div
                        key={item.id}
                        className={`${style.knowledge_center_right_card}`}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <Link
                           href={
                              item?.acf?.external_news_link?.url
                                ? item?.acf?.external_news_link?.url
                                : `${newsurl}/insights/knowledge-center/${item.slug}`
                            }
                            target={
                              item?.acf?.external_news_link
                              ? item?.acf?.external_news_link?.target
                              : "_self"
                            }                          
                         >
                          {item.date && <span>{DateConvert(item.date)}</span>}
                          <p>
                            {item.title.rendered && parse(item.title.rendered)}
                          </p>
                          {item?.acf?.article_logo?.url && (
                            <Image
                              src={item?.acf?.article_logo?.url}
                              width={150}
                              height={110}
                              alt="logo"
                            />
                          )}
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </section>
      ) : null}

      {/* ========Projects Case Studies section======== */}
      {props &&
      props?.InsightsData &&
      props?.InsightsData?.acf &&
      props?.caseStudiesArray.length > 0 &&
      (props?.InsightsData?.acf?.case_studies_text ||
        props?.InsightsData?.acf?.case_studies_link) ? (
        <section className={`${style.news_center_section}`}>
          <div
            className={`${style.news_center_container} ${comon.wrap} ${comon.pt_130}  ${comon.pb_130}`}
          >
            <div className={`${style.news_center_header} ${comon.news_center_header}`}>
              {props?.InsightsData?.acf?.case_studies_text && (
                <h3 data-aos="fade-up" data-aos-duration="1000">
                  {props?.InsightsData?.acf?.case_studies_text &&
                    parse(props?.InsightsData?.acf?.case_studies_text)}
                </h3>
              )}
              {props?.InsightsData?.acf?.case_studies_link && (
                <Buttion
                  text={props?.InsightsData?.acf?.case_studies_link?.title} // Button text
                  href={props?.InsightsData?.acf?.case_studies_link?.url} // Navigation target for the button
                  moduleClass={buttion.blue} // Custom button styling
                  imageSrc="/images/buttion_arrow.svg" // Image for button
                />
              )}
            </div>
            <div className={`${style.news_center_card_sec}`}>
              {props?.caseStudiesArray && props?.caseStudiesArray.length > 0 && (
                <>
                  {/* Left Card Section */}
                  <div
                    className={`${style.news_center_left_card_sec}`}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    <div className={`${style.news_center_left_card}`}>
                      {props?.caseStudiesArray[0].acf.listing_tag && (
                        <div
                          className={`${style.news_center_left_card_notification}`}
                        >
                          {props?.caseStudiesArray[0].acf.listing_tag &&
                            parse(props?.caseStudiesArray[0].acf.listing_tag)}
                        </div>
                      )}
                      {props?.caseStudiesArray[0].acf.insights_page_image ||
                      props?.caseStudiesArray[0].acf.listing_image ? (
                        <div className={`${style.news_center_left_card_img}`}>
                          <Image
                            src={
                              props?.caseStudiesArray[0].acf.insights_page_image
                                ?.url ||
                              props?.caseStudiesArray[0].acf.listing_image?.url
                            }
                            height={400}
                            width={900}
                            alt=""
                          />
                        </div>
                      ) : (
                        <div className={`${style.news_center_left_card_img}`}>
                          <Image
                            src={"/images/noimage-insights.png"}
                            height={400}
                            width={900}
                            alt=""
                          />
                        </div>
                      )}

                      <div className={`${style.news_center_left_card_data} ${comon.news_center_left_card_data}`}>
                        <Link
                           href={
                              props?.caseStudiesArray[0]?.acf?.external_news_link?.url
                                ? props?.caseStudiesArray[0]?.acf?.external_news_link?.url
                                : `${newsurl}/insights/projects-case-studies/${props?.caseStudiesArray[0].slug}`
                            }
                            target={
                              props?.caseStudiesArray[0]?.acf?.external_news_link
                              ? props?.caseStudiesArray[0]?.acf?.external_news_link?.target
                              : "_self"
                            }                        
                        >
                          {props?.caseStudiesArray[0].date && (
                            <span>
                              {DateConvert(props?.caseStudiesArray[0].date)}
                            </span>
                          )}
                          {props?.caseStudiesArray[0].title && (
                            <h4>
                              {parse(props?.caseStudiesArray[0].title.rendered)}
                            </h4>
                          )}
                          {props?.caseStudiesArray[0].acf.short_content && (
                            <p>
                              {props?.caseStudiesArray[0].acf.short_content
                                .replace(/<\/?[^>]+(>|$)/g, "")
                                .substring(0, 100)}
                              ...
                            </p>
                          )}
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Right Card Section */}
                  <div className={`${style.news_center_right_card_sec}`}>
                    {props?.caseStudiesArray.slice(1).map((item) => (
                      <div
                        key={item.id}
                        className={`${style.news_center_right_card}`}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <Link
                          href={
                              item?.acf?.external_news_link?.url
                                ? item?.acf?.external_news_link?.url
                                : `${newsurl}/insights/projects-case-studies/${item.slug}`
                            }
                            target={
                              item?.acf?.external_news_link
                              ? item?.acf?.external_news_link?.target
                              : "_self"
                            }                            
                        >
                          {item.acf.listing_tag && (
                            <div
                              className={`${style.news_center_right_card_notification}`}
                            >
                              {item.acf.listing_tag &&
                                parse(item.acf.listing_tag)}
                            </div>
                          )}
                          {item.date && <span>{DateConvert(item.date)}</span>}
                          {item.title.rendered && (
                            <p>
                              {item.title.rendered &&
                                parse(item.title.rendered)}
                            </p>
                          )}
                        </Link>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </section>
      ) : null}

      <InquireNow formtitle={props?.InsightsData?.title?.rendered} />
    </div>
  );
};

export default Insights;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const InsightsData = await getInsightspage(locale);
  const query = "";
  //const slug = locale === "ar" ? "publications-ar" : "publications";
  const slug = InsightsData?.acf?.select_category?.slug;
  const InsightsPostsData = await getInsightsPosts(locale.locale, query);
  const IRMenuData = await getIrMenuLinks(locale);

  // console.log('filteredPosts', slug)

  //publications
  const Insightstaxonamy = await getInsightstaxonamyList(slug, locale.locale);
  const category = Insightstaxonamy.find((tax) => tax.slug === slug);
  const categoryId = category ? category.id : null;

  // console.log('Insightstaxonamy', Insightstaxonamy)
  // Filter posts by category ID
  const filteredPosts = InsightsPostsData.filter((post) =>
    post.categories.includes(categoryId)
  );
  // console.log('filteredPosts', filteredPosts)

  //news_center
  let InsightsPosts = [];
  if (
    InsightsData &&
    InsightsData.acf &&
    Array.isArray(InsightsData.acf.news_center)
  ) {
    InsightsPosts = InsightsData.acf.news_center;
  }
  // Format boardDirectors for use in the component
  let NewsCenterArray = [];
  if (InsightsPosts.length > 0) {
  NewsCenterArray = InsightsPosts.map((id) =>
      InsightsPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  //knowledge_center
  let knowleInsightsPosts = [];
  if (
    InsightsData &&
    InsightsData.acf &&
    Array.isArray(InsightsData.acf.knowledge_center)
  ) {
    knowleInsightsPosts = InsightsData.acf.knowledge_center;
  }
  // Format boardDirectors for use in the component
  let knowledgecenterArray = []; 
  if (knowleInsightsPosts.length > 0) {
  knowledgecenterArray = knowleInsightsPosts.map((id) =>
    InsightsPostsData?.find((post) => post.id === id)
  ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  //knowledge_center
  let CasestudiesPosts = [];
  if (
    InsightsData &&
    InsightsData.acf &&
    Array.isArray(InsightsData.acf.projects_case_studies)
  ) {
    CasestudiesPosts = InsightsData.acf.projects_case_studies;
  }
  // Format boardDirectors for use in the component
  let CasestudiesArray = [];
  if (CasestudiesPosts.length > 0) {
  CasestudiesArray = CasestudiesPosts.map((id) =>
    InsightsPostsData?.find((post) => post.id === id)
  ).filter(Boolean); // To ensure undefined values (if any) are removed
  }
  

  //console.log('testingdata', industriesCategory)

  return {
    props: {
      InsightsData: InsightsData || null,
      IRMenuData: IRMenuData || null,
      InsightsPostsData: InsightsPostsData || null,
      Insightstaxonamy: Insightstaxonamy || null,
      newsCenterArray: NewsCenterArray || [],
      caseStudiesArray: CasestudiesArray || [],
      knowledgecenterArray: knowledgecenterArray || [],
      publicationsPostsData: filteredPosts || null,
      
    },
    revalidate: 10,
  };
};
