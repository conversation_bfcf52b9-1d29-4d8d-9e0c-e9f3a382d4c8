@import "variable", "base";

.careers_detail_content_section {
    width: 100%;
    background-color: rgb(255, 255, 255);
    color: #000;

    .careers_detail_content_container {
        width: 100%;

        .careers_detail_content_title {
            padding-bottom: 30px;
           
            width: 48%;
            position: relative;

            @media #{$media-1440} {
                padding-bottom: 25px;
            }
            @media #{$media-995} {
                width: 100%;
            }
        

            &::after {
                position: absolute;
                content: '';
                width: 90%;
                height: 1px;
                background-color: #80d1e9;
                bottom: 0;
                left: 0;
                @media #{$media-995} {
                    width: 100%;
                }
            }

            span {
                background: rgba(186, 178, 234, 0.2);
                border-radius: 50px;
                padding: 5px 15px;
                color: #2e3192;
                font-size: 14px;
                line-height: 27px;
                font-weight: 400;
            }

            h3 {
                color: #000000;
                font-size: 45px;
                line-height: 63.23px;
                letter-spacing: -0.67px;
                display: block;
                font-weight: 400;
                font-family: var(--helveticaneuelt_arabic_55);

                @media #{$media-1440} {
                    font-size: 42px;
                    line-height: 55.23px;
                    @media #{$media-995} {
                        font-size: 38px;
                    line-height: 51.23px;
                    }
                    @media #{$media-767} {
                        font-size: 34px;
                    line-height: 42.23px;
                    }
                    @media #{$media-400} {
                        font-size: 30px;
                    line-height: 38.23px;
                    }
                }


            }

            .careers_detail_title_location {
                display: inline-flex;
                align-items: center;
                gap: 10px;
                color: #454545;
                font-size: 18px;
                line-height: 32.29px;
                font-weight: 500;
                padding-top: 10px;

                @media #{$media-1440} {
                    font-size: 16px;
                    line-height: 30.29px;
                }
                @media #{$media-995} {
                    font-size: 15px;
                    line-height: 28.29px;
                }

                .careers_detail_title_location_img {
                    height: auto;
                    width: 24px;

                    img {
                        display: block;
                        object-fit: cover;
                        height: auto;
                        width: 100%;
                    }
                }
            }
        }

        .careers_detail_content_description_sec {
            width: 100%;
            display: flex;
            flex-wrap: wrap;

            .careers_detail_content_description_left {
                width: 50%;
                padding-top: 40px;
                box-sizing: border-box;
                padding-right: 2%;
                @media #{$media-995} {
                    width: 100%;
                    padding-top: 35px;
                }

                .careers_detail_content_description_title {
                    color: #000000;
                    font-size: 26px;
                    font-weight: 400;
                    @media #{$media-1440} {
                        font-size: 25px;
                    }
                }

                p {
                    padding-top: 20px;
                    color: #454545;
                    font-size: 18px;
                    line-height: 27px;
                    font-family: var(--helveticaneuelt_arabic_55);
                    font-weight: 400;
                    @media #{$media-1440} {
                        font-size: 17px;
                        line-height: 26px;
                    }
                    @media #{$media-995} {
                        font-size: 16px;
                        line-height: 24px;
                    }
                    @media #{$media-767} {
                        font-size: 15px;
                        line-height: 22px;
                    }
                }

                ul {
                    margin-top: 35px;
                    list-style: none;
                    @media #{$media-995} {
                        margin-top: 30px;
                    }

                    .careers_detail_content_description_key_responsible_title {
                        color: #000000;
                        font-size: 26px;
                        font-family: var(--helveticaneuelt_arabic_55);
                        font-weight: 400;
                        padding-bottom: 25px;
                        text-transform: capitalize;
                        @media #{$media-1440} {
                            font-size: 25px;
                        }
                        
                    }

                    li {
                        color: #454545;
                        font-size: 18px;
                        line-height: 27px;
                        font-weight: 400;
                        font-family: var(--helveticaneuelt_arabic_55);
                        padding-left: 25px;
                        padding-bottom: 20px;
                        padding-right: 5%;
                        position: relative;
                        @media #{$media-1440} {
                            font-size: 17px;
                            line-height: 26px;
                        }
                        @media #{$media-995} {
                            font-size: 16px;
                            line-height: 24px;
                            padding-bottom: 15px;
                        }
                        @media #{$media-767} {
                            font-size: 15px;
                            line-height: 22px;
                        }


                        &::after {
                            position: absolute;
                            content: '';
                            height: 8px;
                            width: 8px;
                            border-radius: 50%;
                            background: #5e45ff;
                            left: 0;
                            top: 7px;
                        }
                    }
                }
            }

            .careers_detail_content_description_right {
                width: 50%;
                @media #{$media-995} {
                   width: 100%;
                   margin-top: 30px;
                }

                .careers_detail_form_body {
                    width: 95%;
                    margin-left: auto;

                    border-radius: 10px;
                    border: 1px solid #e3e3e3;
                    box-sizing: border-box;
                    padding: 50px 7%;
                    @media #{$media-1440} {
                        padding: 45px 7%;
                    }
                    @media #{$media-995} {
                        width: 100%;
                        padding: 40px;
                     }
                     @media #{$media-767} {
                        padding: 30px;
                    }

                    .careers_detail_form_title {

                        font-family: var(--helveticaneuelt_arabic_55);

                        color: #2e3192;
                        font-size: 30px;
                        line-height: 63.23px;
                        letter-spacing: -0.67px;
                        font-weight: 400;
                        @media #{$media-1440} {
                            font-size: 29px;
                        line-height: 55.23px;
                        }
                    }

                    .careers_detail_form_field {
                        list-style: none;

                        li {
                            width: 100%;
                            margin: 20px 0;

                            input[type="file"] {
                                display: none;
                            }

                            input {
                                width: 100%;
                                display: block;
                                background-color: white;
                                border: 1px solid #dbdbdb;
                                border-radius: 5px;
                                padding: 15px 5%;
                                color: #353a3f;
                                font-size: 17px;

                                letter-spacing: -0.03em;
                                font-weight: 400;

                                &:focus {
                                    outline: none;
                                }

                                &::placeholder {
                                    color: #afafaf;
                                    font-family: var(--helveticaneuelt_arabic_55);

                                    font-size: 14px;
                                    letter-spacing: -0.03em;
                                    font-weight: 400;
                                }
                            }

                            label {
                                width: 100%;
                                display: inline-flex;
                                justify-content: space-between;
                                background-color: white;
                                border: 1px solid #dbdbdb;
                                border-radius: 5px;
                                padding: 15px 5%;
                                color: #afafaf;

                                font-size: 14px;

                                letter-spacing: -0.03em;
                                font-weight: 400;

                                span {

                                    color: #5A5ABF;
                                    font-weight: bold;
                                    text-decoration: underline;
                                    cursor: pointer;

                                }
                            }
                        }
                    }

                    .careers_detail_form_btn {
                        margin-top: 50px;
                        padding: 17px 40px;
                        color: #ffffff;
                        background: #5e45ff;
                        border-radius: 50px;
                        font-size: 15px;
                        line-height: 140%;
                        letter-spacing: -0.02em;
                        font-weight: 400;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        text-transform: uppercase;
                        border: 0;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        @media #{$media-1440} {
                            margin-top: 45px;
                         }
                         @media #{$media-995} {
                            margin-top: 40px;
                            font-size: 14px;
                         }
                         @media #{$media-767} {
                            margin-top: 35px;
                         }

                        .careers_detail_form_btn_icon {
                            height: auto;
                            width: 15px;

                            img {
                                display: block;
                                object-fit: cover;
                                height: auto;
                                width: 100%;
                            }
                        }

                        &:hover {

                            background: #4531c7;

                        }
                    }

                }
            }
        }
    }
}