@import "variable", "base";

.InvestorsResourcesCard {
    list-style: none;
    width: 23%;
    box-sizing: border-box;
    padding: 60px 2.5%;
    border-radius: 10px;
    z-index: 0;
    background-image: linear-gradient(180deg,
            rgba(186, 178, 234, 0.2) 0%,
            rgba(171, 163, 223, 1) 100%);
    position: relative;
    display: flex;
    flex-direction: column;

    &:hover {
        &::after {
            background: #5e45ff;
        }

        .timeline {
            color: #ffffff;
        }

        .description {
            color: #ffffff;
        }

        .InvestorsResourcesCard_btn {
            background-color: #ffffff;
            color: #4d4747;

            &:hover {
                background-color: #d9d9d9;
            }

            .InvestorsResourcesCard_btn_icon {
                svg {
                    path {
                        fill: #5e45ff;
                    }
                }
            }
        }
    }

    .timeline {
        color: #000000;
        font-size: 1.125rem;
        letter-spacing: 0.05em;
        font-weight: 400;
        line-height: 100%;

        z-index: 1;
        font-family: var(--helveticaNeue);
        position: relative;
        @media #{$media-1600} {
            font-size: 1.325rem;
        }

        @media #{$media-1024} {
            font-size: 16px;
        }
    }

    .description {
        color: #000000;
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-weight: 400;
        font-family: var(--helveticaNeue);
        padding-top: 20px;
        z-index: 1;
        position: relative;
        display: block;
        flex-grow: 1;
        @media #{$media-1600} {
            font-size: 1.425rem;
            line-height: 1.95rem;
        }
        @media #{$media-1024} {
            font-size: 17px;
            line-height: 25px;
        }

        @media #{$media-767} {
            padding-top: 0;
        }
    }

    // ====IR Section====

    .IR_heading {
        color: #000000;
        font-size: 20px;
        line-height: 50px;
        font-weight: bold;
        font-family: var(--helveticaNeue);
        z-index: 1;
        position: relative;

        @media #{$media-1024} {
            font-size: 19px;
            line-height: 30px;
        }
    }

    .IR_address {
        color: #1e1e2c;
        font-size: 16px;
        line-height: 26px;
        font-family: var(--helveticaNeue);
        padding-top: 20px;
        padding-bottom: 15px;
        z-index: 1;
        position: relative;

        @media #{$media-1024} {
            font-size: 19px;
        }

        @media #{$media-767} {
            font-size: 17px;
        }
    }

    .IR_phone,
    .IR_email {
        color: #000000;
        font-size: 15px;
        line-height: 20px;
        font-weight: 400;
        font-family: var(--helveticaneuelt_arabic_55);
        padding-top: 10px;
        z-index: 1;
        display: block;
        position: relative;

        @media #{$media-1024} {
            font-size: 19px;
        }

        @media #{$media-767} {
            font-size: 16px;
        }
    }



    .InvestorsResourcesCard_btn {
        color: #ffffff;
        margin-top: 40px;

        display: inline-flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 50px;
        font-size: 16px;
        line-height: 28px;
        font-weight: 400;
        padding: 10px 25px;
        background-color: #5e45ff;
        z-index: 1;
        position: relative;
        border: 0;
        font-family: var(--helveticaNeue);

        .InvestorsResourcesCard_btn_icon {
            height: auto;
            width: 18px;

            img {
                display: block;
                height: auto;
                width: 100%;
                object-fit: cover;
            }

            @media #{$media-1024} {
                width: 15px;
            }
        }

        @media #{$media-1280} {
            margin-top: 25px;
            font-size: 15px;
            line-height: 28px;
            padding: 7px 20px;
        }

        @media #{$media-1024} {
            margin-top: 20px;
            font-size: 15px;
            line-height: 28px;
            padding: 6px 17px;
        }

        &:hover {
            background: rgb(52, 80, 219);
        }
    }

    &::after {
        z-index: 0;

        position: absolute;
        content: "";
        height: calc(100% - 2px);
        width: calc(100% - 2px);
        top: 1px;
        left: 1px;
        background-color: white;
        border-radius: 9px;
    }

    @media #{$media-1280} {
        padding: 40px 2.5%;
    }

    @media #{$media-1024} {
        width: 31.33%;
    }

    @media #{$media-950} {
        padding: 40px 2.5% 30px 2.5%;
    }

    @media #{$media-767} {
        width: 100%;
        padding: 30px 5%;
    }
}