.expert_card{
    .expert_pic{
        border-radius: 10px;
        overflow: hidden;
        text-align: center;
        position: relative;
        padding-top: 15px;
        background-color: #fdfbfc;
        img{
            display: block;
            border-radius: 10px;
            overflow: hidden;
            margin: 0 auto;
        }
    
        &:after {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: 10px;
            padding: 1px;
            background:linear-gradient(
                180deg,
                rgba(186, 178, 234, 0.2) 0%,
                rgba(171, 163, 223, 1) 100%
            );
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
        }
    }
    h3{
        color: #1A1831;
        text-align: center;
        display: block;
        margin-top: 20px;
    }
}