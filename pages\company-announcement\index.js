import React, { useEffect, useState } from "react";
import style from "@/styles/companyAnnoncement.module.scss";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import TabSection from "@/component/Tab-section";
import Yoast from "@/component/yoast";
import {
  getCompanyAnnouncement,
  getCompanyPosts,
  getIrMenuLinks,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import { useRouter } from "next/router";

const ShareInfo = (props) => {
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  const [visiblePosts, setVisiblePosts] = useState(10); // Number of visible posts
  const [searchKeyword, setSearchKeyword] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [filteredPosts, setFilteredPosts] = useState([]);
  const router = useRouter();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setFilteredPosts(props?.IrCompanyPostsData || []);
  }, [props?.IrCompanyPostsData]);

  const yoastData = props?.IrCompanyData?.yoast_head_json;

  const handleLoadMore = () => {
    setVisiblePosts((prev) => prev + 10);
  };

  const handleFilter = () => {
    const filtered = props?.IrCompanyPostsData.filter((post) => {
      const title = post?.title?.rendered?.toLowerCase();
      const postDate = new Date(post.acf?.date_calender);
      const from = fromDate ? new Date(fromDate) : null;
      const to = toDate ? new Date(toDate) : null;

      // Filter logic
      const matchesKeyword = title?.includes(searchKeyword.toLowerCase());
      const matchesDate =
        (!from || postDate >= from) && (!to || postDate <= to);

      return matchesKeyword && matchesDate;
    });

    setFilteredPosts(filtered);
  };

  const handleReset = () => {
    setSearchKeyword("");
    setFromDate("");
    setToDate("");
    setFilteredPosts(props?.IrCompanyPostsData || []);
  };

  if (!props.IrCompanyData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}
      {yoastData && <Yoast meta={yoastData} />}
      {props &&
      props?.IrCompanyData &&
      props?.IrCompanyData?.acf &&
      props?.IrCompanyData?.acf?.banner_title &&
      (props?.IrCompanyData?.acf?.mob_banner_image ||
        props?.IrCompanyData?.acf?.banner_image ||
        props?.IrCompanyData?.acf?.breadcrumbs ||
        props?.IrCompanyData?.acf?.banner_viedo ||
        props?.IrCompanyData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.IrCompanyData?.acf?.banner_title}
          breadcrumb1={
            props?.IrCompanyData?.acf?.active_breadcrumbs === "yes"
              ? props?.IrCompanyData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.IrCompanyData?.acf?.mob_banner_image?.url
              : props?.IrCompanyData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.IrCompanyData?.acf?.banner_viedo?.url}
          banner_type={props?.IrCompanyData?.acf?.banner_type}
        />
      ) : null}

      {/* =========Tab Section====== */}
      {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.investors_relations_menu && (
          <TabSection tabs={props?.IRMenuData?.investors_relations_menu} />
        )}

      {/* -----------Announcement section----------- */}
      <section className={`${style.share_info_announcement_section}`}>
        <div
          className={`${style.share_info_announcement_container} ${comon.wrap} ${comon.pt_50} ${comon.pb_50}`}
        >
          {props?.IrCompanyData?.acf?.announcement_title && (
            <h4 data-aos="fade-up" data-aos-duration="1000">
              {props?.IrCompanyData?.acf?.announcement_title &&
                parse(props?.IrCompanyData?.acf?.announcement_title)}
            </h4>
          )}
          <div
            className={`${style.share_info_form_field_sec} ${comon.pt_30} ${comon.pb_30}`}
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            <form
              className="filterform"
              onSubmit={(e) => {
                e.preventDefault();
                handleFilter();
              }}
            >
              <ul className={`${style.share_info_input_sec}`}>
                <li>
                  <input
                    type="text"
                    placeholder={router.locale === "ar" ? "بحث" : "Search"}
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                  />
                </li>
                <li
                  onClick={() =>
                    document.getElementById("fromDate").showPicker()
                  }
                  className={style.date_li}
                >
                  <input
                    type="date"
                    id="fromDate"
                    placeholder="From"
                    value={fromDate}
                    onChange={(e) => setFromDate(e.target.value)}
                    className={`${fromDate ? style.active : ""} ${
                      style.date_feild
                    }`}
                  />
                  {!fromDate && (
                    <span className={style.input_placeholder}>
                      {router.locale === "ar" ? "من" : "From"}
                    </span>
                  )}
                </li>
                <li
                  onClick={() => document.getElementById("toDate").showPicker()}
                  className={style.date_li}
                >
                  <input
                    type="date"
                    id="toDate"
                    placeholder="To"
                    value={toDate}
                    onChange={(e) => setToDate(e.target.value)}
                    className={`${toDate ? style.active : ""} ${
                      style.date_feild
                    }`}
                  />
                  {!toDate && (
                    <span className={style.input_placeholder}>
                      {router.locale === "ar" ? "إلى" : "To"}
                    </span>
                  )}
                </li>
              </ul>
              <button type="submit" className={`${style.share_info_input_btn}`}>
                {router.locale === "ar" ? "تصفية" : "Filter"}
              </button>
              <button
                type="button"
                onClick={handleReset}
                className={`${style.share_info_input_btn}`}
              >
                {router.locale === "ar" ? "إعادة تعيين" : "Reset"}
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* -----------Company Announcement section----------- */}
      <section className={`${style.share_info_company_announcement_section}`}>
        <div
          className={`${style.share_info_company_announcement_container} ${comon.share_info_company_announcement_container} ${comon.wrap} ${comon.pt_100} ${comon.pb_100}`}
        >
          {props?.IrCompanyData?.acf?.company_announcement_title && (
            <h4 data-aos="fade-up" data-aos-duration="1000">
              {props?.IrCompanyData?.acf?.company_announcement_title &&
                parse(props?.IrCompanyData?.acf?.company_announcement_title)}
            </h4>
          )}
          <div
            className={`${style.share_info_company_announcement_card_sec} ${comon.pt_50}`}
          >
            {filteredPosts &&
              filteredPosts
                .slice(0, visiblePosts)
                .map((Mediadata, medIndex) => (
                  <Link
                    className={`${style.share_info_company_announcement_card}`}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                    key={medIndex}
                    href={`/company-announcement/${Mediadata?.slug}`}
                  >
                    <span>
                      {Mediadata.acf?.hijri_calendar}{" "}
                      {Mediadata.acf?.date_calender}{" "}
                      {Mediadata.acf?.time_calender}
                    </span>

                    <h5>
                      {Mediadata?.title && parse(Mediadata?.title?.rendered)}
                    </h5>
                  </Link>
                ))}
          </div>
          {visiblePosts < filteredPosts.length && (
            <p className={`${style.loading} ${style.pt_60}`}>
              <a href="#." onClick={handleLoadMore}>
                {router.locale === "ar" ? "تحميل المزيد ..." : "Load More ...."}
              </a>
            </p>
          )}
        </div>
      </section>
    </div>
  );
};

export default ShareInfo;

export const getStaticProps = async (locale) => {
  const IrCompanyData = await getCompanyAnnouncement(locale.locale);
  const IRMenuData = await getIrMenuLinks(locale);
  const query = "";
  const IrCompanyPostsData = await getCompanyPosts(locale.locale, query);

  return {
    props: {
      IrCompanyData: IrCompanyData || null,
      IRMenuData: IRMenuData || null,
      IrCompanyPostsData: IrCompanyPostsData || null,
    },
    revalidate: 10,
  };
};
