import React, { useEffect } from "react";
import Header from "@/component/Header";
import Footer from "@/component/Footer";
import localFont from "next/font/local";
import comon from "@/styles/comon.module.scss";
import AOS from "aos";
import Head from "next/head";
import { useRouter } from "next/router";
import "aos/dist/aos.css";
// -------- Font Implementation Start --------

const helveticaneueltArabic55 = localFont({
  src: "../public/fonts/HelveticaNeueLTArabic-Roman.woff",
  variable: "--helveticaneuelt_arabic_55",
});

const helveticaneueltarabicBold = localFont({
  src: "../public/fonts/helvetica/HelveticaNeue-Bold.woff",
  variable: "--helveticaneueltarabicBold",
});

const helveticaneueltarabicLight = localFont({
  src: "../public/fonts/helvetica/HelveticaNeue-Light.woff",
  variable: "--helveticaneueltarabicLight",
});
const helveticaNeue = localFont({
  src: "../public/fonts/helvetica/HelveticaNeue.woff",
  variable: "--helveticaNeue",
});
const helveticaNeueMedium = localFont({
  src: "../public/fonts/helvetica/HelveticaNeue-Medium.woff",
  variable: "--helveticaNeueMedium",
});

const segoeUiBold = localFont({
  src: "../public/fonts/segoe-ui-bold.woff",
  variable: "--segoeUiBold",
});

const segoeUiSemiBold = localFont({
  src: "../public/fonts/segoe-ui-semibold.woff",
  variable: "--segoeUiSemiBold",
});

const segoeUi = localFont({
  src: "../public/fonts/segoe-ui.woff",
  variable: "--segoe-ui",
});

// -------- Font Implementation End --------

const Layout = (props) => {
  const router = useRouter();
  const { pathname } = router;

  // Use effect to initialize Locomotive Scroll
  useEffect(() => {
    let locomotiveScrollInstance;

    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });

    if (pathname !== "/digital" && pathname !== "/digital#inquire") {
      import("locomotive-scroll").then((LocomotiveScroll) => {
        locomotiveScrollInstance = new LocomotiveScroll.default({
          el: document.querySelector("#main-element"),
          smooth: true,
        });
      });
    }

    return () => {
      if (locomotiveScrollInstance) locomotiveScrollInstance.destroy();
    };
  }, [pathname]);

  return (
    <React.Fragment>
      <Head>
        <title>Tam Development</title>
        <meta name="description" content="Tam Development" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin />
        <link
          href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
          rel="stylesheet"
        ></link>
      </Head>
      <main
        id="main-element"
        className={`${comon.main} ${helveticaNeue.variable} ${helveticaNeueMedium.variable} ${helveticaneueltarabicLight.variable} ${helveticaneueltarabicBold.variable} ${helveticaneueltArabic55.variable} ${segoeUiBold.variable} ${segoeUiSemiBold.variable} ${segoeUi.variable}`}
      >
        <Header />
        {props.children}
        <Footer />
      </main>
    </React.Fragment>
  );
};

export default Layout;
