{"name": "tam", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fancyapps/ui": "github:fancyapps/ui", "@mui/icons-material": "^6.1.6", "aos": "^2.3.4", "axios": "^1.7.7", "chart.js": "^4.4.7", "date-fns": "^4.1.0", "gsap": "^3.12.5", "html-react-parser": "^5.1.18", "i18n": "^0.15.1", "i18next": "^23.15.1", "locomotive-scroll": "^5.0.0-beta.21", "next": "^14.2.28", "next-sitemap": "^4.2.3", "react": "^18", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "^18", "react-fast-marquee": "^1.6.5", "react-intersection-observer": "^9.13.1", "sass": "^1.79.4", "swiper": "^11.1.14", "xml2js": "^0.6.2"}}