<Swiper
          loop={true}
          spaceBetween={10}
          navigation={{
            nextEl: ".custom_next_1",
            prevEl: ".custom_prev_1",
          }}
          thumbs={thumbsSwiper && { swiper: thumbsSwiper }}
          modules={[FreeMode, Navigation, Thumbs]}
          className="mySwiper2"
        >
         {props?.TeamData && props?.TeamData.map((data, tindex) => (
            <SwiperSlide key={tindex}>
              <div
                className={`${aboutus.about_our_team_content_sec} ${comon.about_our_team_content_sec}`}
                style={{
                  backgroundImage: `url(${data?.acf?.team_image?.url})`,
                }}
                
              >
                <div
                  className={`${aboutus.about_our_team_container} ${comon.wrap} `}
                >
                  <div className={aboutus.about_our_team_left_content_sec}>
                    <span
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    > {data.categoryNames.join(", ")} {/* Display category names */}</span>
                    <div className={aboutus.about_our_team_left_title_sec}>
                      <div className={aboutus.about_our_team_left_title}>
                        {data?.title &&
                         <h4
                          data-aos="fade-up"
                           data-aos-duration="1000">
                           {data?.title && data?.title?.rendered}
                         </h4>
                        }
                       
                       {data?.acf?.team_position &&
                         <h6
                          data-aos="fade-up"
                           data-aos-duration="1000">
                           {data?.acf?.team_position}
                         </h6>
                         }
                        
                      </div>
                     {data?.acf?.team_linkedin &&
                       <Link
                         href={data?.acf?.team_linkedin?.url}
                         target={data?.acf?.team_linkedin?.target}
                         className={aboutus.about_our_team_left_linkedin}
                       >
                         <DynamicImage
                           src="/images/linked_image.png"
                           alt="Dynamic Image"
                         />
                       </Link>
                     }
                    </div>
                    {isMobile && data?.acf?.team_image && (
                      <div className={aboutus.team_mob_image}>
                        <DynamicImage src={data?.acf?.team_image?.url} alt="Dynamic Image" />
                      </div>
                   )}
                   {data?.acf?.team_content &&
                     <p
                       data-aos="fade-up"
                       data-aos-duration="1000">
                       {data?.acf?.team_content && parse(data?.acf?.team_content)}
                     </p>
                   }
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>