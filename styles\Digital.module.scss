@import "variable", "base";

.digital_what_we_offer_section {
    width: 100%;
    position: relative;

    .digital_what_we_offer_container {
        width: 100%;

        @media #{$media-767} {
            padding-top: 30px;
        }

        h3 {
            color: #ffffff;
            // font-size: 55px;
            font-size: 3.4375rem;

            line-height: 4.063rem;
            font-weight: 400;
        }

        p {
            padding-top: 25px;
            color: #ffffff;
            font-size: 16px;
            line-height: 20px;
            font-weight: 400;

            @media #{$media-950} {
                padding-top: 20px;
            }

            @media #{$media-767} {
                padding-top: 15px;
            }
        }

        .digital_what_we_offer_card_sec {
            display: flex;
            flex-wrap: wrap;
            gap: 2%;
            row-gap: 30px;
            list-style: none;
            padding-top: 40px;

            li {
                width: 23%;
                // border: 1px solid #644AFF;
                border-radius: 10px;
                display: flex;
                // background-color: #222242;
                background-image: linear-gradient(180deg,
                        rgba(186, 178, 234, 0.2) 0%,
                        rgba(171, 163, 223, 1) 100%);
                flex-direction: column;
                justify-content: space-between;
                box-sizing: border-box;
                padding: 50px 2% 50px 3%;
                position: relative;
                z-index: 1;
                cursor: pointer;

                .digital_what_we_offer_card_logo_sec {
                    z-index: 1;

                    .digital_what_we_offer_card_logo {
                        height: auto;
                        width: 22%;
                        margin-left: -5px;

                        img {
                            height: 110px;
                            width: 110px;
                            display: block;
                            object-fit: cover;
                        }

                        @media #{$media-1024} {
                            width: 30%;
                        }

                        @media #{$media-820} {
                            width: 20%;
                        }

                        @media #{$media-500} {
                            width: 30%;
                        }
                    }
                }

                .digital_what_we_offer_card_footer_sec {
                    padding-top: 35px;
                    z-index: 1;

                    h4 {
                        color: #ffffff;
                        // font-size: 32px;
                        font-size: 1.563rem;
                        line-height: 1.875rem;
                        font-weight: 400;
                        display: inline-block;
                        font-family: var(--helveticaneueltarabicLight);

                        @media #{$media-1024} {
                            line-height: 130%;
                            font-size: 1.3rem;
                        }

                        @media #{$media-820} {
                            line-height: 130%;
                            width: 100%;
                            font-size: 1.9rem;
                        }
                    }

                    @media #{$media-1440} {
                        padding-top: 25px;
                    }

                    @media #{$media-1280} {
                        padding-top: 20px;
                    }

                    @media #{$media-1024} {
                        padding-top: 25px;
                    }

                    @media #{$media-820} {
                        padding-top: 10px;
                    }
                }

                &::before {
                    position: absolute;
                    content: "";
                    height: calc(100% - 2px);
                    width: calc(100% - 2px);
                    top: 1px;
                    left: 1px;
                    // background-image: linear-gradient(150deg,
                    //         #222242ad 0%,
                    //         rgba(101, 74, 255, 0) 100%);
                    // ;

                    background-color: #222242;

                    // background-color: red;
                    border-radius: 10px;
                    z-index: -2;
                    transition: .3s all ease-in-out;

                }

                &:hover {
                    &::before {
                        background-color: #5E45FF;
                    }

                }

                @media #{$media-1440} {
                    padding: 30px 3% 35px 3%;
                }

                @media #{$media-1024} {
                    padding: 30px 2%;
                }

                @media #{$media-820} {
                    width: 49%;
                }

                @media #{$media-500} {
                    padding: 25px 3% 30px 3%;
                }
            }

            @media #{$media-1024} {
                row-gap: 20px;
            }

            @media #{$media-500} {
                row-gap: 8px;
            }
        }

        .digital_what_we_offer_card_popup_container {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            width: 100%;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: -50;
            opacity: 0;

            &::after {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                display: block;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, .5);
            }

            &.popup_active {

                z-index: 999;
                opacity: 1;
            }

            .digital_what_we_offer_card_popup_sec {
                width: 35%;
                background-color: rgb(255, 255, 255);
                padding: 80px 3% 50px 3%;
                border-radius: 62px;
                box-sizing: border-box;
                position: relative;
                z-index: 1000;

                @media #{$media-1440} {
                    width: 40%;
                    padding: 70px 3% 45px 3%;
                }

                @media #{$media-1024} {
                    width: 60%;
                    border-radius: 45px;
                    padding: 55px 3% 35px 3%;
                }

                @media #{$media-820} {
                    width: 80%;
                    border-radius: 35px;
                }

                @media #{$media-767} {
                    border-radius: 15px;
                    width: 90%;
                    padding: 30px 20px;

                }

                .digital_what_we_offer_card_popup_close {
                    height: auto;
                    width: 40px;
                    position: absolute;
                    inset-inline-end: 35px;
                    top: 35px;
                    cursor: pointer;

                    img {
                        display: block;
                        object-fit: cover;
                        height: auto;
                        width: 100%;
                    }

                    @media #{$media-1024} {
                        inset-inline-end: 25px;
                        top: 25px;
                    }

                    @media #{$media-767} {
                        width: 35px;
                        inset-inline-end: 15px;
                        top: 15px;
                    }
                }

                .digital_what_we_offer_card_popup_title {
                    h4 {
                        color: #212142;
                        font-family: var(--helveticaneuelt_arabic_55);
                        font-size: 2.813rem;
                        line-height: 2.938rem;
                        font-weight: 400;
                        padding-inline-end: 5%;
                        padding-bottom: 30px;
                        z-index: 9;

                        @media #{$media-1024} {
                            padding-bottom: 0px;
                            font-size: 2.513rem;
                        }
                        @media #{$media-500} {
                            padding-bottom: 0px;
                            font-size: 2.813rem;
                        }
                    }
                }

                .digital_what_we_offer_card_popup_content {
                    padding-top: 20px;

                    &+.digital_what_we_offer_card_popup_content {
                        padding-top: 30px;
                    }

                    h4 {
                        color: #5e45ff;
                        font-family: var(--helveticaneueltarabicBold);
                        font-size: 1.875rem;
                        line-height: 2.8125rem;
                        @media #{$media-500} {
                            font-size: 2rem;
                        line-height: 2.6rem;
                        }
                    }

                    ul {
                        li {
                            color: #252525;
                            font-family: var(--helveticaneuelt_arabic_55);
                            margin: 10px 0;
                            font-size: 1.563rem;
                            line-height: 2.313rem;
                            font-weight: 400;
                            padding-inline-start: 40px;
                            position: relative;

                            @media #{$media-767} {
                                padding-left: 25px;
                                font-size: 1.93rem;
                            line-height: 2.43rem;
                            }


                            &::before {
                                position: absolute;
                                content: "";
                                height: 25px;
                                width: 27px;
                                inset-inline-start: 0;
                                top: 2px;
                                background-image: url(/images/diamond_new.svg);
                                background-position: center;
                                background-repeat: no-repeat;
                                background-size: contain;

                                @media #{$media-767} {
                                    height: 17px;
                                    width: 17px;

                                }
                            }

                        }
                    }

                    ul+h4 {
                        padding-top: 30px;

                        @media #{$media-1024} {
                            padding-top: 20px;
                        }

                        @media #{$media-767} {
                            padding-top: 15px;
                        }
                    }
                }
            }
        }
    }
}

.counter_sec {
    padding-bottom: 120px;

    @media #{$media-1024} {
        padding-bottom: 60px;
    }

    @media #{$media-767} {
        padding-bottom: 30px;
    }

    h2 {
        font-size: 2.188rem;
        margin-bottom: 30px;
        display: block;
    }
}

.counter_list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 130px;

    @media #{$media-1024} {
        grid-template-columns: repeat(2, 1fr);
        width: 70%;
    }

    @media #{$media-820} {
        column-gap: 40px;
    }

    li {
        position: relative;

        &::after {
            content: "";
            background-image: url(../public//images/dot_icn.png);
            width: 25px;
            height: 40px;
            display: block;
            position: absolute;
            right: -70px;
            top: 50%;
            transform: translateY(-70%);

            @media #{$media-820} {
                display: none;
            }
        }

        // &:first-child {
        //     h5 {
        //         text-align: left;

        //     }
        // }

        &:last-child {
            &::after {
                display: none;
            }
        }

        // &:nth-child(2) {
        //     &::after {
        //         display: none;
        //     }
        // }
    }

    .count_set {
        background: linear-gradient(-90deg,
                rgb(227, 181, 255) -10%,
                rgb(94, 69, 255) 50%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 5.938rem;
        font-family: var(--helveticaneuelt_arabic_55);
        line-height: 100%;
        font-weight: 400;
        display: flex;
        // display: inline-flex;
        align-items: flex-end;
        justify-content: flex-end;
        text-transform: uppercase;
    }

    h5 {
        background: linear-gradient(-90deg,
                rgba(227, 181, 255, 1) 0%,
                rgba(94, 69, 255, 1) 100%),
            linear-gradient(to left, #302c29, #302c29);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 1.25rem;
        line-height: 50px;
        font-weight: 400;
        display: block;
        font-family: var(--helveticaneueltarabicLight);
        text-align: right;

        @media #{$media-1024} {
            text-align: left;
        }
    }
}

.digital_product {
    
    padding: 100px 0 140px 0;

    @media #{$media-1024} {
        padding: 80px 0 100px 0;
    }

    @media #{$media-820} {
        padding: 50px 0;
    }

    .digital_top {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        margin-bottom: 40px;

        @media #{$media-767} {
            flex-wrap: wrap;
            width: 100%;
        }

        h2 {
            font-size: 3.438rem;
            color: #fff;

            @media #{$media-767} {
                width: 100%;
            }

            br {
                @media #{$media-767} {
                    display: none;
                }
            }
        }
    }

    .digital_top>p {
        font-size: 16px;
        line-height: 20px;
        color: #fff;
        width: 50%;

        @media #{$media-820} {
            font-size: 14px;
            line-height: 18px;
        }

        @media #{$media-767} {
            width: 100%;
            margin-top: 20px;
        }
        @media #{$media-820} {
            font-size: 15px;
            line-height: 20px;
        }
    }

    .digi_main_slider {
        border-radius: 20px;
        position: relative;
        padding: 1px;

        &::after {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: 20px;
            padding: 1px;
            background: linear-gradient(270deg,
                    rgba(255, 255, 255, 1) 0%,
                    rgba(161, 147, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(270deg,
                    rgba(255, 255, 255, 1) 0%,
                    rgba(161, 147, 255, 0.5) 100%);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
        }
    }

    .digi_slider {
        background: linear-gradient(94deg, rgb(37 34 66) 23%, rgb(53 44 120) 100%);
        position: relative;
        z-index: 9;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        align-items: center;
        border-radius: 20px;
        padding: 40px 60px 75px;

        @media #{$media-820} {
            display: flex;
        }

        @media #{$media-767} {
            flex-wrap: wrap;
            padding: 30px 40px 65px;
            text-align: center;
        }

        h3 {
            font-size: 30px;
            color: #fff;
            margin-bottom: 20px;
            text-transform: capitalize;
            

            @media #{$media-820} {
                font-size: 24px;
                line-height: 111%;
            }
        }

        p {
            color: #fff;
            font-size: 16px;
            margin-bottom: 40px;
            font-family: var(--helveticaneueltarabicLight);
            font-weight: 300;
            line-height: 24px;

            @media #{$media-820} {
                font-size: 14px;
                line-height: 22px;
            }

            @media #{$media-767} {
                margin-bottom: 20px;

            }
        }
    }

    .main_slider_pic {
        width: 50%;

        @media #{$media-767} {
            width: 100%;
        }

        img {

            @media #{$media-820} {
                width: 240px;
                height: 230px;
                object-fit: cover;
            }

            @media #{$media-767} {
                width: 240px;
                height: 200px;
                object-fit: cover;
            }
        }
    }

    .main_slider_text {
        @media #{$media-820} {
            width: 50%;
        }

        @media #{$media-767} {
            width: 100%;
            margin-top: 15px;

        }
    }
.thumb_list_wrap{
    overflow: auto;
    &::-webkit-scrollbar {
        display: none;
    }
    .thumb_list {
        display: grid;
        grid-template-columns: repeat(9, 1fr);
        column-gap: 13px;
        max-width: 1175px;
        margin: 0 auto;
        margin-top: 30px;

        

        li {
            border: 1px solid #BFB5FF;
            outline: 1px solid transparent;
            border-radius: 10px;
            text-align: center;
            padding: 15px 0;
            width: 120px;
            cursor: pointer;
            opacity: 0.3;

            img {
                object-fit: cover;
            }

            &.active {
                opacity: 1;
                outline: 1px solid #BFB5FF;
            }


        }
    }
}

    .digi_arws {
        width: 60px;
        height: 60px;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 9;

        @media #{$media-1600} {
            width: 40px;
            height: 40px;
        }

        @media #{$media-767} {
            width: 40px;
            height: 40px;
        }

        img {
            width: 18px;
            height: 20px;

        }
    }

    .arrow_left {
        inset-inline-start: -70px;

        @media #{$media-1600} {
            inset-inline-start: -47px;
        }

        @media #{$media-1280} {
            inset-inline-start: -25px;
        }

        @media #{$media-820} {
            inset-inline-start: -15px;
        }

        @media #{$media-767} {
            inset-inline-start: 7px;
        }
    }

    .arrow_right {
        inset-inline-end: -70px;

        @media #{$media-1600} {
            inset-inline-end: -47px;
        }

        @media #{$media-1280} {
            inset-inline-end: -25px;
        }

        @media #{$media-820} {
            inset-inline-end: -15px;
        }

        @media #{$media-767} {
            inset-inline-end: 7px;
        }
    }
}

.learnmore_btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 50px;
    border: 0;
    background-color: white;
    color: #2a2656;
    font-size: 16px;
    line-height: 28px;
    font-weight: 400;
    cursor: pointer;
    width: 170px;
    transition: all 0.4s ease-out;

    &:hover {
        background: #d9d9d9;
    }

    .learnmore_btn_img {
        height: auto;
        width: 14px;
        margin-inline-start: 10px;
        display: flex;
        align-items: center;
    }

    @media #{$media-767} {
        margin: 0 auto;
        font-size: 14px;
    }
}

.digi_main_slider {
    position: relative;
}

.digital_side_bar_container {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    right: -100%;
    z-index: 999;
    overflow-y: scroll;
    // scrollbar-width: none;
    transition: all 0.53s ease-in-out;
    &::-webkit-scrollbar {
        display: none;
      }

    &.digital_side_bar_container_active {


        right: 0%;

    }


    .digital_side_bar_section {
        box-sizing: border-box;
        background-color: rgb(255, 255, 255);
        width: 55%;
        position: absolute;
        top: 0;
        right: 0;

        @media #{$media-767} {
            width: 100%;
        }


        .digital_what_we_offer_card_popup_close {
            height: auto;
            width: 40px;
            position: absolute;
            inset-inline-end: 35px;
            top: 35px;
            cursor: pointer;

            @media #{$media-1024} {
                inset-inline-end: 25px;
                top: 25px;
            }

            @media #{$media-767} {
                width: 35px;
                inset-inline-end: 15px;
                top: 15px;
            }

            img {
                display: block;
                object-fit: cover;
                height: auto;
                width: 100%;
            }
        }



        .digital_side_top_section {

            padding: 60px 6%;
            width: 100%;

            // color: #000000;
            @media #{$media-1440} {
                padding: 50px 6%;
            }

            @media #{$media-1024} {
                padding: 40px 6%;
            }



            .digi_side_bar_head {
                h3 {
                    color: #000000;
                    font-size: 40px;
                    line-height: 40px;
                    display: block;
                    text-transform: uppercase;
                    font-weight: 400;
                    font-family: var(--helveticaNeue);

                    @media #{$media-1440} {
                        font-size: 36px;
                    }

                    @media #{$media-1024} {
                        font-size: 32px;
                    }
                }

                span {
                    color: #000000;
                    font-size: 20px;
                    font-family: var(--helveticaNeue);
                    display: block;
                    padding: 10px 0;
                    line-height: 40px;
                    font-weight: 400;

                    @media #{$media-1440} {
                        font-size: 19px;
                        line-height: 37px;
                    }

                    @media #{$media-1024} {
                        font-size: 17px;
                        line-height: 28px;
                    }
                }

                p {
                    color: #000000;
                    font-size: 16px;
                    font-family: var(--helveticaNeue);
                    display: block;

                    line-height: 27px;
                    font-weight: 400;

                    @media #{$media-1024} {
                        font-size: 15px;
                        line-height: 25px;
                    }
                }
            }

            .digi_side_bar_content {
                width: 100%;
                border-top: 1px solid rgb(209, 209, 209);
                padding-top: 50px;
                padding-bottom: 10px;
                padding-top: 35px;
                padding-bottom: 30px;




                h4 {
                    color: #000000;

                    display: block;
                    font-size: 30px;
                    line-height: 40px;
                    font-weight: 400;
                    padding-bottom: 15px;

                    font-family: var(--helveticaNeue);

                    @media #{$media-1440} {
                        font-size: 28px;
                    }

                    @media #{$media-1024} {
                        font-size: 25px;
                        line-height: 35px;
                    }
                }

                .digi_side_bar_first_ul {
                    display: flex;
                    flex-wrap: wrap;
                    margin-left: -15px;
                    margin-right: -15px;
                    width: 100%;

                    li {
                        width: calc(50% - 30px);
                        box-sizing: border-box;
                        margin: 20px 15px;
                        display: flex;
                        flex-direction: column;
                        font-family: var(--helveticaNeue);

                        @media #{$media-1024} {
                            margin: 15px;
                        }

                        @media #{$media-995} {
                            width: calc(100% - 30px);
                        }

                        span {
                            background: #5e45ff;
                            border-radius: 50%;
                            width: 50px;
                            height: 50px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #ffffff;
                            font-size: 20px;
                            line-height: 40px;
                            font-weight: 400;
                            margin-bottom: 10px;

                            font-family: var(--helveticaNeue);
                        }

                        h5 {
                            color: #000000;
                            font-size: 20px;
                            line-height: 27px;
                            font-weight: 400;
                            padding-bottom: 10px;

                            font-family: var(--helveticaNeue);
                        }

                        p {
                            color: #999999;
                            font-family: var(--helveticaNeue);
                            padding-right: 5%;
                            font-size: 18px;
                            line-height: 28px;
                            font-weight: 400;

                            @media #{$media-1440} {
                                font-size: 17px;
                            }

                            @media #{$media-1024} {
                                font-size: 15px;
                                line-height: 25px;
                            }
                        }
                    }
                }

                .digi_side_bar_functionality_ul {

                    display: flex;
                    flex-wrap: wrap;
                    margin-left: -5px;
                    margin-right: -5px;

                    li {
                        width: calc(25% - 10px);
                        margin: 5px;
                        background: rgba(220, 220, 220, 0.21);
                        border-radius: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 30px 20px;

                        @media #{$media-1024} {
                            width: calc(50% - 10px);
                        }

                        p {
                            color: #000000;
                            text-align: center;
                            font-size: 18px;
                            line-height: 25px;
                            font-weight: 500;
                            font-family: var(--helveticaNeue);

                            @media #{$media-1440} {
                                font-size: 17px;
                            }

                            @media #{$media-1440} {
                                font-size: 16px;
                            }
                        }
                    }
                }

                &:nth-child(2) {
                    border-top: 0;

                }


            }
        }

        .digi_side_bar_footer {
            background-color: #5e45ff;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 100px 0;

            h4 {
                font-family: var(--helveticaNeue);
                color: #ffffff;
                font-size: 32px;
                text-align: center;
                line-height: 51px;
                font-weight: 400;
                padding-bottom: 30px;
                text-transform: uppercase;

                @media #{$media-1440} {
                    font-size: 28px;
                }

                @media #{$media-1024} {
                    font-size: 25px;
                    line-height: 35px;
                    padding-inline: 15px;
                }
            }

            .digi_side_bar_footer_btn {
                font-family: var(--helveticaNeue);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 23px 45px;
                border-radius: 30px;
                border: 0;
                background-color: white;
                color: #000000;
                transition: all 0.4s ease-out;

                @media #{$media-1440} {
                    padding: 20px 40px;
                }

                @media #{$media-1024} {
                    padding: 18px 38px;
                }

                img {
                    margin-inline-start: 12px;
                }

                &:hover {
                    background: #d9d9d9;
                }
            }
        }


    }
}

.fade_in_opacity {
    opacity: 0;
    animation: fadeIn 1s forwards;
    /* Adjust the duration as needed */
    transform: translateX(20px);
}

/* Keyframes for the fade-in effect */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0px);
    }
}