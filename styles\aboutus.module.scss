@import "variable", "base";

//   {/* -----------Image section----------- */}

.about_img_section {
    width: 100%;

    img {
        width: 100%;
        display: block;
        object-fit: cover;
        height: auto;
    }
}

//   {/* -----------vision section----------- */}

.about_vision_section {
    width: 100%;
    // background-color: $blue;
    position: relative;
    z-index: 9;

    .about_container {
        .about_title_section {
            display: flex;
            align-items: stretch;
            flex-wrap: wrap;
            row-gap: 40px;
            margin-left: -2% !important;
            margin-right: -2% !important;

            @media #{$media-1440} {
                row-gap: 35px;
            }

            @media #{$media-1024} {
                row-gap: 30px;
            }

            @media #{$media-767} {
                row-gap: 25px;
            }

            .about_title_sec {
                width: 50%;
                padding: 0 2%;
                box-sizing: border-box;

                .about_title_logo_sec {
                    height: auto;
                    width: 87px;
                    width: 16.3%;

                    img {
                        height: auto;
                        width: 100%;
                        object-fit: cover;
                        display: block;
                    }

                    @media #{$media-1600} {
                        width: 65px;
                    }

                    @media #{$media-1440} {
                        width: 62px;
                    }

                    @media #{$media-1024} {
                        width: 55px;
                    }

                    @media #{$media-820} {
                        width: 45px;
                    }
                }

                h3 {
                    display: inline-block;
                    color: #ffffff;
                    text-align: left;
                    // font-size: 55px;
                    font-size: 3.4375rem;
                    line-height: 100%;
                    font-weight: 400;

                    @media #{$media-995} {
                        font-size: 25px;
                    }
                }

                p {
                    display: inline-block;
                    // font-family: var(--helveticaneueltarabicLight);
                    display: block;
                    color: #ffffff;
                    text-align: start;
                    font-size: 20px;
                    line-height: 26px;
                    font-weight: 400;
                    padding-inline-end: 80px;

                    @media #{$media-1600} {
                        font-size: 18px;
                    }

                    @media #{$media-1024} {
                        font-size: 16px;
                    }

                    @media #{$media-820} {
                        font-size: 14px;
                        padding-right: 0px;
                        line-height: 19px;
                        padding-top: 10px;
                    }

                    @media #{$media-767} {
                        line-height: 20px;
                    }

                    @media #{$media-500} {
                        font-size: 15px;
                    }
                }

                @media #{$media-500} {
                    width: 100%;
                }
            }
        }

        .about_value_sec {
            width: 100%;

            h3 {
                display: inline-block;

                color: #ffffff;
                // font-size: 55px;
                font-size: 3.4375rem;
                line-height: 100%;
                font-weight: 400;
            }

            .about_value_ul_sec {
                display: flex;
                flex-wrap: wrap;
                gap: 1%;
                list-style: none;
                width: 100%;

                li {
                    width: 19%;
                    box-sizing: border-box;
                    padding: 50px 3%;
                    display: flex;
                    flex-direction: column;
                    text-align: left;
                    border-radius: 10px;
                    border: 1px solid rgba(255, 255, 255, 0.404);

                    h5 {
                        display: inline-block;

                        color: #ffffff;
                        font-size: 26px;
                        font-weight: 400;
                        line-height: 100%;

                        @media #{$media-1600} {
                            font-size: 24px;
                        }

                        @media #{$media-1024} {
                            font-size: 20px;
                        }

                        @media #{$media-820} {
                            font-size: 19px;
                        }
                    }

                    p {
                        display: inline-block;

                        padding-top: 10px;
                        color: #ffffff;
                        font-size: 19px;
                        font-weight: 400;
                        line-height: 100%;

                        @media #{$media-1600} {
                            font-size: 18px;
                        }

                        @media #{$media-1024} {
                            font-size: 16px;
                        }
                    }

                    @media #{$media-1600} {
                        padding: 40px 3%;
                    }

                    @media #{$media-1440} {
                        padding: 30px 3%;
                    }

                    @media #{$media-1024} {
                        padding: 25px 3%;
                    }

                    @media #{$media-820} {
                        padding: 15px 2%;
                    }

                    @media #{$media-500} {
                        width: 49%;
                        padding: 15px 5%;
                    }
                }

                @media #{$media-500} {
                    gap: 2%;
                    row-gap: 10px;
                    padding-top: 20px;
                }
            }
        }
    }
}

//   {/* -----------recognition section----------- */}

.about_recognition_section {
    // background-color: #212035;
    width: 100%;

    .about_recognition_container {
        .about_recognition_head_sec {
            width: 100%;
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            justify-content: space-between;

            h4 {
                color: #ffffff;
                font-size: 3.4375rem;
                font-weight: 400;

                @media #{$media-700} {
                    // width: 100%;
                    margin-bottom: 20px;
                }
            }

            .about_recognition_swiper_btn_sec {
                display: flex;
                gap: 20px;

                span {
                    position: unset;
                    margin: 0;

                    img {
                        width: 50%;
                    }

                    &:hover {
                        border: 1px solid rgb(121, 112, 255);
                    }

                    &:hover img {
                        filter: invert(0) brightness(1.5) !important;
                    }
                }

                @media #{$media-1440} {
                    gap: 15px;
                }

                @media #{$media-768} {
                    gap: 10px;
                }
            }
        }

        .about_recognition_swiper_sec {
            width: 100%;
            // display: flex;
            // gap: 30px;
            margin-top: 50px;

            @media #{$media-700} {
                margin-top: 30px;
            }

            .about_recognition_swiper_card {
                width: 100%;
                text-align: center;
                padding: 40px 3%;
                box-sizing: border-box;
                border: 1px solid rgba(100, 74, 255, 1);
                border-radius: 15px;
                position: relative;
                min-height: 440px;
                overflow: hidden;

                @media #{$media-1600} {
                    min-height: 400px;
                    padding: 25px 3%;
                }

                @media #{$media-1024} {
                    min-height: 390px;
                }

                @media #{$media-820} {
                    min-height: 330px;
                }

                @media #{$media-767} {
                    min-height: 310px;
                }

                .about_recognition_swiper_card_logo {
                    height: auto;
                    width: 60%;
                    margin: auto;
                    height: 212px;
                    display: flex;
                    align-items: center;

                    @media #{$media-1024} {
                        height: 170px;
                    }

                    img {
                        height: auto;
                        width: 100%;
                        object-fit: contain;
                        display: block;
                        z-index: 5;
                        position: relative;
                    }

                    @media #{$media-500} {
                        width: 50%;
                    }
                }

                .about_recognition_swiper_card_title {
                    h5 {
                        // font-size: 30px;
                        color: $white;
                        font-size: 1.875rem;
                        line-height: 132.54%;
                        font-weight: 400;
                        z-index: 10;
                        position: relative;
                        margin-top: 10px;

                        @media #{$media-767} {
                            font-size: 2.5rem;
                        }
                    }
                }

                p {
                    color: #ffffff;
                    font-size: 20px;
                    line-height: 132.54%;
                    font-weight: 400;
                    z-index: 10;
                    position: relative;
                    padding-left: 50px;
                    padding-right: 50px;

                    @media #{$media-1280} {
                        font-size: 17px;
                    }

                    @media #{$media-820} {
                        font-size: 16px;
                        padding-left: 0;
                        padding-right: 0;
                    }
                }

                &::after {
                    position: absolute;
                    content: "";
                    top: -2px;
                    left: -2px;
                    background-image: linear-gradient(110deg,
                            rgb(33 32 53 / 82%),
                            rgba(168, 148, 255, 0));

                    // 90deg, rgba(194, 153, 255, 0.5) 0%, rgba(100, 74, 255, 1) 100%

                    height: calc(100% + 1px);
                    width: 100%;
                    display: block;
                    z-index: 0;
                }
            }
        }
    }
}

.about_recognition_swiper_btn_sec {
    span {
        img {
            width: 34% !important;

            @media #{$media-767} {
                width: auto !important;
            }
        }
    }
}

.about_title_sec {


    .mission_vission_block {
        border-radius: 10px;
        height: 100%;
        position: relative;
        background: linear-gradient(180deg,
                rgba(186, 178, 234, 0.2) 0%,
                rgba(171, 163, 223, 1) 100%);

        .mission_vission_block_inside {
            width: calc(100% - 2px);
            height: calc(100% - 2px);
            top: 1px;
            inset-inline-start: 1px;
            padding: 50px 50px 50px 60px;
            background-color: rgb(94, 69, 255);
            border-radius: 10px;
            position: relative;
            overflow: hidden;

            &::after {
                content: '';
                position: absolute;
                background-image: url(../public/images/mision_bx_after.png);
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center right;
                inset-inline-end: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 100%;
                width: 14%;
                z-index: 1;

                /* Ensure this is above the block */
                @media #{$media-767} {
                    inset-inline-end: -2px;
                }
            }

            @media #{$media-1440} {
                padding: 45px 45px 45px 55px;
            }

            @media #{$media-1024} {
                padding: 35px;
            }

            @media #{$media-995} {
                padding: 30px;
            }
        }

    }

    &:first-child .mission_vission_block .mission_vission_block_inside {
        background-image: url(../public/images/vision_bg.png);
        background-size: cover;
        background-repeat: no-repeat;
    }
}

.value_ul {
    margin: 0 -2%;
    display: flex;
    flex-wrap: wrap;

    li {
        list-style: none;
        width: 20%;
        min-height: 430px;
        padding-left: 2%;
        padding-right: 2%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        transition: all 0.5s ease;

        @media #{$media-700} {
            min-height: 210px;
        }

        .value_head {
            transition: all 1s ease;

            h3 {
                font-size: 2.188rem;
                color: $white;
                text-align: center;
                width: 100%;
                font-family: var(--helveticaneueltarabicBold);
                font-weight: 600;
                line-height: 115%;

                @media #{$media-700} {
                    margin-bottom: 1px;
                }

                @media #{$media-500} {
                    font-size: 2.4rem;
                    line-height: 2.9rem;
                }
            }

            h4 {
                font-size: 2.188rem;
                color: $white;
                text-align: center;
                width: 100%;
                font-family: var(--helveticaneueltarabicBold);
                font-weight: 600;

                @media #{$media-700} {
                    font-size: 2rem;
                }

            }
        }

        .value_content {
            height: 0;
            width: 100%;
            overflow: hidden;
            transition: all 0.5s ease;
            overflow: hidden;

            p {

                width: 100%;
                height: auto;
                opacity: 1;
                color: #ffffff;
                text-align: center;
                font-size: 18px;
                line-height: 120%;
                font-weight: 400;
                padding-top: 15px;

                @media #{$media-1400} {
                    font-size: 16px;

                }

                @media #{$media-1300} {
                    font-size: 14px;

                }

                @media #{$media-700} {
                    font-size: 13px;

                }
            }
        }

        &:hover {
            background-color: #5e45ff;
        }

        &:hover .value_content {
            height: var(--dynamic-height);

            @media #{$media-700} {
                height: auto;
            }
        }


        // &:nth-of-type(1):hover .value_content {
        //     height: 165px;

        //     @media #{$media-1400} {
        //         height: 150px;
        //     }
        //     @media #{$media-1200} {
        //         height: 160px;
        //     }
        //     @media #{$media-700} {
        //         height: auto;
        //     }
        // }

        // &:nth-of-type(2):hover .value_content {
        //     height: 230px;
        //     @media #{$media-1400} {
        //         height: 210px;
        //     }
        //     @media #{$media-1200} {
        //         height: 225px;
        //     }
        //     @media #{$media-700} {
        //         height: auto;
        //     }
        // }

        // &:nth-of-type(3):hover .value_content {
        //     height: 300px;
        //     @media #{$media-1400} {
        //         height: 265px;
        //     }
        //     @media #{$media-1200} {
        //         height: 290px;
        //     }
        //     @media #{$media-700} {
        //         height: auto;
        //     }
        // }

        // &:nth-of-type(4):hover .value_content {
        //     height: 235px;
        //     @media #{$media-1400} {
        //         height: 210px;
        //     }
        //     @media #{$media-1200} {
        //         height: 210px;
        //     }
        //     @media #{$media-700} {
        //         height: auto;
        //     }
        // }

        // &:nth-of-type(5):hover .value_content {
        //     height: 100px;
        //     @media #{$media-1200} {
        //         height: 110px;
        //     }
        //     @media #{$media-700} {
        //         height: auto;
        //     }
        // }


        .value_content>p {
            width: 100%;
            height: auto;
            opacity: 1;
            color: #ffffff;
            text-align: center;
            font-size: 1.15rem;
            line-height: 120%;
            font-weight: 400;
            padding-top: 10px;


            @media #{$media-1600} {
                font-size: 1.3rem;
                line-height: 130%;

            }

            @media #{$media-700} {
                font-size: 14px;
                line-height: 140%;

            }
        }

        &::after {
            content: "";
            display: block;
            width: 28px;
            height: 43px;
            background: url(../public/images/diamond_1.png) center center no-repeat;
            background-size: contain;
            position: absolute;
            inset-inline-end: -13px;
            z-index: 5;
            top: 50%;
            margin-top: -21px;

            @media #{$media-1024} {
                width: 15px;
            }

            @media #{$media-700} {
                display: none;
            }
        }

        @media #{$media-700} {
            width: 48%;
            margin-bottom: 10px;
            background: #212035;
            padding: 2%;
            margin: 1%;

        }

        @media #{$media-500} {
            background: #212035;
            padding: 15px;
            margin: 1%;

        }

    }

    &> :last-child {
        &::after {
            display: none;
        }
    }

    @media #{$media-700} {
        padding-top: 0px;
    }
}

.time_line {
    .about_recognition_head_sec {
        h2 {
            text-transform: uppercase;
        }
    }

    .time_line_block {
        padding: 0 25px;
        display: flex;
        flex-direction: column;
        align-items: center;

        h3 {
            font-size: 1.625rem;
            color: #AEAEE8;
            width: 100%;
            font-weight: 400;
            text-align: center;
            padding-top: 40px;
            position: relative;

            @media #{$media-1600} {
                font-size: 1.8rem;
                padding-top: 30px;
            }

            @media #{$media-767} {
                font-family: var(--helveticaNeue);
                font-size: 2.2rem;
            }

            &::after {
                content: '';
                position: absolute;
                top: -18px;
                width: 25px;
                height: 40px;
                background-image: url(/images/diamond_2.png);
                background-size: contain;
                background-repeat: no-repeat;
                inset-inline-start: 50%;
                transform: translateX(-50%);
                direction: ltr;

                @media #{$media-1600} {
                    width: 20px;
                    height: 30px;
                }
            }
        }

        h4 {
            font-size: 1.375rem;
            color: #ffffff;
            width: 100%;
            font-weight: 400;
            margin-bottom: 15px;
            margin-top: 15px;
            text-transform: uppercase;
            text-align: center;

            @media #{$media-1600} {
                font-size: 1.6rem;
            }

            @media #{$media-767} {
                font-family: var(--helveticaNeue);
                font-size: 2.2rem;
            }
        }

        .time_line_icon_block {
            width: auto;
            height: 80px;
            margin-bottom: 40px;
            display: flex;
            align-items: center;

            img {
                width: auto;
                height: auto;
            }
        }

        p {
            font-size: 1.15rem;
            color: #ffffff;
            font-family: var(--helveticaNeue);
            line-height: 1.8rem;
            text-align: center;
            max-width: 80%;

            @media #{$media-1600} {
                font-size: 1.3rem;
            }

            @media #{$media-1024} {
                padding-inline-end: 0px;
            }

            @media #{$media-767} {
                font-size: 1.9rem;
                line-height: 2.4rem;
                max-width: 100%;
            }
        }
    }

    .history_slider {
        position: relative;

        &::after {
            content: "";
            display: block;
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, );
            left: 0;
            position: absolute;
            top: 140px;

            @media #{$media-1600} {
                top: 136px;
            }

            @media #{$media-600} {
                top: 130px;
            }
        }
    }
}

.about_our_team_section {
    width: 100%;
    background-color: white;

    position: relative;

    @media #{$media-767} {
        padding-bottom: 65px;
    }

    h3 {
        color: #1a1831;
        font-size: 3.438rem;
        font-weight: 400;

        @media #{$media-767} {
            font-size: 3.8rem;
        }
    }

    .team_title {
        color: #1a1831;
        font-size: 2.438rem;
        font-weight: 400;
        display: block;

        @media #{$media-767} {
            font-size: 3.8rem;
        }
    }
    h3+.team_title {
        margin-top: 40px;
    }

    // {/* ========= Swiper btn=========== */}

    .about_our_team_swiper_btn_sec {
        position: absolute;
        top: 54%;
        left: 0%;
        width: 100%;

        @media #{$media-1600} {
            top: 55%;
        }

        @media #{$media-820} {
            bottom: 60px;
            top: auto;
        }

        .about_our_team_swiper_container {
            display: flex;
            gap: 20px;

            span {
                position: unset;
                display: inline-flex;
                margin: 0;

                img {
                    width: 100%;
                    display: block;

                    @media #{$media-1600} {
                        width: 62%;
                    }

                    @media #{$media-767} {
                        width: auto;
                    }
                }

                &:hover {
                    background-color: #5e45ff !important;

                }

                &:hover img {
                    filter: invert(0) brightness(4) !important;
                }
            }

            @media #{$media-1440} {
                gap: 15px;
            }

            @media #{$media-768} {
                gap: 10px;
            }
        }
    }

    // {/* =========Detailed Swiper=========== */}

    .about_our_team_content_sec {
        width: 100%;
        background-size: 35% !important;
        background-position: right top;
        background-repeat: no-repeat;

        @media #{$media-1600} {
            background-size: 45% !important;
        }

        @media #{$media-1024} {
            background-size: 55% !important;
            background-position-y: 40%;
        }

        @media #{$media-820} {
            background-size: 50% !important;
            background-position-y: 10%;
        }

        @media #{$media-767} {
            background-image: none !important;
        }

        .about_our_team_container {
            width: 100%;
            display: flex;

            .about_our_team_left_content_sec {
                margin-top: 70px;
                width: 50%;
                padding-bottom: 450px;

                @media #{$media-1600} {
                    padding-bottom: 500px;
                }

                @media #{$media-820} {
                    margin-top: 50px;
                    width: 55%;
                    padding-bottom: 500px;
                }

                @media #{$media-767} {
                    width: 100%;
                    padding-bottom: 0;
                    margin-top: 20px;
                }

                span {
                    color: #1e1e2c;
                    font-family: var(--helveticaNeue);
                    font-size: 1rem;
                    line-height: 26px;
                    background: #f5f3ff;
                    border-radius: 20px;
                    padding: 8px 26px;
                    font-weight: 400;

                    @media #{$media-820} {
                        font-size: 1.5rem;
                    }
                }

                .about_our_team_left_title_sec {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 35px;
                    width: 88%;

                    @media #{$media-1024} {
                        width: auto;
                        justify-content: start;
                        gap: 25px;
                    }

                    @media #{$media-767} {
                        width: 100%;
                        justify-content: space-between;
                    }

                    .about_our_team_left_title {
                        display: inline-flex;
                        flex-direction: column;

                        h4 {
                            color: #1e1e2c;
                            font-size: 2.188rem;
                            font-weight: 400;
                            display: block;

                            @media #{$media-1024} {
                                font-size: 2.7rem;
                            }
                        }

                        h6 {
                            margin-top: 10px;

                            display: block;
                            color: #1e1e2c;
                            font-size: 1.438rem;
                            font-weight: 300;
                            font-family: var(--helveticaNeue);

                            @media #{$media-1024} {
                                font-size: 2rem;
                            }
                        }
                    }

                    .about_our_team_left_linkedin {
                        width: 30px;

                        &:hover {
                            // filter: contrast(0);
                            transform: scale(1.1);
                        }
                    }
                }

                &>p {
                    margin-top: 20px;
                    color: #1e1e2c;
                    font-size: 16px;
                    line-height: 26px;
                    font-weight: 400;
                    font-family: var(--helveticaNeue);
                    width: 88%;
                    max-height: 180px;

                    @media #{$media-1200} {
                        font-size: 15px;
                        line-height: 24px;
                    }

                    @media #{$media-820} {
                        font-size: 14px;
                        line-height: 22px;
                        max-height: unset;
                    }

                    @media #{$media-767} {
                        padding-bottom: 20px;
                        width: 100%;
                    }
                }
            }

            .about_our_team_right_content_sec {
                width: 50%;

                .about_our_team_right_profile {
                    width: 100%;
                }
            }
        }
    }

    // {/* =========Simplefied Swiper=========== */}

    .about_our_team_content_profile_sec {
        width: 100%;
        z-index: 12;
        bottom: 0%;
        padding-bottom: 100px;
        position: absolute;
        background-image: linear-gradient(0deg, #ffffff 77%, transparent);
        padding-inline-start: 2%;
        // padding-right: 2%;
        bottom: 0px;


        @media #{$media-1600} {
            bottom: 50px;
            background-image: linear-gradient(0deg, #ffffff 77%, transparent);
            padding-bottom: 50px;
        }

        @media #{$media-820} {
            padding-bottom: 60px;
            padding-left: 3%;
            padding-right: 3%;
            background-image: linear-gradient(0deg, #ffffff 90%, transparent);
        }

        @media #{$media-767} {
            position: initial;
        }

        .about_our_team_content_profile_card {
            color: #1a1831;
            cursor: pointer;

            .about_our_team_content_profile_photo_sec {
                width: 100%;
                border: 1px solid #aba3df;
                border-radius: 15px;
                // overflow: hidden;
                background-color: white;
                position: relative;
                height: 215px;
                overflow: hidden;

                img {
                    display: block;
                    position: relative;
                    z-index: 2;
                    border-radius: 15px;
                    height: 100%;
                    object-fit: cover;
                    object-position: top;
                }

                &::after {
                    content: "";
                    position: absolute;
                    height: calc(100% + 2px);
                    width: calc(100% + 2px);
                    left: -1px;
                    top: -1px;
                    border-radius: 14px;

                    background-image: linear-gradient(to bottom,
                            rgba(255, 255, 255, 0.884),
                            transparent);
                }
            }

            .about_our_team_content_profile_data_sec {
                width: 100%;
                display: flex;
                flex-direction: column;

                h4 {
                    color: #1a1831;
                    font-size: 20px;

                    text-align: center;
                    padding-top: 20px;
                    font-family: var(--helveticaneuelt_arabic_55);

                    @media #{$media-767} {
                        font-size: 16px;
                        font-family: var(--helveticaNeue);
                        padding-top: 15px;
                    }
                }

                span {
                    color: #1a1831;
                    font-size: 16px;
                    line-height: 20px;
                    font-weight: 400;
                    text-align: center;
                    padding-top: 5px;
                    width: 100%;
                    font-family: var(--helveticaNeue);

                    @media #{$media-767} {
                        width: 80%;
                        margin: 0 auto;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

.about_overview {
    position: relative;

    .abt_vector {
        position: absolute;
        inset-inline-end: 0;
        top: 0;
    }
}

.prev_awards_btn,
.next_awards_btn {
    padding: 34% !important;

    &:hover {
        background-color: #5e45ff !important;
    }

}

.team_mob_image {
    @media #{$media-767} {
        max-height: 400px;
        overflow: hidden;
    }

    img {
        @media #{$media-767} {
            height: 100%;
            object-fit: cover;
            object-position: top;
        }
    }
}
.sec_mob_padding_top{
    @media #{$media-767} {
        padding-top: 40px;
    }
}