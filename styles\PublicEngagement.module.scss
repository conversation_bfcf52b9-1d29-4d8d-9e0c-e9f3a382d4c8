@import "variable", "base";

.public_engagement_sub_domain_section {
    width: 100%;
    background-color: #f5f3ff;

    .public_engagement_sub_domain_container {
        width: 100%;

        h3 {
            color: #2a2656;
            // font-size: 50px;
            font-size: 3.125rem;
            font-weight: 400;
        }

        p {
            padding-top: 30px;
            color: #2a2656;
            font-size: 16px;
            line-height: 20px;
            font-weight: 400;

            @media #{$media-950} {


                padding-top: 20px;

            }
        }

        .public_engagement_sub_domain_card_sec {
            display: flex;
            flex-wrap: wrap;
            gap: 2%;
            row-gap: 25px;
            list-style: none;
            padding-top: 55px;

            h4 {
                color: #ffffff;
                // font-size: 30px;
                font-size: 1.375rem;
                line-height: 25px;
                font-weight: 400 !important;
                width: calc(100% - 120px);
                display: inline-block;
                font-family: var(--helveticaneueltarabicLight);
                word-wrap: break-word;
                white-space: wrap;
                padding-left: 15px;

                @media #{$media-1024} {
                    line-height: 130%;
                }

                @media #{$media-950} {
                    padding-bottom: 10px;
                    font-size: 1.65rem;
                }

                @media #{$media-767} {
                    font-size: 2rem;
                    padding-bottom: 0;
                }
            }

            li {
                width: 32%;
                background-color: #5e45ff;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                box-sizing: border-box;

                .public_engagement_card {
                    padding: 55px 5% 20px 5%;

                    @media #{$media-767} {
                        padding: 15px 20px;
                    }
                }

                .public_engagement_sub_domain_card_logo_sec {
                    display: flex;
                    align-items: center;

                    .public_engagement_sub_domain_card_logo {
                        height: auto;
                        width: 120px;

                        img {
                            height: auto;
                            display: block;
                            object-fit: contain;

                            @media #{$media-820} {
                                width: 100px;
                                height: auto;
                            }

                            @media #{$media-767} {
                                width: 80px;
                            }
                        }

                        @media #{$media-1024} {
                            width: 80%;
                        }

                        @media #{$media-950} {
                            width: 70%;
                        }

                        @media #{$media-820} {
                            width: 35%;
                        }

                        @media #{$media-500} {
                            width: 25%;

                        }
                    }
                }

                .public_engagement_sub_domain_card_footer_sec {
                    position: unset;
                    display: flex;
                    justify-content: flex-end;
                    align-items: flex-end;

                    .arrow_btn {
                        background-color: rgb(255, 255, 255) !important;
                        position: unset;
                        margin: 0;
                        width: 46px;
                        height: 46px;
                        display: inline-flex;
                        margin-top: -15px;
                        border: none;

                        img {
                            width: 50%;
                        }

                        @media #{$media-1024} {
                            width: 40px;
                            height: 40px;
                        }

                        @media #{$media-500} {
                            width: 35px;
                            height: 35px;
                        }
                    }

                    @media #{$media-1280} {
                        padding-top: 110px;
                    }

                    @media #{$media-1024} {
                        padding-top: 50px;
                    }

                    @media #{$media-820} {
                        padding-top: 10px;
                    }
                }

                @media #{$media-950} {
                    width: 49%;
                }

                @media #{$media-767} {
                    width: 100%;

                }

            }

            @media #{$media-1024} {

                row-gap: 20px;
            }

            @media #{$media-500} {
                row-gap: 15px;
                padding-top: 30px;
            }
        }
    }
}

.why_section {
    background-image: url(/images/community_bg.png);
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: cover;
    background-color: #222345;

    .why_items {
        display: flex;
        flex-wrap: wrap;

        .why_items_left {
            width: 35%;
            padding-inline-end: 25px;

            @media #{$media-767} {
                width: 100%;
            }

            h3 {
                color: #ffffff;
                font-size: 3.438rem;
                line-height: normal;
                font-family: var(--helveticaneueltarabicLight);

                @media #{$media-767} {
                    font-size: 3rem;
                    margin-bottom: 20px;
                }
            }
        }

        .why_items_right {
            width: 65%;

            @media #{$media-767} {
                width: 100%;
            }

            ul {
                margin-inline-start: 15px;

                li {
                    font-family: var(--helveticaneueltarabicLight);
                    font-size: 1.25rem;
                    line-height: 1.625rem;
                    font-weight: 300;
                    color: #ffffff;
                    padding-inline-start: 15px;
                    position: relative;
                    list-style-image: url('/images/diamond_3.png');

                    @media #{$media-767} {
                        font-size: 1.8rem;
                        line-height: 2.6rem;
                    }
                }

                li+li {
                    margin-top: 10px;
                }
            }
        }
    }

    .why_items+.why_items {
        margin-top: 80px;
        border-top: 1px solid #ffffff;
        padding-top: 80px;

        @media #{$media-1600} {
            margin-top: 60px;
            padding-top: 60px;
        }
        @media #{$media-1024} {
            margin-top: 50px;
            padding-top: 50px;
        }
        @media #{$media-600} {
            margin-top: 40px;
            padding-top: 40px;
        }
    }
}