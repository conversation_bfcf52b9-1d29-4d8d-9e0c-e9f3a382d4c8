@import "variable", "base";



.media_resources_card_section {
    width: 100%;
    background-color: white;

    .media_resources_card_container {
        width: 100%;

        h3 {
            color: #2a2656;
            text-align: left;
            font-size: 40px;
            line-height: 100%;
        
            @media #{$media-820} {
                font-size:25px;
            }
        }

        .knowledge_center_container {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 1%;
            row-gap: 15px;
            list-style: none;

            .knowledge_center_card_body {
                width: 32% !important;
                // background: #F4F4F4;
                box-shadow: none !important;
                padding: 30px 3% 40px 3%;

                @media #{$media-820} {
                    width: 100% !important;
                    padding: 25px 5% ;
                }
            }
        }
    }
}