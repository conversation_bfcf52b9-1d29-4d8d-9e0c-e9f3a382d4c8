import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { min } from "date-fns";

// Register required chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const ShareChart = ({ chartdata }) => {
  // Create gradient functions inside the component
  const createGradient = (ctx, colorStart, colorEnd) => {
    const gradient = ctx.createLinearGradient(0, 0, 0, ctx.canvas.clientHeight);
    gradient.addColorStop(0, colorStart);
    gradient.addColorStop(1, colorEnd);
    return gradient;
  };

  // Chart Data
  const data = {
    labels: ["Jun", "Jul", "Aug", "Sep", "Oct", "Nov"],
    datasets: [
      {
        label: "High Price",
        data: [
          chartdata.lastTradePrice,
          chartdata.highPrice,
          chartdata.prevClosePrice,
          chartdata.bidPrice,
          chartdata.high52WeeksPrice,
        ],
        borderWidth: 2,
        pointRadius: 2,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderWidth: 4,
        borderColor: "#fdb236", // Using the exact color from PHP version
        fill: true,
        backgroundColor: function(context) {
          const chart = context.chart;
          const { ctx } = chart;
          return createGradient(ctx, "rgba(195, 113, 239, 0.15)", "rgba(0, 0, 0, 0)");
        },
        tension: 0.4, // This is equivalent to cubicInterpolationMode: 'monotone'
      },
      {
        label: "Low Price",
        data: [
          chartdata.lowPrice,
          chartdata.askPrice,
          chartdata.low52WeeksPrice,
          chartdata.startOfYearPrice,
          chartdata.closePrice,
        ],
        borderWidth: 2,
        pointRadius: 2,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderWidth: 4,
        borderColor: "#5e45ff", // Using the exact color from PHP version
        fill: true,
        backgroundColor: function(context) {
          const chart = context.chart;
          const { ctx } = chart;
          return createGradient(ctx, "rgba(51, 169, 247, 0.3)", "rgba(0, 0, 0, 0)");
        },
        tension: 0.4, // This is equivalent to cubicInterpolationMode: 'monotone'
      },
    ],
  };

  // Chart Options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // Hide legend as in PHP version
      },
      tooltip: {
        backgroundColor: "rgba(53, 27, 92, 0.8)",
        caretPadding: 5,
        boxWidth: 5,
        usePointStyle: "triangle",
        boxPadding: 3,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        min:160,
        ticks: {
          callback: function(value) {
            return "SAR " + value;
          },
          stepSize: 20, // Added from PHP version
        },
      },
      x: {
        grid: {
          display: false, // Remove x-axis grid lines
        },
        beginAtZero: true, // Added from PHP version
      },
    },
  };

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Line data={data} options={options} />
    </div>
  );
};

export default ShareChart;