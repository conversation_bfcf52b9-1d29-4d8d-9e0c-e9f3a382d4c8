import React, { useEffect, useRef } from "react";
import Buttion from "@/component/buttion/Buttion"; // Import custom Button component
import buttion from "@/styles/buttion.module.scss";
import gsap from "gsap";
// import styles from "./Marquee.module.css";
import useImageDimensions from "./ImageDimensions";
import Image from "next/image";
import comon from "@/styles/comon.module.scss";
import clients from "@/styles/clients.module.scss";
import Marquee from "react-fast-marquee";
import { useRouter } from "next/router";
import parse from "html-react-parser";

const MarqueeClient = ({ our_clients_title, our_clients, our_button }) => {
	const router = useRouter();
	  const language = router.locale === "ar" ? "ar" : "en";
	  const direction = language === "ar" ? "right" : "left";
    const handleLoad = (event) => {
      event.target.classList.remove("opacity-0");
    };
  //   const marqueeRef = useRef(null);

  //   useEffect(() => {
  //     if (typeof window === "undefined") return;

  //     const marquee = marqueeRef.current;
  //     const width = marquee.offsetWidth;

  //     gsap.set(marquee, { x: 0 });
  //     gsap.timeline({ repeat: -1 }).to(marquee, {
  //       x: -width,
  //       duration: 15,
  //       ease: "linear",
  //     });
  //   }, []);

  return (
    <>
      {our_clients_title &&
        <div
          className={`${comon.wrap} ${comon.pt_100} ${comon.pb_30} ${clients.client_sec}`}
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <h2 className={`${comon.h2} ${comon.text_white}`}>
            {our_clients_title && parse(our_clients_title)}
          </h2>
          {
            our_button &&(
              <Buttion
              aosType="fade-up"
              aosDuration={1500}
              text={our_button?.title} // Button text
              href={our_button?.url} // Navigation target for the button
              target={our_button?.target}
              moduleClass={buttion.blue} // Custom button styling
              imageSrc="/images/buttion_arrow.svg" // Image for button
            />
            )
          }
         
        </div>
      }
      {/* <div className={clients.marquee}>
			<div ref={marqueeRef} className={clients.client_marquee}>
			{our_clients && [...our_clients,...our_clients].map((src, index) => {
          const { width, height } = useImageDimensions(src);

          return (
			  <div key={index}>
				  {src && (
					  <div className={`${clients.client_logo_block}`} data-aos="fade-up" data-aos-duration="1000">
						  {src ? (
							  <Image
								  className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
								  onLoad={handleLoad}
								  src={src?.url}
								  alt={`client-logo-${index + 1}`}
								  width={src.width}
								  height={src.height}
							  />
						  ) : null }
					  </div>
				  )}
            </div>
          );
        })}
			</div>
		</div> */}
      <div className={clients.marquee}>
        <div className={clients.client_marquee}>
          <Marquee speed={100} loop={0} direction={direction}>
            {our_clients &&
              [...our_clients, ...our_clients].map((src, index) => (
                <div key={index} style={{paddingLeft:"10px", paddingRight :"10px"}}>
                  {src && (
                    <div
                      className={`${clients.client_logo_block}`}
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      {src ? (
                        <Image
                          className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                          onLoad={handleLoad}
                          src={src?.url}
                          alt={`client-logo-${index + 1}`}
                          width={src.width}
                          height={src.height}
                        />
                      ) : null}
                    </div>
                  )}
                </div>
              ))}
          </Marquee>
        </div>
      </div>
    </>
  );
};

export default MarqueeClient;
