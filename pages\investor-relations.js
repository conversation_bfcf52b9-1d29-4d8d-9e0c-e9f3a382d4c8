import InnerBanner from "@/component/InnerBanner";
import React, { useEffect, useState } from "react";
import style from "@/styles/Investor-relation.module.scss";
import OverviewTwoCol from "@/component/OverviewTwoCol";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import TabSection from "@/component/Tab-section";
import Overview from "@/component/Overview";
import InvestorsResourcesCard from "@/component/InvestorsReportCard";
import Image from "next/image";
import ShareChart from '@/component/shareChart'
import Buttion from "@/component/buttion/Buttion";
import { Swiper, SwiperSlide } from "swiper/react";
import { Fancybox } from "@fancyapps/ui";
import "@fancyapps/ui/dist/fancybox/fancybox.css";
import Box from "@mui/material/Box";
import Modal from "@mui/material/Modal";
import InquireNowIR from "@/component/IRform";
import { useRouter } from "next/router";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";

// import required modules
import { Navigation } from "swiper/modules";
import { Autoplay } from "swiper/modules";

import Yoast from "@/component/yoast";
import {
  getInvestorsRelations,  
  getMediaresourcesPostPosts,
  getIrMenuLinks,
  getShareInfo,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import xml2js from "xml2js";

const InvestorRelation = (props) => {
  const [open, setOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(null); // State to store selected video data
  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";

  const handleOpen = (video) => {
    setSelectedVideo(video); // Update the selected video data
    setOpen(true); // Open the modal
  };

  const handleClose = () => {
    setOpen(false); // Close the modal
    setSelectedVideo(null); // Clear the selected video
  };

  const popUpstyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "fit-content",
    border: "0 solid #000",
    boxShadow: 24,
    outline: "none",

    // p: 4,
  };
  useEffect(() => {
    // Initialize Fancybox when the component mounts
    Fancybox.bind("[data-fancybox]", {
      Toolbar: false,
      closeButton: "top",
    });
  }, []);

  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const yoastData = props?.IrData?.yoast_head_json;

  if (!props.IrData) {
    return null;
  }
  const pathtrim = props?.stocktable["soapenv:Envelope"]["soapenv:Body"]["p451:getDetailQuoteForCompanyResponse"].getDetailQuoteForCompanyReturn;

   const changeAmount = parseFloat(pathtrim?.changeAmount || 0).toFixed(2);
  const changePercentage = parseFloat(pathtrim?.changePercentage || 0).toFixed(2);

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.IrData &&
      props?.IrData?.acf &&
      props?.IrData?.acf?.banner_title &&
      (props?.IrData?.acf?.mob_banner_image ||
        props?.IrData?.acf?.banner_image ||
        props?.IrData?.acf?.breadcrumbs ||
        props?.IrData?.acf?.banner_viedo ||
        props?.IrData?.acf?.banner_side_left_image ||
        props?.IrData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.IrData?.acf?.banner_title}
          breadcrumb1={
            props?.IrData?.acf?.active_breadcrumbs === "yes"
              ? props?.IrData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.IrData?.acf?.mob_banner_image?.url
              : props?.IrData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.IrData?.acf?.banner_viedo?.url}
          banner_type={props?.IrData?.acf?.banner_type}
          sharechart={pathtrim}
        />
      ) : null}

      {/* =========Tab Section====== */}
      {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.investors_relations_menu && (
          <TabSection tabs={props?.IRMenuData?.investors_relations_menu} />
        )}
      {/* <TabSection tabs={Tab_section} /> */}
      {/*----------------- share info secion newly added---------------- */}
      {props &&
      props?.shareinfoData &&
      props?.shareinfoData?.acf &&
      (props?.shareinfoData?.acf?.share_info_title ||
        props?.shareinfoData?.acf?.stock_performance_title) ? (
        <section className={`${style.share_info_section}`}>
          <div
            className={`${style.share_info_container} ${comon.wrap} ${comon.pt_100}`}
          >
            {props &&
              props?.shareinfoData &&
              props?.shareinfoData?.acf &&
              props?.shareinfoData?.acf?.share_info_title && (
                <h3>
                  {props?.shareinfoData?.acf?.share_info_title &&
                    parse(props?.shareinfoData?.acf?.share_info_title)}
                </h3>
              )}
            <div className={`${style.share_info_content_sec} `}>
              {props && props?.stocktable && (
                <div className={style.share_info_left}>
                  {/* <Image src={props?.shareinfoData?.acf?.share_info_image?.url} width={594} height={501}  alt="" /> */}

                  <div className={style.inner_banner_share_chart_wrap}>
                    <div className={style.inner_banner_share_chart}>
                      <div className={style.diasmond}>
                        <Image
                          src="/images/diamond_new.svg"
                          alt=""
                          width={25}
                          height={40}
                        />
                      </div>
                      <div className={style.stock_info}>
                        {router.locale === "ar" ? (
                          <h5>
                            {pathtrim?.companyLongNameAr &&
                              parse(pathtrim?.companyLongNameAr)}
                          </h5>
                        ) : (
                          <h5>
                            {pathtrim?.companyLongName &&
                              parse(pathtrim?.companyLongName)}
                          </h5>
                        )}
                        <p>{pathtrim?.id && parse(pathtrim?.id)}</p>
                        <h6 className={`${style.stockData} ${style.down}`}>
                          <span>
                            {pathtrim?.lastTradePrice &&
                              parseFloat(pathtrim?.lastTradePrice).toFixed(2)}
                          </span>

                          {/* {changeAmount > 0 ? (
                            <Image
                              src="/images/stock_up.svg"
                              alt=""
                              width={28}
                              height={28}
                            />
                          ) : changeAmount <= 0 ? (
                            <Image
                              src="/images/stock_down.svg"
                              alt=""
                              width={28}
                              height={28}
                            />
                          ) : null} */}
                          <span
                            className={`changeTxt ${
                              changeAmount > 0
                                ? "upTxt"
                                : changeAmount <= 0
                                ? "downTxt"
                                : "normalTxt"
                            }`}
                          >
                            ({changeAmount}) ({changePercentage}%)
                          </span>
                        </h6>
                      </div>
                    </div>
                  </div>
                  <div className={style.share_graph_wrapper}>
                    <ShareChart chartdata={pathtrim} />
                  </div>
                </div>
              )}
              {props && props?.stocktable && (
                <div className={style.share_info_right}>
                  {props &&
                    props?.shareinfoData &&
                    props?.shareinfoData?.acf &&
                    props?.shareinfoData?.acf?.stock_performance_title && (
                      <h5>
                        {props?.shareinfoData?.acf?.stock_performance_title &&
                          parse(
                            props?.shareinfoData?.acf?.stock_performance_title
                          )}
                      </h5>
                    )}

                  <div className={style.table_sec}>
                    <table className={style.table}>
                      <tbody>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_1 &&
                              parse(props?.shareinfoData?.acf?.stock_text_1)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_1 &&
                              parseFloat(pathtrim?.lastTradePrice).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_2 &&
                              parse(props?.shareinfoData?.acf?.stock_text_2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_2 &&
                              parseFloat(pathtrim?.lastTradeQuantity).toFixed(
                                2
                              )}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_3 &&
                              parse(props?.shareinfoData?.acf?.stock_text_3)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_3 &&
                              parseFloat(pathtrim?.lowPrice).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_4 &&
                              parse(props?.shareinfoData?.acf?.stock_text_4)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_4 &&
                              parseFloat(pathtrim?.highPrice).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_5 &&
                              parse(props?.shareinfoData?.acf?.stock_text_5)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_5 &&
                              pathtrim?.noOfTrades}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_6 &&
                              parse(props?.shareinfoData?.acf?.stock_text_6)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_6 &&
                              parseFloat(pathtrim?.volumeTraded).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_7 &&
                              parse(props?.shareinfoData?.acf?.stock_text_7)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_7 &&
                              parseFloat(pathtrim?.avgTradeSize).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_8 &&
                              parse(props?.shareinfoData?.acf?.stock_text_8)}
                          </td>
                          <td
                            className={`changeTxt ${
                              changeAmount > 0
                                ? "upTxt"
                                : changeAmount <= 0
                                ? "downTxt"
                                : "normalTxt"
                            }`}
                          >
                            (
                            {props?.shareinfoData?.acf?.stock_text_8 &&
                              changeAmount}
                            )
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_9 &&
                              parse(props?.shareinfoData?.acf?.stock_text_9)}
                          </td>
                          <td
                            className={`changeTxt ${
                              changeAmount > 0
                                ? "upTxt"
                                : changeAmount <= 0
                                ? "downTxt"
                                : "normalTxt"
                            }`}
                          >
                            (
                            {props?.shareinfoData?.acf?.stock_text_9 &&
                              changePercentage}
                            )
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_10 &&
                              parse(props?.shareinfoData?.acf?.stock_text_10)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_10 &&
                              parseFloat(pathtrim?.change52week).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_11 &&
                              parse(props?.shareinfoData?.acf?.stock_text_11)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_11 &&
                              pathtrim?.high52weekDate}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_12 &&
                              parse(props?.shareinfoData?.acf?.stock_text_12)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_12 &&
                              parseFloat(pathtrim?.high52WeeksPrice).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_13 &&
                              parse(props?.shareinfoData?.acf?.stock_text_13)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_13 &&
                              pathtrim?.low52weekDate}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_14 &&
                              parse(props?.shareinfoData?.acf?.stock_text_14)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_14 &&
                              parseFloat(pathtrim?.low52WeeksPrice).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_15 &&
                              parse(props?.shareinfoData?.acf?.stock_text_15)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_15 &&
                              parseFloat(pathtrim?.startOfYearPrice).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_16 &&
                              parse(props?.shareinfoData?.acf?.stock_text_16)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_16 &&
                              parseFloat(pathtrim?.yearAgoPrice).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_17 &&
                              parse(props?.shareinfoData?.acf?.stock_text_17)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_17 &&
                              parseFloat(pathtrim?.closePrice).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_18 &&
                              parse(props?.shareinfoData?.acf?.stock_text_18)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_18 &&
                              parseFloat(pathtrim?.earning).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_19 &&
                              parse(props?.shareinfoData?.acf?.stock_text_19)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_19 &&
                              parseFloat(pathtrim?.EPS).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_20 &&
                              parse(props?.shareinfoData?.acf?.stock_text_20)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_20 &&
                              parseFloat(pathtrim?.marketCap).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_21 &&
                              parse(props?.shareinfoData?.acf?.stock_text_21)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_21 &&
                              pathtrim?.numberOfShares}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_22 &&
                              parse(props?.shareinfoData?.acf?.stock_text_22)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_22 &&
                              pathtrim?.numberOfSharesFloated}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_23 &&
                              parse(props?.shareinfoData?.acf?.stock_text_23)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_23 &&
                              parseFloat(pathtrim?.PBValue).toFixed(2)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_24 &&
                              parse(props?.shareinfoData?.acf?.stock_text_24)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_24 &&
                              parseFloat(pathtrim?.PERatio).toFixed(2)}
                          </td>
                        </tr>
                        <tr>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_25 &&
                              parse(props?.shareinfoData?.acf?.stock_text_25)}
                          </td>
                          <td>
                            {props?.shareinfoData?.acf?.stock_text_25 &&
                              parseFloat(pathtrim?.bookValue).toFixed(2)}
                          </td>
                          <td></td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>
      ) : null}

      {/*-----------------end of share info secion newly added---------------- */}
      {/* ========Overview Section==== */}
      {props &&
      props?.IrData &&
      props?.IrData?.acf &&
      (props?.IrData?.acf?.overview_title ||
        props?.IrData?.acf?.overview_content) ? (
        <div className="iv_over_view">
          <OverviewTwoCol
            head={props?.IrData?.acf?.overview_title}
            paragraph={props?.IrData?.acf?.overview_content}
          />
        </div>
      ) : null}
      {props &&
      props?.IrData &&
      props?.IrData?.acf &&
      props?.IrData?.acf?.overview_video_listing ? (
        <div className={`${comon.wrap}`}>
          <div className={style.video_cols}>
            {props?.IrData?.acf?.overview_video_listing &&
              props?.IrData?.acf?.overview_video_listing.map(
                (video, valIndex) => (
                  <div
                    className={style.video_col}
                    key={valIndex}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    {video.video_cover_image && (
                      <div className={style.video_pic}>
                        <Image
                          src={video.video_cover_image?.url}
                          width={689}
                          height={395}
                          alt="Video Thumbnail"
                        />
                        <Link
                          href="#."
                          onClick={(e) => {
                            e.preventDefault(); // Prevent default link behavior
                            handleOpen(video); // Pass the video data
                          }}
                          className={style.video_icon}
                        ></Link>
                      </div>
                    )}

                    {video.video_title && (
                      <h3>{video.video_title && parse(video.video_title)}</h3>
                    )}
                  </div>
                )
              )}

            <Modal
              open={open}
              onClose={handleClose}
              aria-labelledby="modal-modal-title"
              aria-describedby="modal-modal-description"
            >
              <Box sx={popUpstyle}>
                <div className={style.modal_video_close} onClick={handleClose}>
                  <Image
                    src={"/images/close-white.png"}
                    height={50}
                    width={50}
                    alt="Close popup"
                  />
                </div>

                {selectedVideo && (
                  <video
                    src={selectedVideo.video_file?.url} // Use selected video file URL
                    controls
                    autoPlay
                    width={900}
                    height={500}
                  ></video>
                )}
              </Box>
            </Modal>
          </div>
        </div>
      ) : null}
      {/* =======Investor detials Card Blocks======= */}
      {props &&
      props?.IrData &&
        props?.IrData?.acf &&
        props?.IrData?.acf?.investor_resources_tax?.length > 0 &&
        (props?.IrData?.acf?.investor_resources_title ||
        props?.IrData?.acf?.investor_resources_button ) ? (
        <section
          className={`${style.investor_relation_card_block_section}  ${comon.pt_80}  `}
        >
          <div
            className={`${style.investor_relation_card_block_section_container} ${comon.wrap}  `}
          >
            <div
              className={`${style.investor_relation_card_block_head_sec} ${comon.investor_relation_card_block_head_sec}  `}
            >
              {props?.IrData?.acf?.investor_resources_title && (
                <h4 data-aos="fade-up" data-aos-duration="1000">
                  {props?.IrData?.acf?.investor_resources_title &&
                    parse(props?.IrData?.acf?.investor_resources_title)}
                </h4>
              )}
              {props?.IrData?.acf?.investor_resources_button && (
                <Buttion
                  text={props?.IrData?.acf?.investor_resources_button?.title}
                  href={props?.IrData?.acf?.investor_resources_button?.url}
                  imageSrc={"/images/btn-right-arrow.svg"}
                  icn_invert={1}
                />
              )}
            </div>
            {props && props?.IrData?.acf?.investor_resources_tax && (
              <ul
                className={`${style.investor_relation_company_announcement_card_sec}  ${comon.pt_50}  ${comon.pb_100}`}
              >
                <InvestorsResourcesCard cardData={props?.IrData?.acf?.investor_resources_tax} />
              </ul>
            )}
          </div>
        </section>
      ) : null}
      {/* ========Media Resource Section========= */}
      {props &&
      props?.IrData &&
      props?.IrData?.acf &&
      (props?.IrData?.acf?.media_resources_title ||
        props?.IrData?.acf?.media_resources_button ||
        props?.IrMediaData) ? (
        <section
          className={`${style.investor_relation_media_resource_section}`}
        >
          <div
            className={`${style.investor_relation_media_resource_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100}`}
          >
            <div
              className={`${style.investor_relation_media_resource_head_sec} ${comon.investor_relation_media_resource_head_sec} `}
            >
              {props?.IrData?.acf?.media_resources_title && (
                <h4 data-aos="fade-up" data-aos-duration="1000">
                  {props?.IrData?.acf?.media_resources_title &&
                    parse(props?.IrData?.acf?.media_resources_title)}
                </h4>
              )}
              {props?.IrData?.acf?.media_resources_button && (
                <Buttion
                  text={props?.IrData?.acf?.media_resources_button?.title}
                  href={props?.IrData?.acf?.media_resources_button?.url}
                  imageSrc={"/images/btn-right-arrow.svg"}
                  icn_invert={1}
                />
              )}
            </div>
            <div
              className={`${style.investor_relation_media_resource_card_swiper_sec}  `}
            >
              <span
                className={`${comon.custom_next} ${comon.custom_btn} ${comon.custom_btn_border} custom_prev_1  ${style.investor_relation_media_resource_card_swiper_prev_btn}  `}
              >
                <Image
                  className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                  src={`/images/prev_ic.svg`}
                  alt="call"
                  width={11}
                  height={11}
                />
              </span>

              <span
                className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} custom_next_1 ${style.investor_relation_media_resource_card_swiper_next_btn}  `}
              >
                <Image
                  className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                  src={`/images/next_ic.svg`}
                  alt="call"
                  width={11}
                  height={11}
                />
              </span>
              <Swiper
                data-aos="fade-up"
                data-aos-duration="1000"
                modules={[Navigation, Autoplay]}
                className="mySwiper"
                slidesPerView={3}
                spaceBetween={10}
                navigation={{
                  nextEl: ".custom_next_1",
                  prevEl: ".custom_prev_1",
                }}
                autoplay={{
                  delay: 4000,
                  disableOnInteraction: false,
                }}
                loop={true}
                speed={1000}
                dir={language == "ar" ? "rtl" : "ltr"}
                key={language}
                breakpoints={{
                  0: {
                    slidesPerView: 1,
                    spaceBetween: 10,
                  },
                  600: {
                    slidesPerView: 2,
                    spaceBetween: 10,
                  },
                  1024: {
                    slidesPerView: 3,
                    spaceBetween: 10,
                  },
                }}
              >
                {props?.IrMediaData.map((Mediadata, medIndex) => (
                  <SwiperSlide key={medIndex}>
                    <div
                      className={`${style.investor_relation_media_resources_card_list_img} `}
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      <Link href={Mediadata?.acf?.link || "#."} target="_blank">
                        {Mediadata?.acf?.media_image && (
                          <Image
                            src={Mediadata?.acf?.media_image?.url}
                            height={400}
                            width={500}
                            alt=""
                          />
                        )}
                        {Mediadata?.acf?.media_type === "video" && (
                          <div
                            className={`${style.investor_relation_media_resources_card_list_img_play_btn} `}
                          >
                            <Image
                              src={"/images/media-resource-img-play-btn.svg"}
                              height={95}
                              width={95}
                              alt=""
                            />
                          </div>
                        )}

                        <div
                          className={`${style.investor_relation_media_resources_card_list_data} `}
                        >
                          {Mediadata?.title && (
                            <h5>
                              {Mediadata?.title &&
                                parse(Mediadata?.title?.rendered)}
                            </h5>
                          )}
                          {Mediadata?.acf?.sub_title && (
                            <span>{Mediadata?.acf?.sub_title} </span>
                          )}
                          <p className={style.action_text}>
                            {Mediadata?.acf?.media_type === "video"
                              ? router.locale === "ar"
                                ? "شاهد الآن"
                                : "Watch Now!"
                              : router.locale === "ar"
                              ? "اضغط هنا"
                              : "Click Here"}
                          </p>
                        </div>
                      </Link>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
        </section>
      ) : null}

      {/* ===========Contact Form Section============= */}

      <InquireNowIR formtitle={props?.IrData?.title?.rendered} />
    </div>
  );
};

export default InvestorRelation;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const IrData = await getInvestorsRelations(locale);
  const IRMenuData = await getIrMenuLinks(locale);

  const query = ""; 
  const IrMediaPostData = await getMediaresourcesPostPosts(locale, query);
  let MediaresourcesPosts = [];
  if (IrData && IrData?.acf && Array.isArray(IrData?.acf?.media_resources)) {
    MediaresourcesPosts = IrData?.acf?.media_resources;
  }

  // Format Media Resourses for use in the component
  let IrMediaData = [];
  if (MediaresourcesPosts.length > 0) {
    IrMediaData = MediaresourcesPosts.map((id) =>
      IrMediaPostData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  //console.log('testingdata', locale)
  const soapRequest = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://services.RSS.tadawul.com">
        <soapenv:Header/>
        <soapenv:Body>
            <ser:getDetailQuoteForCompany>
                <companyId>9570</companyId>
                <secureKey>*********</secureKey>
            </ser:getDetailQuoteForCompany>
        </soapenv:Body>
    </soapenv:Envelope>`;

  const response = await fetch(
    "https://webservices.tadawul.com.sa/Tadawul_WebAPI/services/GetDetailQuote",
    {
      method: "POST",
      headers: {
        "Content-Type": "text/xml",
        SOAPAction: "getDetailQuoteForCompany",
      },
      body: soapRequest,
    }
  );

  const xmlText = await response.text(); // Get XML response as text

  // Convert XML to JSON
  const parser = new xml2js.Parser({ explicitArray: false });
  const jsonData = await parser.parseStringPromise(xmlText);
  const shareinfoData = await getShareInfo(locale);

  return {
    props: {
      IrData: IrData || null,
      IRMenuData: IRMenuData || null,
      // IrRepotData: IrRepotData || [],
      IrMediaData: IrMediaData || [],
      stocktable: jsonData || null,
      shareinfoData: shareinfoData || null,
    },
    revalidate: 10,
  };
};
