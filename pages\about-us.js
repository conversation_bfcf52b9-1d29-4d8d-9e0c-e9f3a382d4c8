import React, { useState, useEffect, useRef } from "react";
import aboutus from "@/styles/aboutus.module.scss";
import comon from "@/styles/comon.module.scss";
import InquireNow from "@/component/InquireNow";
import Image from "next/image";
import Buttion from "@/component/buttion/Buttion"; // Import custom Button component
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import Overview from "@/component/Overview";
import Clients from "@/component/MarqueeClient";

// import required modules
import { Autoplay, Pagination } from "swiper/modules";
import InnerBanner from "@/component/InnerBanner";
import Link from "next/link";
import TabSection from "@/component/Tab-section";
import DynamicImage from "@/component/DynamicImage";
import { useRouter } from "next/router";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/navigation";
import "swiper/css/thumbs";
import "swiper/css/pagination";
import { useInView } from "react-intersection-observer";

import { FreeMode, Navigation, Thumbs } from "swiper/modules";

import Yoast from "@/component/yoast";
import {
  getAbout,
  getAwardsPosts,
  getTeamPosts,
  getTeamCategories,
  getIrMenuLinks,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Index = (props) => {
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [thumbsSwiperAr, setThumbsSwiperAr] = useState(null);
  const [leaderThumbsSwiper, setLeaderThumbsSwiper] = useState(null);
  const [leaderThumbsSwiperAr, setLeaderThumbsSwiperAr] = useState(null);
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  const [isClient, setIsClient] = useState(false);
  const mainSwiperRef = useRef(null); // Ref to the main Swiper
  const mainSwiperRefAr = useRef(null); // Ref to the main Swiper
  const mainSwiperLeaderRef = useRef(null);
  const mainSwiperLeaderRefAr = useRef(null);
  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";

  useEffect(() => {
    if (!props?.aboutData?.acf?.values) return; // Ensure data exists

    setTimeout(() => {
      // Optional: Ensures elements are fully rendered before applying styles
      const listItems = document.querySelectorAll("li");

      listItems.forEach((li) => {
        const content = li.querySelector(".value_content");
        if (content) {
          const height = content.scrollHeight; // Get full height dynamically
          li.style.setProperty("--dynamic-height", `${height}px`);
        }
      });
    }, 100);
  }, [JSON.stringify(props?.aboutData?.acf?.values)]); // Depend only on relevant data

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setIsClient(true); // Ensures Swiper only loads on the client
  }, []);

  if (!isClient) return null; // Avoid rendering on the server

  // ============our team============

  const yoastData = props?.aboutData?.yoast_head_json;

  if (!props.aboutData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.aboutData &&
      props?.aboutData?.acf &&
      props?.aboutData?.acf?.banner_title &&
      (props?.aboutData?.acf?.mob_banner_image ||
        props?.aboutData?.acf?.banner_image ||
        props?.aboutData?.acf?.banner_viedo ||
        props?.aboutData?.acf?.breadcrumbs ||
        props?.aboutData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.aboutData?.acf?.banner_title}
          breadcrumb1={
            props?.aboutData?.acf?.active_breadcrumbs === "yes"
              ? props?.aboutData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.aboutData?.acf?.mob_banner_image?.url
              : props?.aboutData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.aboutData?.acf?.banner_viedo?.url}
          banner_type={props?.aboutData?.acf?.banner_type}
        />
      ) : null}

      {/* =========Tab Section====== */}
      {/* {props && props?.IRMenuData && props?.IRMenuData?.about_us_menu && (
        <TabSection tabs={props?.IRMenuData?.about_us_menu} />
      )} */}

      {/* -----------Overview section----------- */}
      {props &&
      props?.aboutData &&
      props?.aboutData?.acf &&
      (props?.aboutData?.acf?.overview_title ||
        props?.aboutData?.acf?.overview_content ||
        props?.aboutData?.acf?.engagement_in_numbers_title ||
        props?.aboutData?.acf?.engagement_in_numbers) ? (
        <div className={`about_overview ${aboutus.about_overview}`}>
          <div className={`${aboutus.abt_vector} ${comon.abt_vector}`}>
            <Image
              src="/images/about_vector.png"
              alt="call"
              width={253}
              height={830}
            />
          </div>
          <Overview
            head={props?.aboutData?.acf?.overview_title}
            paragraph={props?.aboutData?.acf?.overview_content}
            swiperhead={props?.aboutData?.acf?.engagement_in_numbers_title}
            swiperContents={props?.aboutData?.acf?.engagement_in_numbers}
          />
        </div>
      ) : null}

      {/* -----------Image section----------- */}
      {props &&
        props?.aboutData &&
        props?.aboutData?.acf &&
        props?.aboutData?.acf?.vision_mission && (
          <section
            style={{ background: " #5e45ff" }}
            className={`${aboutus.about_vision_section}`}
          >
            <div
              className={`${aboutus.about_container} ${comon.wrap} ${comon.pt_110}  ${comon.pb_150}`}
            >
              <div className={`${aboutus.about_title_section}`}>
                {props?.aboutData?.acf?.vision_mission &&
                  props?.aboutData?.acf?.vision_mission.map(
                    (vision, vIndex) => (
                      <div
                        className={`${aboutus.about_title_sec} ${comon.about_title_sec}`}
                        data-aos="fade-up"
                        data-aos-duration="2000"
                        key={vIndex}
                      >
                        <div className={`${aboutus.mission_vission_block}`}>
                          <div
                            className={`${aboutus.mission_vission_block_inside} ${comon.mission_vission_block_inside}`}
                          >
                            {vision?.vision_mission_image && (
                              <div
                                className={`${aboutus.about_title_logo_sec}`}
                              >
                                <Image
                                  src={vision?.vision_mission_image?.url}
                                  alt="call"
                                  width={87}
                                  height={73}
                                />{" "}
                              </div>
                            )}
                            {vision?.vision_mission_title && (
                              <h3 className={`${aboutus.pt_30}`}>
                                {" "}
                                {vision?.vision_mission_title &&
                                  parse(vision?.vision_mission_title)}
                              </h3>
                            )}
                            {vision?.vision_mission_content && (
                              <p className={`${aboutus.pt_30}`}>
                                {vision?.vision_mission_content &&
                                  parse(vision?.vision_mission_content)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  )}
              </div>
            </div>
          </section>
        )}

      {/* -----------Our Team section BoardMember ----------- */}

      <div className={comon.team_english}>
        <>
          {props && props.TeamData && props.TeamData.length > 0 && (
            <section
              className={`${aboutus.about_our_team_section} ${comon.about_our_team_section} ${comon.pt_100}  ${comon.pb_75}`}
            >
              {props?.aboutData?.acf?.our_team_title && (
                <div div className={comon.wrap}>
                  <h3 data-aos="fade-up" data-aos-duration="1000">
                    {props?.aboutData?.acf?.our_team_title &&
                      parse(props?.aboutData?.acf?.our_team_title)}
                  </h3>
                </div>
              )}
              {/* ===========Btn Section============== */}
              <div className={`${aboutus.about_our_team_swiper_btn_sec} `}>
                <div
                  className={` ${comon.wrap} ${aboutus.about_our_team_swiper_container}  `}
                >
                  <span
                    className={`${comon.custom_next} ${comon.custom_btn} ${comon.custom_btn_border} custom_prev_1`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/prev_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                  <span
                    className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} custom_next_1`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/next_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                </div>
              </div>

              {/* =========Detailed Swiper=========== */}

              {/* Main Swiper */}
              <Swiper
                loop={true}
                spaceBetween={10}
                navigation={{
                  nextEl: ".custom_next_1",
                  prevEl: ".custom_prev_1",
                }}
                thumbs={{ swiper: thumbsSwiper }}
                modules={[FreeMode, Navigation, Thumbs]}
                className="mySwiper2"
                onSwiper={(swiper) => (mainSwiperRef.current = swiper)} // Bind to ref
                dir={"ltr"}
              >
                {props?.TeamData &&
                  props?.TeamData.map((data, tindex) => (
                    <SwiperSlide key={tindex}>
                      <div
                        className={`${aboutus.about_our_team_content_sec} ${comon.about_our_team_content_sec}`}
                        style={{
                          backgroundImage: `url(${data?.acf?.team_image?.url})`,
                        }}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <div
                          className={`${aboutus.about_our_team_container} ${comon.wrap} `}
                        >
                          <div
                            className={aboutus.about_our_team_left_content_sec}
                            data-aos="fade-up"
                            data-aos-duration="1000"
                          >
                            {/* <span data-aos="fade-up" data-aos-duration="1000">
                              {data.categoryNames.join(", ")}
                            </span> */}
                            <div
                              className={aboutus.about_our_team_left_title_sec}
                            >
                              <div
                                className={`${aboutus.about_our_team_left_title} ${comon.about_our_team_left_title}`}
                              >
                                {data?.title && (
                                  <h4
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.title?.rendered}
                                  </h4>
                                )}

                                {data?.acf?.team_position && (
                                  <h6
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.acf?.team_position}
                                  </h6>
                                )}
                              </div>
                              {data?.acf?.team_linkedin && (
                                <Link
                                  href={data?.acf?.team_linkedin?.url}
                                  target={data?.acf?.team_linkedin?.target}
                                  className={
                                    aboutus.about_our_team_left_linkedin
                                  }
                                >
                                  <DynamicImage
                                    src="/images/linked_image.png"
                                    alt="Dynamic Image"
                                  />
                                </Link>
                              )}
                            </div>
                            {isMobile && data?.acf?.team_image && (
                              <div
                                className={`${aboutus.team_mob_image} team_mob_image`}
                              >
                                <DynamicImage
                                  src={data?.acf?.team_image?.url}
                                  alt="Dynamic Image"
                                />
                              </div>
                            )}
                            {data?.acf?.team_content && (
                              <p data-aos="fade-up" data-aos-duration="1000">
                                {parse(data?.acf?.team_content)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
              </Swiper>

              {/* Thumbnails Swiper */}
              <div
                className={`${aboutus.about_our_team_content_profile_sec} team_tumb`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <Swiper
                  onSwiper={setThumbsSwiper}
                  spaceBetween={30}
                  slidesPerView={8.2} // Adjust the number of thumbnails
                  freeMode={true}
                  watchSlidesProgress={true}
                  modules={[FreeMode, Navigation, Thumbs]}
                  className="mySwiper thumb_swiper center_tumb"
                  dir={"ltr"}
                  breakpoints={{
                    0: {
                      slidesPerView: 2,
                      spaceBetween: 20,
                    },
                    768: {
                      slidesPerView: 3,
                      spaceBetween: 20,
                    },
                    1024: {
                      slidesPerView: 4,
                      spaceBetween: 20,
                    },
                    1200: {
                      slidesPerView: 6.2,
                      spaceBetween: 30,
                    },
                    1600: {
                      slidesPerView: 8.2,
                      spaceBetween: 30,
                    },
                  }}
                >
                  {props?.TeamData &&
                    props?.TeamData.map((data, tindex) => (
                      <SwiperSlide>
                        <div
                          className={
                            aboutus.about_our_team_content_profile_card
                          }
                          key={tindex}
                        >
                          {data?.acf?.team_image && (
                            <div
                              className={`${aboutus.about_our_team_content_profile_photo_sec} about_our_team_content_profile_photo_sec`}
                            >
                              <DynamicImage
                                src={data?.acf?.team_image?.url}
                                alt="Dynamic Image"
                              />
                            </div>
                          )}
                          <div
                            className={
                              aboutus.about_our_team_content_profile_data_sec
                            }
                          >
                            {data?.title && (
                              <h4>{data?.title && data?.title?.rendered}</h4>
                            )}
                            {data?.acf?.team_position && (
                              <span>{data?.acf?.team_position}</span>
                            )}
                          </div>
                        </div>
                      </SwiperSlide>
                    ))}
                </Swiper>
              </div>
            </section>
          )}
        </>
      </div>

      {/* -----------Our Team section ar----------- */}

      <div className={comon.team_arabic}>
        <>
          {props && props.TeamData && props.TeamData.length > 0 && (
            <section
              className={`${aboutus.about_our_team_section} ${comon.about_our_team_section} ${comon.pt_100}  ${comon.pb_75}`}
            >
              {props?.aboutData?.acf?.our_team_title && (
                <div div className={comon.wrap}>
                  <h3 data-aos="fade-up" data-aos-duration="1000">
                    {props?.aboutData?.acf?.our_team_title &&
                      parse(props?.aboutData?.acf?.our_team_title)}
                  </h3>
                </div>
              )}
              {/* ===========Btn Section============== */}
              <div className={`${aboutus.about_our_team_swiper_btn_sec} `}>
                <div
                  className={` ${comon.wrap} ${aboutus.about_our_team_swiper_container}  `}
                >
                  <span
                    className={`${comon.custom_next} ${comon.custom_btn} ${comon.custom_btn_border} custom_prev_1_ar`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/prev_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                  <span
                    className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} custom_next_1_ar`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/next_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                </div>
              </div>

              {/* =========Detailed Swiper=========== */}

              {/* Main Swiper */}
              <Swiper
                loop={true}
                spaceBetween={10}
                navigation={{
                  nextEl: ".custom_next_1_ar",
                  prevEl: ".custom_prev_1_ar",
                }}
                thumbs={{ swiper: thumbsSwiperAr }}
                modules={[FreeMode, Navigation, Thumbs]}
                className="mySwiper2_ar"
                onSwiper={(swiper) => (mainSwiperRefAr.current = swiper)} // Bind to ref
                dir={"rtl"}
              >
                {props?.TeamData &&
                  props?.TeamData.map((data, tindex) => (
                    <SwiperSlide key={tindex}>
                      <div
                        className={`${aboutus.about_our_team_content_sec} ${comon.about_our_team_content_sec}`}
                        style={{
                          backgroundImage: `url(${data?.acf?.team_image?.url})`,
                        }}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <div
                          className={`${aboutus.about_our_team_container} ${comon.wrap} `}
                        >
                          <div
                            className={aboutus.about_our_team_left_content_sec}
                            data-aos="fade-up"
                            data-aos-duration="1000"
                          >
                            {/* <span data-aos="fade-up" data-aos-duration="1000">
                              {data.categoryNames.join(", ")}
                            </span> */}
                            <div
                              className={aboutus.about_our_team_left_title_sec}
                            >
                              <div
                                className={`${aboutus.about_our_team_left_title} ${comon.about_our_team_left_title}`}
                              >
                                {data?.title && (
                                  <h4
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.title?.rendered}
                                  </h4>
                                )}

                                {data?.acf?.team_position && (
                                  <h6
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.acf?.team_position}
                                  </h6>
                                )}
                              </div>
                              {data?.acf?.team_linkedin && (
                                <Link
                                  href={data?.acf?.team_linkedin?.url}
                                  target={data?.acf?.team_linkedin?.target}
                                  className={
                                    aboutus.about_our_team_left_linkedin
                                  }
                                >
                                  <DynamicImage
                                    src="/images/linked_image.png"
                                    alt="Dynamic Image"
                                  />
                                </Link>
                              )}
                            </div>
                            {isMobile && data?.acf?.team_image && (
                              <div
                                className={`${aboutus.team_mob_image} team_mob_image`}
                              >
                                <DynamicImage
                                  src={data?.acf?.team_image?.url}
                                  alt="Dynamic Image"
                                />
                              </div>
                            )}
                            {data?.acf?.team_content && (
                              <p data-aos="fade-up" data-aos-duration="1000">
                                {parse(data?.acf?.team_content)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
              </Swiper>

              {/* Thumbnails Swiper */}
              <div
                className={`${aboutus.about_our_team_content_profile_sec} team_tumb`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <Swiper
                  onSwiper={setThumbsSwiperAr}
                  spaceBetween={30}
                  slidesPerView={8.2} // Adjust the number of thumbnails
                  freeMode={true}
                  watchSlidesProgress={true}
                  modules={[FreeMode, Navigation, Thumbs]}
                  className="mySwiper thumb_swiper center_tumb"
                  dir={"rtl"}
                  breakpoints={{
                    0: {
                      slidesPerView: 2,
                      spaceBetween: 20,
                    },
                    768: {
                      slidesPerView: 3,
                      spaceBetween: 20,
                    },
                    1024: {
                      slidesPerView: 4,
                      spaceBetween: 20,
                    },
                    1200: {
                      slidesPerView: 6.2,
                      spaceBetween: 30,
                    },
                    1600: {
                      slidesPerView: 8.2,
                      spaceBetween: 30,
                    },
                  }}
                >
                  {props?.TeamData &&
                    props?.TeamData.map((data, tindex) => (
                      <SwiperSlide>
                        <div
                          className={
                            aboutus.about_our_team_content_profile_card
                          }
                          key={tindex}
                        >
                          {data?.acf?.team_image && (
                            <div
                              className={`${aboutus.about_our_team_content_profile_photo_sec} about_our_team_content_profile_photo_sec`}
                            >
                              <DynamicImage
                                src={data?.acf?.team_image?.url}
                                alt="Dynamic Image"
                              />
                            </div>
                          )}
                          <div
                            className={
                              aboutus.about_our_team_content_profile_data_sec
                            }
                          >
                            {data?.title && (
                              <h4>{data?.title && data?.title?.rendered}</h4>
                            )}
                            {data?.acf?.team_position && (
                              <span>{data?.acf?.team_position}</span>
                            )}
                          </div>
                        </div>
                      </SwiperSlide>
                    ))}
                </Swiper>
              </div>
            </section>
          )}
        </>
      </div>

      {/* ----------- end of Our Team section BoardMember ----------- */}

      {/* -----------Our Executive Team section ----------- */}
      <div className={comon.team_english}>
        <>
          {props && props.ExecutiveData && props.ExecutiveData.length > 0 && (
            <section
              className={`${aboutus.about_our_team_section} ${comon.about_our_team_section} ${comon.pt_100}  ${comon.pb_75}`}
            >
              {props?.aboutData?.acf?.our_executive_title && (
                <div div className={comon.wrap}>
                  <h3 data-aos="fade-up" data-aos-duration="1000">
                    {props?.aboutData?.acf?.our_executive_title &&
                      parse(props?.aboutData?.acf?.our_executive_title)}
                  </h3>
                </div>
              )}
              {/* ===========Btn Section============== */}
              <div className={`${aboutus.about_our_team_swiper_btn_sec} `}>
                <div
                  className={` ${comon.wrap} ${aboutus.about_our_team_swiper_container}  `}
                >
                  <span
                    className={`${comon.custom_next} ${comon.custom_btn} ${comon.custom_btn_border} custom_prev_2`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/prev_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                  <span
                    className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} custom_next_2`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/next_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                </div>
              </div>

              {/* =========Detailed Swiper=========== */}

              {/* Main Swiper */}
              <Swiper
                loop={true}
                spaceBetween={10}
                navigation={{
                  nextEl: ".custom_next_2",
                  prevEl: ".custom_prev_2",
                }}
                thumbs={{ swiper: leaderThumbsSwiper }}
                modules={[FreeMode, Navigation, Thumbs]}
                className="mySwiper2"
                onSwiper={(swiper) => (mainSwiperLeaderRef.current = swiper)} // Bind to ref
                dir={"ltr"}
              >
                {props?.ExecutiveData &&
                  props?.ExecutiveData.map((data, tindex) => (
                    <SwiperSlide key={tindex}>
                      <div
                        className={`${aboutus.about_our_team_content_sec} ${comon.about_our_team_content_sec}`}
                        style={{
                          backgroundImage: `url(${data?.acf?.team_image?.url})`,
                        }}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <div
                          className={`${aboutus.about_our_team_container} ${comon.wrap} `}
                        >
                          <div
                            className={aboutus.about_our_team_left_content_sec}
                            data-aos="fade-up"
                            data-aos-duration="1000"
                          >
                            {/* <span data-aos="fade-up" data-aos-duration="1000">
                              {data.categoryNames.join(", ")}
                            </span> */}
                            <div
                              className={aboutus.about_our_team_left_title_sec}
                            >
                              <div
                                className={`${aboutus.about_our_team_left_title} ${comon.about_our_team_left_title}`}
                              >
                                {data?.title && (
                                  <h4
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.title?.rendered}
                                  </h4>
                                )}

                                {data?.acf?.team_position && (
                                  <h6
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.acf?.team_position}
                                  </h6>
                                )}
                              </div>
                              {data?.acf?.team_linkedin && (
                                <Link
                                  href={data?.acf?.team_linkedin?.url}
                                  target={data?.acf?.team_linkedin?.target}
                                  className={
                                    aboutus.about_our_team_left_linkedin
                                  }
                                >
                                  <DynamicImage
                                    src="/images/linked_image.png"
                                    alt="Dynamic Image"
                                  />
                                </Link>
                              )}
                            </div>
                            {isMobile && data?.acf?.team_image && (
                              <div
                                className={`${aboutus.team_mob_image} team_mob_image`}
                              >
                                <DynamicImage
                                  src={data?.acf?.team_image?.url}
                                  alt="Dynamic Image"
                                />
                              </div>
                            )}
                            {data?.acf?.team_content && (
                              <p data-aos="fade-up" data-aos-duration="1000">
                                {parse(data?.acf?.team_content)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
              </Swiper>

              {/* Thumbnails Swiper */}
              <div
                className={`${aboutus.about_our_team_content_profile_sec} team_tumb`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <Swiper
                  onSwiper={setLeaderThumbsSwiper}
                  spaceBetween={30}
                  slidesPerView={8.2} // Adjust the number of thumbnails
                  freeMode={true}
                  watchSlidesProgress={true}
                  modules={[FreeMode, Navigation, Thumbs]}
                  className="mySwiper thumb_swiper"
                  dir={"ltr"}
                  breakpoints={{
                    0: {
                      slidesPerView: 2,
                      spaceBetween: 20,
                    },
                    768: {
                      slidesPerView: 3,
                      spaceBetween: 20,
                    },
                    1024: {
                      slidesPerView: 4,
                      spaceBetween: 20,
                    },
                    1200: {
                      slidesPerView: 6.2,
                      spaceBetween: 30,
                    },
                    1600: {
                      slidesPerView: 8.2,
                      spaceBetween: 30,
                    },
                  }}
                >
                  {props?.ExecutiveData &&
                    props?.ExecutiveData.map((data, tindex) => (
                      <SwiperSlide>
                        <div
                          className={
                            aboutus.about_our_team_content_profile_card
                          }
                          key={tindex}
                        >
                          {data?.acf?.team_image && (
                            <div
                              className={`${aboutus.about_our_team_content_profile_photo_sec} about_our_team_content_profile_photo_sec`}
                            >
                              <DynamicImage
                                src={data?.acf?.team_image?.url}
                                alt="Dynamic Image"
                              />
                            </div>
                          )}
                          <div
                            className={
                              aboutus.about_our_team_content_profile_data_sec
                            }
                          >
                            {data?.title && (
                              <h4>{data?.title && data?.title?.rendered}</h4>
                            )}
                            {data?.acf?.team_position && (
                              <span>{data?.acf?.team_position}</span>
                            )}
                          </div>
                        </div>
                      </SwiperSlide>
                    ))}
                </Swiper>
              </div>
            </section>
          )}
        </>
      </div>

      {/* -----------Our Team section ar----------- */}

      <div className={comon.team_arabic}>
        <>
          {props && props.ExecutiveData && props.ExecutiveData.length > 0 && (
            <section
              className={`${aboutus.about_our_team_section} ${comon.about_our_team_section} ${comon.pt_100}  ${comon.pb_75}`}
            >
              {props?.aboutData?.acf?.our_executive_title && (
                <div div className={comon.wrap}>
                  <h3 data-aos="fade-up" data-aos-duration="1000">
                    {props?.aboutData?.acf?.our_executive_title &&
                      parse(props?.aboutData?.acf?.our_executive_title)}
                  </h3>
                </div>
              )}
              {/* ===========Btn Section============== */}
              <div className={`${aboutus.about_our_team_swiper_btn_sec} `}>
                <div
                  className={` ${comon.wrap} ${aboutus.about_our_team_swiper_container}  `}
                >
                  <span
                    className={`${comon.custom_next} ${comon.custom_btn} ${comon.custom_btn_border} custom_prev_2_ar`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/prev_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                  <span
                    className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} custom_next_2_ar`}
                  >
                    <Image
                      className={`${comon.img_mx_fluid}`}
                      src={`/images/next_ic.svg`}
                      alt="call"
                      width={11}
                      height={11}
                    />
                  </span>
                </div>
              </div>

              {/* =========Detailed Swiper=========== */}

              {/* Main Swiper */}
              <Swiper
                loop={true}
                spaceBetween={10}
                navigation={{
                  nextEl: ".custom_next_2_ar",
                  prevEl: ".custom_prev_2_ar",
                }}
                thumbs={{ swiper: leaderThumbsSwiperAr }}
                modules={[FreeMode, Navigation, Thumbs]}
                className="mySwiper2_ar"
                onSwiper={(swiper) => (mainSwiperLeaderRefAr.current = swiper)} // Bind to ref
                dir={"rtl"}
              >
                {props?.ExecutiveData &&
                  props?.ExecutiveData.map((data, tindex) => (
                    <SwiperSlide key={tindex}>
                      <div
                        className={`${aboutus.about_our_team_content_sec} ${comon.about_our_team_content_sec}`}
                        style={{
                          backgroundImage: `url(${data?.acf?.team_image?.url})`,
                        }}
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        <div
                          className={`${aboutus.about_our_team_container} ${comon.wrap} `}
                        >
                          <div
                            className={aboutus.about_our_team_left_content_sec}
                            data-aos="fade-up"
                            data-aos-duration="1000"
                          >
                            {/* <span data-aos="fade-up" data-aos-duration="1000">
                              {data.categoryNames.join(", ")}
                            </span> */}
                            <div
                              className={aboutus.about_our_team_left_title_sec}
                            >
                              <div
                                className={`${aboutus.about_our_team_left_title} ${comon.about_our_team_left_title}`}
                              >
                                {data?.title && (
                                  <h4
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.title?.rendered}
                                  </h4>
                                )}

                                {data?.acf?.team_position && (
                                  <h6
                                    data-aos="fade-up"
                                    data-aos-duration="1000"
                                  >
                                    {data?.acf?.team_position}
                                  </h6>
                                )}
                              </div>
                              {data?.acf?.team_linkedin && (
                                <Link
                                  href={data?.acf?.team_linkedin?.url}
                                  target={data?.acf?.team_linkedin?.target}
                                  className={
                                    aboutus.about_our_team_left_linkedin
                                  }
                                >
                                  <DynamicImage
                                    src="/images/linked_image.png"
                                    alt="Dynamic Image"
                                  />
                                </Link>
                              )}
                            </div>
                            {isMobile && data?.acf?.team_image && (
                              <div
                                className={`${aboutus.team_mob_image} team_mob_image`}
                              >
                                <DynamicImage
                                  src={data?.acf?.team_image?.url}
                                  alt="Dynamic Image"
                                />
                              </div>
                            )}
                            {data?.acf?.team_content && (
                              <p data-aos="fade-up" data-aos-duration="1000">
                                {parse(data?.acf?.team_content)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
              </Swiper>

              {/* Thumbnails Swiper */}
              <div
                className={`${aboutus.about_our_team_content_profile_sec} team_tumb`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <Swiper
                  onSwiper={setLeaderThumbsSwiperAr}
                  spaceBetween={30}
                  slidesPerView={8.2} // Adjust the number of thumbnails
                  freeMode={true}
                  watchSlidesProgress={true}
                  modules={[FreeMode, Navigation, Thumbs]}
                  className="mySwiper thumb_swiper"
                  dir={"rtl"}
                  breakpoints={{
                    0: {
                      slidesPerView: 2,
                      spaceBetween: 20,
                    },
                    768: {
                      slidesPerView: 3,
                      spaceBetween: 20,
                    },
                    1024: {
                      slidesPerView: 4,
                      spaceBetween: 20,
                    },
                    1200: {
                      slidesPerView: 6.2,
                      spaceBetween: 30,
                    },
                    1600: {
                      slidesPerView: 8.2,
                      spaceBetween: 30,
                    },
                  }}
                >
                  {props?.ExecutiveData &&
                    props?.ExecutiveData.map((data, tindex) => (
                      <SwiperSlide>
                        <div
                          className={
                            aboutus.about_our_team_content_profile_card
                          }
                          key={tindex}
                        >
                          {data?.acf?.team_image && (
                            <div
                              className={`${aboutus.about_our_team_content_profile_photo_sec} about_our_team_content_profile_photo_sec`}
                            >
                              <DynamicImage
                                src={data?.acf?.team_image?.url}
                                alt="Dynamic Image"
                              />
                            </div>
                          )}
                          <div
                            className={
                              aboutus.about_our_team_content_profile_data_sec
                            }
                          >
                            {data?.title && (
                              <h4>{data?.title && data?.title?.rendered}</h4>
                            )}
                            {data?.acf?.team_position && (
                              <span>{data?.acf?.team_position}</span>
                            )}
                          </div>
                        </div>
                      </SwiperSlide>
                    ))}
                </Swiper>
              </div>
            </section>
          )}
        </>
      </div>

      {/* ----------- end of Our Executive Team section ----------- */}

      {/* -----------Values section----------- */}
      <div
        style={{
          background: "url(../images/dgital_prdct_bg.png) top left no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "left center",
        }}
        className={aboutus.sec_mob_padding_top}
      >
        {props &&
        props?.aboutData &&
        props?.aboutData?.acf &&
        (props?.aboutData?.acf?.our_clients_title ||
          props?.aboutData?.acf?.our_clients) ? (
          <section>
            <Clients
              our_clients_title={props?.aboutData?.acf?.our_clients_title}
              our_clients={props?.aboutData?.acf?.our_clients}
              our_button={props?.aboutData?.acf?.our_clients_button}
            />
            <div className={`${comon.wrap}`}>
             
            </div>
          </section>
        ) : null}
        {props &&
          props?.aboutData &&
          props?.aboutData?.acf &&
          props?.aboutData?.acf?.values_title &&
          props?.aboutData?.acf?.vision_mission && (
            <section
              // style={{ background: " #1e1e2c" }}
              className={`${aboutus.about_container}  ${comon.pt_100}  ${comon.pb_100}`}
            >
              <div className={`${aboutus.about_container} ${comon.wrap} `}>
                {props?.aboutData?.acf?.values_title && (
                  <div
                    className={`${comon.w_100} ${comon.mb_20} ${comon.text_center} `}
                  >
                    <h2
                      data-aos="fade-up"
                      data-aos-duration="1000"
                      className={`${comon.h2} ${comon.mb_15} ${comon.text_white}`}
                    >
                      {props?.aboutData?.acf?.values_title &&
                        parse(props?.aboutData?.acf?.values_title)}
                    </h2>
                  </div>
                )}
                <ul className={`${aboutus.value_ul} ${comon.pt_20} `}>
                  {props?.aboutData?.acf?.values &&
                    props?.aboutData?.acf?.values.map((value, valIndex) => (
                      <li key={valIndex}>
                        <div
                          className={`${aboutus.value_head} ${comon.value_head}`}
                          data-aos="fade-up"
                          data-aos-duration="1000"
                        >
                          {value?.val_title && (
                            <h3>
                              {value?.val_title && parse(value?.val_title)}
                              <br />
                              {value?.val_sub_text &&
                                parse(value?.val_sub_text)}
                            </h3>
                          )}
                          {/* {value?.val_sub_text && (
                          <h4>
                            {value?.val_sub_text && parse(value?.val_sub_text)}
                          </h4>
                        )} */}
                        </div>
                        {value?.val_content && (
                          <div
                            className={`${aboutus.value_content} value_content`}
                          >
                            {value?.val_content && parse(value?.val_content)}
                          </div>
                        )}
                      </li>
                    ))}
                </ul>
              </div>
            </section>
          )}
          </div>
        {/* -----------Our History section----------- */}
        {props &&
          props?.aboutData &&
          props?.aboutData?.acf &&
          props?.aboutData?.acf?.our_history_title &&
          props?.aboutData?.acf?.our_history && (
            <section
              className={`${aboutus.about_container} ${comon.p_relative}  ${comon.pt_100}  ${comon.pb_30} ${aboutus.time_line}`}
              style={{backgroundColor:"#2a2656"}}
            >
              <div className={`${aboutus.about_container} ${comon.wrap}  `}>
                <div
                  className={`${aboutus.about_recognition_head_sec} ${comon.title_block_row} `}
                >
                  {props?.aboutData?.acf?.our_history_title && (
                    <h2
                      data-aos="fade-up"
                      data-aos-duration="1000"
                      className={`${comon.h2} ${comon.mb_15} ${comon.text_white}`}
                    >
                      {props?.aboutData?.acf?.our_history_title &&
                        parse(props?.aboutData?.acf?.our_history_title)}
                    </h2>
                  )}
                  <div
                    className={`${aboutus.about_recognition_swiper_btn_sec} ${comon.swipper_nav_block} `}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    <span
                      className={`${comon.custom_next} ${comon.custom_btn} ${comon.white_border_btn} custom_prev_2`}
                    >
                      <Image
                        className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                        src={`/images/prev_ic.svg`}
                        alt="call"
                        width={11}
                        height={11}
                      />
                    </span>
                    <span
                      className={`${comon.custom_prev} ${comon.custom_btn} ${comon.white_border_btn}  custom_next_2 `}
                    >
                      <Image
                        className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                        src={`/images/next_ic.svg`}
                        alt="call"
                        width={11}
                        height={11}
                      />
                    </span>
                  </div>
                </div>
              </div>
              <div
                className={`${comon.w_100} ${comon.mb_50}  ${comon.mt_100} ${comon.history_slider} ${aboutus.history_slider}  `}
              >
                <Swiper
                  slidesPerView={1}
                  spaceBetween={0}
                  modules={[Navigation]}
                  speed={1000}
                  navigation={{
                    nextEl: ".custom_next_2", // Connects to your custom next button
                    prevEl: ".custom_prev_2", // Connects to your custom previous button
                  }}
                  dir={language === "ar" ? "rtl" : "ltr"}
                  key={language}
                  breakpoints={{
                    640: {
                      slidesPerView: 1,
                      spaceBetween: 0,
                    },
                    768: {
                      slidesPerView: 2,
                      spaceBetween: 0,
                    },
                    1024: {
                      slidesPerView: 3,
                      spaceBetween: 0,
                    },
                  }}
                  // modules={[Pagination]}
                  className="mySwiper"
                >
                  {props?.aboutData?.acf?.our_history &&
                    props?.aboutData?.acf?.our_history.map(
                      (history, hIndex) => (
                        <SwiperSlide key={hIndex}>
                          <div
                            className={`${aboutus.time_line_block} ${comon.time_line_block}`}
                            data-aos="fade-up"
                            data-aos-duration="1000"
                          >
                            {history.history_image && (
                              <div
                                className={`${aboutus.time_line_icon_block} ${comon.mt_20} `}
                              >
                                <Image
                                  src={history.history_image?.url}
                                  alt="call"
                                  width={72}
                                  height={72}
                                />
                              </div>
                            )}
                            {history.history_year && (
                              <h3>
                                {history.history_year &&
                                  parse(history.history_year)}
                              </h3>
                            )}

                            {history.history_title && (
                              <h4>
                                {history.history_title &&
                                  parse(history.history_title)}
                              </h4>
                            )}
                            {history.history_content &&
                              parse(history.history_content)}
                          </div>
                        </SwiperSlide>
                      )
                    )}
                </Swiper>
              </div>
            </section>
          )}

        {/* -----------recognition section----------- */}

        {props && props.AwardsArray && props.AwardsArray.length > 0 && (
          <section section className={`${aboutus.about_recognition_section}`}
          style={{
            background: "url(../images/history_new_bg.png) top left no-repeat",
            backgroundSize: "cover",
            backgroundPosition: "left center",
          }}>
            <div
              className={`${aboutus.about_recognition_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100} swipper_overflow`}
            >
              <div className={`${aboutus.about_recognition_head_sec}  `}>
                {props?.aboutData?.acf?.recognition_and_awards_title && (
                  <h4 data-aos="fade-up" data-aos-duration="1000">
                    {props?.aboutData?.acf?.recognition_and_awards_title &&
                      parse(
                        props?.aboutData?.acf?.recognition_and_awards_title
                      )}
                  </h4>
                )}

                <ul className={`${comon.slider_but_ul}`}>
                  <li>
                    <span
                      className={`${comon.custom_next} ${comon.slider_buttion_white} ${comon.position_unset} ${comon.custom_btn} ${comon.custom_btn_border} ${aboutus.prev_awards_btn} custom_prev_awards`}
                    >
                      <Image
                        className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                        src={`/images/white_arrow_l.svg`}
                        alt="call"
                        width={11}
                        height={11}
                      />
                    </span>
                  </li>
                  <li>
                    <span
                      className={`${comon.position_unset} ${comon.slider_buttion_white} ${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} ${aboutus.next_awards_btn} custom_next_awards`}
                    >
                      <Image
                        className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                        src={`/images/white_arrow_r.svg`}
                        alt="call"
                        width={11}
                        height={11}
                      />
                    </span>
                  </li>
                </ul>
              </div>
              <div
                className={`${aboutus.about_recognition_swiper_sec}  ${comon.mt_50}`}
              >
                <Swiper
                  data-aos="fade-up"
                  data-aos-duration="1000"
                  className={` mySwiper awards_swipper`}
                  slidesPerView={"auto"}
                  spaceBetween={10}
                  pagination={{
                    clickable: true,
                  }}
                  breakpoints={{
                    640: {
                      // slidesPerView: 2,
                      spaceBetween: 20,
                    },
                    768: {
                      // slidesPerView: 3,
                      spaceBetween: 20,
                    },
                    1100: {
                      // slidesPerView: 4,
                      spaceBetween: 20,
                    },
                    1280: {
                      // slidesPerView: 4,

                      spaceBetween: 30,
                    },
                  }}
                  modules={[Navigation, Autoplay]}
                  navigation={{
                    nextEl: ".custom_next_awards", // Connects to your custom next button
                    prevEl: ".custom_prev_awards", // Connects to your custom previous button
                  }}
                  autoplay={{
                    delay: 4000,
                    disableOnInteraction: false,
                  }}
                  loop={true}
                  speed={1000}
                  dir={language === "ar" ? "rtl" : "ltr"}
                  key={language}
                >
                  {props.AwardsArray &&
                    props.AwardsArray.map((awards, aIndex) => (
                      <SwiperSlide
                        className={`${comon.awards_slider}`}
                        key={aIndex}
                      >
                        <div
                          className={`${aboutus.about_recognition_swiper_card}  `}
                        >
                          {awards?.acf?.awards_image && (
                            <div
                              className={`${aboutus.about_recognition_swiper_card_logo} `}
                            >
                              <Image
                                src={awards?.acf?.awards_image?.url}
                                alt=""
                                width="248"
                                height="154"
                              />
                            </div>
                          )}

                          <div
                            className={`${aboutus.about_recognition_swiper_card_title} ${comon.about_recognition_swiper_card_title} `}
                          >
                            {awards?.acf?.awards_year && (
                              <h5>
                                {awards?.acf?.awards_year &&
                                  parse(awards?.acf?.awards_year)}
                              </h5>
                            )}
                          </div>
                          {awards?.title && (
                            <p className={`${comon.pt_10} `}>
                              {awards?.title && parse(awards?.title?.rendered)}
                            </p>
                          )}
                        </div>
                      </SwiperSlide>
                    ))}
                </Swiper>
              </div>

              <div
                className={`${comon.w_100} ${comon.mt_70} ${comon.view_all_bottom}`}
              >
                {props?.aboutData?.acf?.view_more_button && (
                  <div
                    className={`${aboutus.about_recognition_swiper_btn_sec}  view_btn_style`}
                  >
                    <Buttion
                      aosType="fade-up"
                      aosDuration={1500}
                      text={props?.aboutData?.acf?.view_more_button?.title} // Button text
                      href={props?.aboutData?.acf?.view_more_button?.url} // Navigation target for the button
                      target={props?.aboutData?.acf?.view_more_button?.target}
                      moduleClass={buttion.blue} // Custom button styling
                      imageSrc="/images/buttion_arrow.svg" // Image for button
                    />
                  </div>
                )}
              </div>
            </div>
          </section>
        )}
      
      <InquireNow formtitle={props?.aboutData?.title?.rendered} />
    </div>
  );
};

export default Index;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();

  const query = "";

  const aboutData = await getAbout(locale);
  // const IRMenuData = await getIrMenuLinks(locale);
  const TeamData = await getTeamPosts(locale, query);
  let BoardPosts = [];
  if (
    aboutData &&
    aboutData.acf &&
    Array.isArray(aboutData?.acf?.board_members)
  ) {
    BoardPosts = aboutData?.acf?.board_members;
  }

  // Format boardDirectors for use in the component
  let BoardArray = [];
  if (BoardPosts.length > 0) {
    BoardArray = BoardPosts.map((id) =>
      TeamData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  let ExecutivePosts = [];
  if (
    aboutData &&
    aboutData.acf &&
    Array.isArray(aboutData?.acf?.our_executive)
  ) {
    ExecutivePosts = aboutData?.acf?.our_executive;
  }

  // Format boardDirectors for use in the component
  let ExecutiveArray = [];
  if (ExecutivePosts.length > 0) {
    ExecutiveArray = ExecutivePosts.map((id) =>
      TeamData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }
  // const categories = await getTeamCategories(locale);
  // // Create a mapping of category ID to category name
  // const categoryMap = Object.fromEntries(
  //   categories.map((category) => [category.id, category.name])
  // );

  // // Map category IDs in TeamData to their corresponding category names
  // const mappedTeamData = TeamData.map((team) => {
  //   const categoryNames = team.team_category
  //     .map((categoryId) => categoryMap[categoryId]) // Get category names
  //     .filter(Boolean); // Remove any undefined values

  //   return {
  //     ...team,
  //     categoryNames, // Add the category names to each team entry
  //   };
  // });

  const AwardsData = await getAwardsPosts(locale, query);
  let awardsPosts = [];
  if (
    aboutData &&
    aboutData.acf &&
    Array.isArray(aboutData.acf.recognition_awards)
  ) {
    awardsPosts = aboutData.acf.recognition_awards;
  }

  // Format boardDirectors for use in the component
  let AwardsArray = [];
  if (awardsPosts.length > 0) {
    AwardsArray = awardsPosts
      .map((id) => AwardsData?.find((post) => post.id === id))
      .filter(Boolean); // To ensure undefined values (if any) are removed
  }

  //  console.log('testingdata', aboutData)

  return {
    props: {
      aboutData: aboutData || null,
      // IRMenuData: IRMenuData || null,
      TeamData: BoardArray || null,
      ExecutiveData: ExecutiveArray || null,
      AwardsArray: AwardsArray || [],
    },
    revalidate: 10,
  };
};
