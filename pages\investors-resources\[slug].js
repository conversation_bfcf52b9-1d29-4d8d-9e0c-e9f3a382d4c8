import React, { useEffect, useState } from "react";
import style from "@/styles/InvestorsResources.module.scss";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import InvestorsResourcesCard from "@/component/InvestorsResourcesCard";
import TabSection from "@/component/Tab-section";

import Yoast from "@/component/yoast";
import {
    getIRslugPage,    
    getIrMenuLinks,
    getInvestorsResourcesPosts,
    getResourcesCategories,
    getResourcesCategoriesList
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";


const InvestorResources = (props) => {

  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
     useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
     }, []);
    
    
    const yoastData = props?.InvestorsResData?.yoast_head_json;

  if (!props.InvestorsResData) {
    return null;
  }
  

  return (
    <div>
      {/* -----------Banner section----------- */}

            {yoastData && <Yoast meta={yoastData} />}

            {props &&
                props?.InvestorsResData &&
                props?.InvestorsResData?.acf &&
                props?.InvestorsResData?.acf?.banner_title &&
                (props?.InvestorsResData?.acf?.mob_banner_image ||
                props?.InvestorsResData?.acf?.banner_image ||
                props?.InvestorsResData?.acf?.breadcrumbs ||
                props?.InvestorsResData?.acf?.banner_viedo ||                
                props?.InvestorsResData?.acf?.banner_type) ? (
                <InnerBanner
                pagename={props?.InvestorsResData?.acf?.banner_title}
                breadcrumb1={props?.InvestorsResData?.acf?.active_breadcrumbs ==='yes' ? props?.InvestorsResData?.acf?.breadcrumbs : '' } 
                background={`${isMobile ? props?.InvestorsResData?.acf?.mob_banner_image?.url : props?.InvestorsResData?.acf?.banner_image?.url}`}          
                videoSrc={props?.InvestorsResData?.acf?.banner_viedo?.url}
                banner_type={props?.InvestorsResData?.acf?.banner_type}              

                />
            ) : null
      }
      
     


      {/* =========Tab Section====== */}
     {props &&
        props?.IRMenuData &&
                props?.IRMenuData?.investors_relations_menu &&
                <TabSection tabs={props?.IRMenuData?.investors_relations_menu}/>
            }
      {/* -----------Company Announcement section----------- */}

      {props?.AdvisoryTaxData &&
        <section
          className={`${style.investor_resources_company_announcement_section}`}
        >
          <div
            className={`${style.investor_resources_company_announcement_container} ${comon.wrap} ${comon.pt_100}  `}
          >
            <h3>{props?.AdvisoryTaxData && parse(props?.AdvisoryTaxData?.name)}</h3>
          </div>
          <div className={style.inr_graphics}>
            <Image src='/images/ir_graphic.png' width={292} height={511} crossOrigin="anonymous"/>
          </div>
        </section>
      }

      {/* =======Annual Card Blocks======= */}
      
      <section
            className={`${style.investor_resources_card_block_section}  ${comon.pt_80}  `}            
      >
        <div
          className={`${style.investor_resources_card_block_section_container} ${comon.wrap}  `}
           >
           
          <ul
            className={`${style.investor_resources_company_announcement_card_sec}  ${comon.pt_30}  ${comon.pb_100}`}
             >
              
                 <InvestorsResourcesCard cardData={props?.IR_Resources_data} />
             
               
               </ul>
                 
        </div>
      </section>
      
    </div>
  );
};

export default InvestorResources;


export async function getStaticPaths(locale) {
  const ProductlistData = await getResourcesCategoriesList(locale);

  const paths = ProductlistData.map((post) => ({
    params: { slug: post.slug },
    //locale, // Ensure locale is part of the paths (for multi-language support)
  }));
  // console.log('fdshfsd', locale);
  return {
    paths: paths,
    fallback: "blocking", // false or 'blocking'
  };
}

export async function getStaticProps({ params, locale }) {
  const slug = params.slug;
//   console.log('fdshfsd', slug);
//  console.log('getStaticProps', locale);
  const InvestorsResData = await getIRslugPage(locale);
  const IRMenuData = await getIrMenuLinks(locale);

  const ProductSlugData = await getResourcesCategories(slug, locale);

  const taxonomyId = ProductSlugData[0]?.id; // Assuming `id` is directly accessible

  if (!taxonomyId) {
    return {
      notFound: true, // Optionally return a 404 if taxonomy ID is not found
    };
  }

  const query = "";
  const AdvisoryPostData = await getInvestorsResourcesPosts(locale, query);

  // Filter posts based on the taxonomy ID and advisory category
  const AdvisoryPostArray = AdvisoryPostData.filter(
    (post) =>
      Array.isArray(post?.resources_category) &&
      post?.resources_category.includes(taxonomyId)
  );

  //  console.log('testingdata', ProductSlugData[0])  

  return {
    props: {
      InvestorsResData: InvestorsResData || null,
      IRMenuData: IRMenuData || null,  
      AdvisoryTaxData: ProductSlugData[0] || null,      
      IR_Resources_data: AdvisoryPostArray || [],
    },
    revalidate: 10,
  };
}
