@import "variable", "base";

.buttion {
  height: 60px;
  padding-left: 30px;
  padding-right: 30px;
  border-radius: 35px;
  display: inline-block;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  display: inline-flex;
  font-family: var(--helveticaNeueMedium);
  align-items: center;
  line-height: 20px;
  @media #{$media-1600} { 
    height: 52px;
  padding-left: 25px;
  padding-right: 25px;
  }

  img {
    margin-left: 5px;
    margin-right: 5px;
    @media #{$media-700} { 
      width: 12px;
    }
  }

  span {
    display: inline-block;
    margin-left: 5px;
    margin-right: 5px;
  }

  &.blue {
    background: $blue;
    color: $white;

    &:hover {
      background: #5e45ff;
      color: $white;
    }
  }

  &.rounded {
    // background-color: rgb(255, 255, 255);
    // width:38px !important;
    // height:38px !important;
    // border-radius: 100% !important;
    // display: inline-flex !important;
    // align-items: center !important;
    // justify-content: center !important;
    // padding: 0 !important;

    // span {
    //   display: none;
    // }
    height: auto;
    padding: 12px 20px;
    span{
      color: #ffffff;
      font-family: var(--helveticaneueltarabicLight);
      font-size: 16px;
    }


    img {
      filter: invert(1);
      transition: all 0.3s ease;

    }

    &:hover {
      background-color: #5e45ff;
      border: 1px solid #ffffff !important;

      img {
        filter: unset;
      }
    }
  }

  &.white {
    background: $white;
    color: $black;
    height: 50px;
    padding-left: 20px;
    padding-right: 20px;
    font-size: 1rem;
    @media #{$media-1600} {
      font-size: 1.2rem;
    }

    &:hover {
      // background: $gray;
      // color: $black;
      img{
        filter: invert(1);
      }
    }
  }

  &.strock {
    border: solid 1px $white;
    color: $white;
    background: transparent;

    &:hover {
      background: #5e45ff;
      color: $white;
    }
  }


  @media #{$media-820} {
    height: 50px;
    font-size: 14px;
    padding-left: 20px;
    padding-right:20px;

   }


  @media #{$media-700} { 
    height: 40px;
    padding-left: 15px;
    padding-right: 15px;
    font-size: 14px;
  }







}