@import "variable", "base";

.share_info_section {
    h3 {
        color: #2A2656;
        font-size: 2.5rem;
        margin-bottom: 50px;

        @media #{$media-1200} {
            margin-bottom: 40px;
        }

        @media #{$media-767} {
            font-size: 3rem;
            margin-bottom: 30px;
        }
    }

    .share_info_left {

        // padding-inline-end: 20%;
        img {
            max-width: 100%;
            display: block;
            height: auto;
            margin-top: 5px;
        }

        @media #{$media-1024} {
            margin-bottom: 30px;
        }
    }

    .share_info_right {

        h5 {
            color: #413887;
            font-size: 1.563rem;
            font-family: var(--helveticaNeue);
            margin-bottom: 30px;

            @media #{$media-767} {
                font-size: 3rem;
            }
        }

        overflow: auto;

        ::-webkit-scrollbar {
            display: none;
        }
    }

    .share_info_content_sec {
        display: grid;
        grid-template-columns: repeat(2, 1fr);

        @media #{$media-1024} {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .table_sec {
        border: 1px solid #DFDFDF;
        border-radius: 10px;
        padding-inline-start: 20px;
        width: 100%;
        overflow: auto;

        ::-webkit-scrollbar {
            display: none;
        }

    }

    table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 10px;

        td {
            padding: 15px;
            border-bottom: 1px solid #E2E8F0;
            color: #2D3748;
            font-size: 0.938rem;
            font-family: var(--helveticaNeue);
            width: 25%;

            @media #{$media-820} {
                font-size: 1.5rem;
            }

            @media #{$media-767} {
                font-size: 14px;
            }

            &:first-child {
                padding-left: 0;
            }

            &:nth-child(2n+2) {
                background-color: #faf9ff;
                padding-left: 30px;
                color: #5E45FF;
                font-size: 1.125rem;

                @media #{$media-820} {
                    font-size: 1.5rem;
                }
            }

        }

        tr {
            &:last-child {
                td {
                    border-bottom: none;
                    border-bottom-right-radius: 10px;
                }
            }

            &:first-child {
                td {
                    border-top-right-radius: 10px;
                }
            }
        }

    }
}

.inner_banner_share_chart_wrap {

    .inner_banner_share_chart {
        padding: 20px 0;
        padding-inline-end: 50px;
        background-color: #ffffff;
        display: inline-flex;
        gap: 20px;
        border-bottom: 1px solid #dddddd;

        .sharechart_img {
            display: block;
            height: auto;
            width: 100%;
            object-fit: cover;
        }

        @media #{$media-1600} {
            padding: 15px 0;
            padding-inline-end: 40px;
        }

        @media #{$media-767} {
            margin-inline-start: auto;
            bottom: 10%;
            display: flex;
        }

        .diasmond {
            img {
                @media #{$media-1600} {
                    width: 20px;
                }

                @media #{$media-500} {
                    width: 15px;
                }
            }

        }

        .stock_info {


            h5 {
                font-family: var(--helveticaneueltarabicLight);
                color: #5E45FF;
                font-size: 1.9rem;
                line-height: 2.5rem;
                font-weight: 600;
                display: block;

                @media #{$media-767} {
                    font-size: 2.5rem;
                    line-height: 3rem;
                }
            }

            >p {
                color: #5E45FF;
                font-size: 15px;
                line-height: 100%;
                letter-spacing: 0.06em;
                font-weight: 600;
            }

            .stockData {
                margin-top: 10px;
                font-size: 1.9rem;
                line-height: 2.6rem;
                font-weight: 400;
                display: block;
                display: flex;
                align-items: center;
                gap: 5px;
                @media #{$media-767} {
                    font-size: 2.5rem;
                    line-height: 3rem;
                }

                // &.up {
                //     color: #008000;
                // }

                // &.down {
                //     color: #ff0001;
                // }

                >span:first-child {
                    color: #000000 !important;
                }
            }
        }
    }
}

.share_graph_wrapper {
    height: 300px;
    margin-top: 50px;
    margin-inline-end: 15%;

    @media #{$media-1600} {
        height: 250px;
    }

    @media #{$media-1024} {
        margin-inline-end: 0;
    }
}