@import "variable", "base";

.megamenu_main_body {
  font-family: var(--helveticaNeue);   
  // height: 90vh;
  height: 650px;
  width: 100%;
  position: absolute;
  top: 0;
  // top: 15vh;
  left: 0;
  z-index: 15;
  background: rgba(94, 69, 255, 0.9);
  ;
  backdrop-filter: blur(20px);
  position: relative;
  &::after{
    content: "";
    background-image: url(./../public/images/menu_bg.png);
    width: 427px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 1;
  }
  .megamenu_advisary_sec {
    display: inline-flex;
    align-items: center;
    gap: 15px;
    position: relative;
    z-index: 2;

    h3 {
      font-family:inherit;   
      font-size: 35px;
      color: white;
      font-weight: 300;
      line-height: 29px;
      text-align: left;
    }

    p {
      font-family:var(--helveticaneueltarabicLight);  
      color: white;
      font-size: 16px;
      line-height: 20px;
      margin-top: 5px;
    }
  }

  .megamenu_ul_body {
    margin-top:25px;
    list-style: none;
    // display: flex;

    // margin-left: -3%;
    // margin-right: -3%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 8%;
    position: relative;
    z-index: 2;
    @media #{$media-1440} {
      margin-top:20px;
    }
    @media #{$media-1400} {
      grid-column-gap: 3%;
      column-gap: 3%;
    }
    .megamenu_li_body {
      // width: 25%;
      // padding: 0 3%;
      box-sizing: border-box;
      list-style: none;

      .menu_title {
        font-size: 22px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0.5px;
        text-align: start;
        color: $white;
        padding: 20px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        width: 80%;
        display: inline-block;
        font-family: var(--helveticaNeue);
        min-height: 85px;
        @media #{$media-1440} {
          width: 83%;
        }
        @media #{$media-1400} {
          width: 100%;
        }
        &:hover{
          color: #FCB136;
        }
      }

      .megamenu_sub_ul_sec {
        padding-top: 30px;
        list-style: none;

        li {
          padding: 8px 0;
          // sjanskja
          width: 100%;
          @media #{$media-1440} {
            padding: 6px 0;
          }
          a {
            text-decoration: none;
            font-size: 15px;
            font-weight: 300;
            line-height: 22.5px;
            text-align: left;
            color: $white;
            font-family: var(--helveticaNeue);
            &:hover{
              color: #FCB136;
            }
          }
        }
      }
    }
  }
}
.header_btn{
  background-color: #FCB136 !important;
  border: 1px solid #FCB136 !important;
  
  img{
    filter: unset !important;
    width: 12px;
  }
  &:hover{
    background: #5e45ff !important;
  }
}