import React, { useEffect, useState } from "react";
import comon from "@/styles/comon.module.scss";

import style from "@/styles/NewsDetails.module.scss";
import InnerBanner from "@/component/InnerBannernews";
import Image from "next/image";
import Link from "next/link";
import Box from "@mui/material/Box";
import Modal from "@mui/material/Modal";

import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";

// import required modules
import { Navigation, Autoplay } from "swiper/modules";
import InquireNow from "@/component/InquireNow";


import { useRouter } from "next/router";
import Yoast from "@/component/yoast";
import {
  getInsightsPostSlugs,
  getInsightstaxonamyList,  
  gettopicCatList,
  getInsightsPosts,
  getindustriesCatList,
  getInsightsRelatedTaxonamy,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import { parseISO, format } from "date-fns";

 
const DateConvert = (datetimeString) => {
  if (!datetimeString || typeof datetimeString !== "string") {
    console.error("Invalid input for DateConvert:", datetimeString);
    return "Invalid Date";
  }

  try {
    const parsedDate = parseISO(datetimeString);
    const formattedDate = format(parsedDate, "dd-MM-yyyy");
    return formattedDate;
  } catch (error) {
    console.error("Error parsing or formatting date:", error);
    return "Invalid Date";
  }
};

const NewsDetails = (props) => {

   const router = useRouter();
    //console.log(router);
    const { slug } = router.query; // Get the slug from the query parameters
    const { insightPost } = router.query; // Get the slug from the query parameters
    const [open, setOpen] = useState(false);
    const [selectedVideo, setSelectedVideo] = useState(null); // State to store selected video data
    const language = router.locale === "ar" ? "ar" : "en";
    const newsurl = router.locale === "ar" ? "/ar" : "";
  

    const handleOpen = (video) => {
        setSelectedVideo(video); // Update the selected video data
        setOpen(true); // Open the modal
    };

    const handleClose = () => {
        setOpen(false); // Close the modal
        setSelectedVideo(null); // Clear the selected video
  };
  
     const popUpstyle = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: "fit-content",
        border: "0 solid #000",
        boxShadow: 24,
        outline: "none",

        // p: 4,
    };
  
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
     useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
     }, []);
    
    
    const yoastData = props?.InsightsSlugData[0]?.yoast_head_json;

  if (!props.InsightsSlugData[0]) {
    return null;
  }
    
   

  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}
      
      
      
       {props &&
                props?.InsightsSlugData[0] &&
                props?.InsightsSlugData[0]?.acf &&
                props?.InsightsSlugData[0]?.acf?.banner_title &&
                (props?.InsightsSlugData[0]?.acf?.mob_banner_image ||
                props?.InsightsSlugData[0]?.acf?.banner_image ||
                props?.InsightsSlugData[0]?.acf?.banner_viedo ||                
                props?.InsightsSlugData[0]?.acf?.banner_button_link ||                
          props?.InsightsSlugData[0]?.acf?.banner_type) ? (
          <div className="banner_overlay">
                <InnerBanner
                tagsnews={props?.InsightsSlugData[0]?.acf?.listing_tag}  
                pagename={props?.InsightsSlugData[0]?.acf?.banner_title}               
                background={`${isMobile ? props?.InsightsSlugData[0]?.acf?.mob_banner_image?.url : props?.InsightsSlugData[0]?.acf?.banner_image?.url}`}          
                videoSrc={props?.InsightsSlugData[0]?.acf?.banner_viedo?.url}
                banner_type={props?.InsightsSlugData[0]?.acf?.banner_type}  
                dateSec={DateConvert(props?.InsightsSlugData[0]?.date)}
                headerFontSize={"font70"}
                paddingTop={"paddingTop370"}
              paddingBottom={"paddingBottom150"}
              bannerbutton={props?.InsightsSlugData[0]?.acf?.banner_button_link}

            />
            </div>
            ) : null
      }
    
      

 {props.InsightsSlugData[0].acf.main_content && (
<section className={style.news_details_main_section}>
        <div
          className={`${comon.wrap} ${comon.pt_100} ${comon.pb_100} ${style.news_details_sub_section}`}
        >
          <div className={style.news_details_sub_left_section}>
              
            {props.InsightsSlugData[0].acf.main_content.map(
              (productssection, productIndex) => {
                const layout = productssection.acf_fc_layout;

                if (layout === "blog_content") {
                  return (
                    <div className={style.news_details_sub_left_content1} key={productIndex}  data-aos="fade-up"
                  data-aos-duration="1000">
                      {productssection.list_content && parse(productssection.list_content)}
                                 
                    </div>
                  );
                } else if (layout === "video_section") {
                  return (
                     <div className={style.news_details_sub_left_content1} key={productIndex}  data-aos="fade-up"
                  data-aos-duration="1000">
                        {productssection?.cover_image && productssection?.video_file &&
                      <div className={style.news_details_video_block}>
                          <Image
                            src={productssection.cover_image?.url ||"/images/noimage-insights.png"}
                            height={250}
                            width={700}
                            alt=""
                          />
                       
                          <Link href="#."                               
                                onClick={(e) => {
                                    e.preventDefault(); // Prevent default link behavior
                                    handleOpen(productssection?.video_file); // Pass the video data
                                }}
                                className={style.video_icon}
                                >
                          <div className={`${style.news_details_video_play_btn} `}>
                            <Image
                              src={"/images/media-resource-img-play-btn.svg"}
                              height={95}
                              width={95}
                              alt=""
                            />
                            </div>
                            </Link>
                        </div>
                         }
                      {productssection?.video_text &&
                        <div
                          className={`${style.news_details_highlight_area} ${comon.mt_30}`}
                          data-aos="fade-up"
                          data-aos-duration="1000"
                        >
                          {productssection?.video_text && parse(productssection?.video_text)}
                        </div>
                      }
                      {productssection?.video_file &&
                        <Modal
                          open={open}
                          onClose={handleClose}
                          aria-labelledby="modal-modal-title"
                          aria-describedby="modal-modal-description"
                        >
                          <Box sx={popUpstyle}>
                            <div className={style.video_close} onClick={handleClose}>
                              <Image
                                src={"/images/close-white.png"}
                                height={50}
                                width={50}
                                alt="Close popup"
                              />
                            </div>

                            {selectedVideo && (
                              <video
                                src={selectedVideo?.url} // Use selected video file URL
                                controls
                                autoPlay
                                width={900}
                                height={500}
                              ></video>
                            )}
                          </Box>
                        </Modal>
                      }


                  </div>
                  );
                } else if (layout === "quote_section") {
                  return (
                     <div
                      className={`${style.news_details_sub_left_policy_labs_sec} ${comon.mt_50} ${comon.mb_150}`}
                      key={productIndex}
                      data-aos="fade-up"
                       data-aos-duration="1000"
                    >
                      {productssection?.quote_content &&
                        <h5
                        className={style.quote}
                          data-aos="fade-up"
                          data-aos-duration="1000">
                          {productssection?.quote_content && parse(productssection?.quote_content)}
                        </h5>
                      }
                      {productssection?.quote_details &&
                        <span className={comon.pt_30}
                          data-aos="fade-up"
                          data-aos-duration="1000">
                          {productssection?.quote_details && parse(productssection?.quote_details)}
                        </span>
                      }
              </div>
                  );
                } else if (layout === "poll_section") {
                  return (
                   <div
                      className={`${style.news_details_how_do_they_work_sec} `}
                      key={productIndex}
                      data-aos="fade-up"
                       data-aos-duration="1000"
                    >
                      {productssection?.poll_section_title &&
                        <h4 className={comon.mt_70}
                          data-aos="fade-up"
                          data-aos-duration="1000">
                          {productssection?.poll_section_title && parse(productssection?.poll_section_title)}
                        </h4>
                      }
                      {productssection?.poll_list &&
                        <ul
                          className={`${style.news_details_how_do_they_work_slider_sec} ${comon.mt_60}  ${comon.mb_40}`}
                        >
                          {productssection?.poll_list && productssection?.poll_list.map((data, index) => (
                            <li data-aos="fade-up"
                              data-aos-duration="1000" key={index}>
                              {data?.poll_title &&
                                <span
                                  className={style.news_details_how_do_they_work_slider_head}
                                >
                                  {parse(data?.poll_title)}
                                </span>
                              }
                              <div
                                className={style.news_details_how_do_they_work_slider_outer}
                              >
                                <div
                                  className={
                                    style.news_details_how_do_they_work_slider_inner
                                  }
                                  style={{ width: `${data?.poll_value}%` }}
                                ></div>
                              </div>

                              <div
                                className={
                                  style.news_details_how_do_they_work_slider_bottom
                                }
                              >
                              
                                {data?.low_title &&
                                  <p>{parse(data?.low_title)}</p>
                                }
                                {data?.high_title &&
                                  <p>{parse(data?.high_title)}</p>
                                }
                              
                              </div>
                            </li>
                          ))}
              
                        </ul>
                      }
                    </div>
                  );
                } else if (layout === "box_section") {
                  return (
                    <div
                      className={`${style.news_details_how_do_they_work_sec} `}
                      key={productIndex}
                      data-aos="fade-up"
                       data-aos-duration="1000"
                    >
                            
                      <ul className={style.news_details_how_do_they_work_report_sec}>
                        {productssection.box_section_listing && productssection.box_section_listing.map((data, index) => (
                          <li
                            data-aos="fade-up"
                            data-aos-duration="1000" key={index}>
                            <div className={style.how_do_they_work_report_count_sec}>
                              {data.number && <span>{data.number}</span>}
                              {data.number_text && <p>{data.number_text}</p>}
                            </div>
                            {data.content &&
                              <p className={style.how_do_they_work_report_paragraph}>
                                {data.content && parse(data.content)}
                              </p>
                            }
                          </li>
                        ))} 
                    </ul>
              </div>
                  );
                } 
                return null;
              }
            )}
           
          </div>

            <div className={style.news_details_sub_right_section}>
              {props?.IndustriesCategory && props?.IndustriesCategory.length > 0 &&
                <div className={style.news_details_sub_right_content}>
                  <h4 data-aos="fade-up"
                    data-aos-duration="1000">{router.locale === "ar" ? ' الصناعات': 'Industries'} </h4>
                  <ul>
                    {props?.IndustriesCategory && props?.IndustriesCategory.map((data, Iindex) => (
                      <li
                        data-aos="fade-up"
                        data-aos-duration="1000" key={Iindex}>{data && parse(data)}</li>
                    ))}
                    
                  </ul>
                </div>
              }
              {props?.TopicCategory && props?.TopicCategory.length > 0 &&
                <div className={style.news_details_sub_right_content}>
                  <h4 data-aos="fade-up"
                    data-aos-duration="1000">{router.locale === "ar" ? ' عنوان': 'Topic'}  </h4>
                  <ul>
                    {props?.TopicCategory && props?.TopicCategory.map((data, Iindex) => (
                      <li
                        data-aos="fade-up"
                        data-aos-duration="1000" key={Iindex}>{data && parse(data)}</li>
                    ))}
                  </ul>
                </div>
              }
          </div>
        </div>
      </section>
      )}
      
      
      {/* =============We are making bold moves, together=================== */}

      <section className={style.we_are_making_bold_moves_section}>
        {props?.InsightsPostsData?.length > 0 && props?.InsightsSlugData &&
          <div
            className={`${comon.wrap} ${comon.pt_100} ${comon.pb_100} ${style.we_are_making_bold_moves_container}`}
          >
            {props?.InsightsSlugData[0]?.acf?.related_title &&
              <h3 data-aos="fade-up" data-aos-duration="1000">
            {props?.InsightsSlugData[0]?.acf?.related_title}
          </h3>
            }
            
          <div className={`${style.we_are_making_bold_moves_swiper_sec} ${comon.mt_50}`} >


            <span
              className={`${comon.custom_next} ${comon.custom_btn}  ${style.we_are_making_bold_moves_swiper_prev_btn}  custom_prev_1`}
            >
              <Image
                loading="lazy"
                src="/images/single-arrow-btn.svg"
                alt="Picture of the author"
                width={12}
                height={11}
                className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
              />
            </span>

            <span
              className={`${comon.custom_prev} ${comon.custom_btn}  ${style.we_are_making_bold_moves_swiper_next_btn} ${comon.custom_latesst_next}       custom_next_1 `}
            >
              <Image
                loading="lazy"
                src="/images/single-arrow-btn.svg"
                alt="Picture of the author"
                width={12}
                height={11}
                style={{ transform: "rotate(180deg)" }}
                className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
              />
            </span>
            <Swiper
              modules={[ Navigation, Autoplay]}
              className="mySwiper"
              slidesPerView={4}
              navigation={{
                nextEl: ".custom_next_1",
                prevEl: ".custom_prev_1",
              }}
              speed={1000}
              loop={true}
              autoplay={{
                delay: 1000,
                disableOnInteraction: false,
              }}
              spaceBetween={20}
              dir={language == "ar" ? "rtl" : "ltr"}
              key={language}
              breakpoints={{
                0: {
                  slidesPerView: 1,
                  spaceBetween: 15,
                },
                500: {
                  slidesPerView: 2,
                  spaceBetween: 15,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 15,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 15,
                },
                1200: {
                  slidesPerView: 4,
                },
              }}
            >
              {props?.InsightsPostsData && props?.InsightsPostsData.map((data, rindex) => (
                <SwiperSlide key={rindex}>
                  <li
                    className={`${style.we_are_making_bold_moves_card_body} `}                    
                  >
                    <Link
                      // href={data.slug ? `/insights/${data.catslug[0]}/${data.slug}` : "#"}
                    href={
                            data?.acf?.external_news_link
                              ? data?.acf?.external_news_link?.url
                              : `${newsurl}/insights/${data.catslug[0]}/${data.slug}`
                          }
                    target={
                      data?.acf?.external_news_link
                        ? data?.acf?.external_news_link?.target
                        : "_self"
                    }
                    >
                    <div className={style.card_wrapper}>

                   
                    {(data?.acf?.insights_page_image || data?.acf?.listing_image) ?(
                      <div
                        className={`${style.we_are_making_bold_moves_card_img}`}
                      >
                        <Image
                          src={data?.acf?.insights_page_image?.url || data?.acf?.listing_image?.url}
                          height={240}
                          width={340}
                          alt={""}
                        />
                      </div>
                    ) : (
                         <div
                        className={`${style.we_are_making_bold_moves_card_img}`}
                      >
                        <Image
                          src={"/images/no-image.jpg"}
                          height={240}
                          width={340}
                          alt={""}
                        />
                      </div>
                    )}
                    <div
                      className={`${style.we_are_making_bold_moves_card_data}`}
                    >
                      {data?.date && <span>{DateConvert(data.date)}</span>}
                      {data?.title &&
                        <h4>{parse(data?.title?.rendered)}</h4>
                      }
                      {/* cfgbvcfhg
                      {data.Content &&
                        <p>{parse(data.Content)}</p>
                      } */}
                      </div>
                      </div>
                      </Link>
                  </li>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
          </div>
}
       <InquireNow formtitle={props?.InsightsSlugData[0]?.title?.rendered} />
      </section>
    </div>
  );
};

export default NewsDetails;



export async function getStaticPaths() {
  // Fetch all advisory taxonomies (categories) and their associated posts
  const ProductlistData = await getInsightstaxonamyList(); // Fetch taxonomy data

  const AdvisoryPosts = await getInsightsPosts(); // Fetch all advisory posts

  // Generate paths for all taxonomies and their associated posts
  const paths = AdvisoryPosts.flatMap((post) => {
    return post.categories.map((categoryId) => {
      const taxonomy = ProductlistData.find((tax) => tax.id === categoryId);
      if (taxonomy) {
        return {
          params: { 
            insightPost: taxonomy.slug, // Taxonomy slug
            slug: post.slug, // Post slug
          },
        };
      }
      return null; // Skip if taxonomy is not found
    });
  }).filter(Boolean); // Remove null entries

  //console.log("Generated Paths:", paths);

  return {
    paths,
    fallback: "blocking", // Allow SSR for paths not generated at build time
  };
}


export async function getStaticProps({ params, locale }) {
   const insightPost = params.insightPost; // Taxonomy slug from the URL
   const slug = params.slug;
    //console.log("Generated Paths2:", params);
    const InsightstaxonamyList = await getInsightsRelatedTaxonamy(locale); // Fetch taxonomy data
    const InsightsPostArray = await getInsightsPostSlugs(slug, locale);
    const industriesCategory = await getindustriesCatList(locale);
    const topicCategory = await gettopicCatList(locale);
  
    
    
    const query = "";
    const InsightsPostsData = await getInsightsPosts(locale, query); 

    let InsightsPosts = [];
    if (InsightsPostArray[0] && InsightsPostArray[0]?.acf && Array.isArray(InsightsPostArray[0]?.acf?.related_post)) {
    InsightsPosts = InsightsPostArray[0]?.acf?.related_post;
    }
    // Format Investors Resources  for use in the component
    let InsightsPostsRelation = [];
    if (InsightsPosts.length > 0) {
        InsightsPostsRelation = InsightsPosts.map((id) =>
        InsightsPostsData?.find((post) => post.id === id)
        ).filter(Boolean); // To ensure undefined values (if any) are removed
    }
    
      

  // Extract IDs from InsightsPostArray[0]
  const industriesCategoryIds = InsightsPostArray[0]?.industries_category || [];
  const topicCategoryIds = InsightsPostArray[0]?.topic_category || [];

  // Filter industriesCategory and topicCategory by matching IDs
  const filteredIndustriesCategory = industriesCategory
    .filter((cat) => industriesCategoryIds.includes(cat.id)) // Match by ID
    .map((cat) => cat.name); // Extract names

  
  // console.log('locale.locale', industriesCategory)

  const filteredTopicCategory = topicCategory
    .filter((cat) => topicCategoryIds.includes(cat.id)) // Match by ID
    .map((cat) => cat.name); // Extract names

  //console.log('testingdata', filteredTopicCategory)
  

  // Add `catslug` to each post in InsightsPostsRelation
  InsightsPostsRelation = InsightsPostsRelation.map((post) => {
    const categorySlugs = post.categories
      ?.map((catId) => {
        const matchingCategory = InsightstaxonamyList.find(
          (taxonomy) => taxonomy.id === catId
        );
        return matchingCategory?.slug; // Return the slug if a match is found
      })
      .filter(Boolean); // Remove undefined values

    return {
      ...post,
      catslug: categorySlugs, // Add category slugs to the post
    };
  });

   //console.log('Filtered Topics:', InsightsPostsRelation);

  return {
    props: {
      InsightsSlugData: InsightsPostArray || [],
      InsightsPostsData: InsightsPostsRelation || [],     
      IndustriesCategory: filteredIndustriesCategory || [],     
      TopicCategory: filteredTopicCategory || [],     
    },
    revalidate: 10,
  };
}

