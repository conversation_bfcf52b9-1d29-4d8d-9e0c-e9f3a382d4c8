@import "variable", "base";

.banner_main {
    background-color: #1E1E2C;
    height: calc(100vh - var(--marq_height));
    position: relative;

    @media #{$media-1024} {
        height: 75vh;
    }

    @media #{$media-767} {
        height: calc(100vh - 110px);
    }

    @media #{$media-450} {
        background: url("/images/dgital_prdct_bg.png") center top / cover no-repeat;
    }

    .banner_slide_main {
        .wrap {
            height: 100%;
        }

        .banner_main_swipper {
            height: 100%;
            width: 100%;
        }

        .banner_main_img {
            width: 25%;
            height: 100%;

            img {
                width: auto;
                height: 100%;
                object-fit: contain;
            }
        }


        .baner_txt_wrap {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-end;
            padding-bottom: 155px;
            height: 100%;

            @media #{$media-767} {
                align-items: flex-end;
                padding-bottom: 70px;
            }

            @media #{$media-450} {
                align-items: center;
            }

            &.img_baner_txt_wrap {
                align-items: center;
                padding-bottom: 0;

                .banner_img_txt_block {
                    flex-direction: column;
                    margin-top: 100px;

                    h1 ,h3 {
                        text-align: center !important;
                        margin-bottom: 40px;

                        @media #{$media-1600} {
                            margin-bottom: 30px;
                        }

                        @media #{$media-1200} {
                            margin-bottom: 20px;
                        }
                        span{
                            text-align: center !important;
                        }
                    }

                    p {
                        display: block;
                        color: #ffffff;
                        font-family: var(--helveticaneueltarabicLight);
                        font-size: 1.25rem;
                        line-height: 1.975rem;
                        font-weight: 300;
                        text-align: center;
                        max-width: 45%;
                        margin-bottom: 40px;

                        @media #{$media-1600} {
                            margin-bottom: 30px;
                        }

                        @media #{$media-1200} {
                            margin-bottom: 20px;
                            font-size: 1.3rem;
                            line-height: 2.3rem;
                            max-width: 60%;
                        }

                        @media #{$media-995} {
                            font-size: 1.5rem;
                            line-height: 2.3rem;
                        }
                        @media #{$media-700} {
                            font-size: 1.9rem;
                            line-height: 2.7rem;
                            max-width: 90%;
                        }

                    }
                }
            }
        }

        .banner_txt_block {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-top: 10%;


            h3 {
                width: 100%;
                color: #fff;
            }

            h3 {
                @media #{$media-767} {
                    font-size: 4rem;
                }

                @media #{$media-450} {
                    font-size: 4.5rem;
                    line-height: 5.2rem;
                    margin-bottom: 25px;
                }
            }


        }
    }

    .help {
        background-color: #000;
        border-radius: 30px;
        width: 160px;
        height: 34px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        position: absolute;
        right: 14%;
        bottom: 80px;
        z-index: 9;
        transition: all .3s ease-in-out;

        @media #{$media-1600} {
            right: 5%;
        }

        @media #{$media-1400} {
            right: 3%;
        }

        @media #{$media-767} {
            right: 2%;
            bottom: 20px;
        }

        &:hover {
            transform: translateY(-10px);
        }
    }
}

.banner_image {
    width: 100%;
    height: 100%;
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: #1E1E2C;
}

.main_banner_video {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-color: #1E1E2C;
}

.main_banner_normal {
    @media #{$media-450} {
        display: none;
    }
}

.main_banner_mob {
    display: none;

    @media #{$media-450} {
        display: block;
    }
}
.banner_shape{
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    height: 150px;
    display: flex;
    @media #{$media-1600} {
        height: 120px;
    }
    @media #{$media-1400} {
        height: 110px;
    }
    @media #{$media-1024} {
        height: 95px;
        width: 100%;
    }
    img{
        width: 100%;
        height: auto;
        object-fit: contain;
        object-position: center bottom;
    }
}