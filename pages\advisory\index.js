import { useEffect, useState } from "react";
import InnerBanner from "@/component/InnerBanner";
import Overview from "@/component/Overview";
import OverviewTwoCol from "@/component/OverviewTwoCol";
import DynamicImage from "@/component/DynamicImage";
import InquireNow from "@/component/InquireNow";
import style from "@/styles/advisory.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import Buttion from "@/component/buttion/Buttion";
import buttion from "@/styles/buttion.module.scss";
import Link from "next/link";


import Yoast from "@/component/yoast";
import {
    getAdvisory,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Advisory = (props) => {
 const [activeTab, setActiveTab] = useState(0); // Keep track of active tab index
  const [activeTabData, setActiveTabData] = useState(null); // Store data of active tab

  // Handle tab click to update active tab and its data
  const handleTabClick = (index) => {
    setActiveTab(index);
    setActiveTabData(props?.AdvisoryData?.acf?.advisory_listing[index]);
  };

  useEffect(() => {
    if (props?.AdvisoryData?.acf?.advisory_listing) {
      // Set initial tab data based on the first tab if necessary
      setActiveTabData(props?.AdvisoryData?.acf?.advisory_listing[0]);
    }
  }, [props?.AdvisoryData?.acf?.advisory_listing]);


  useEffect(() => {
  const bannerSection = document.querySelector(".inner_banner_section");
  if (bannerSection) {
    bannerSection.style.overflow = "hidden";
  }
}, []);

  
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
     useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
     }, []);
    
    
    const yoastData = props?.AdvisoryData?.yoast_head_json;

  if (!props.AdvisoryData) {
    return null;
  }
  
  return (
    <>
      {yoastData && <Yoast meta={yoastData} />}

            {props &&
                props?.AdvisoryData &&
                props?.AdvisoryData?.acf &&
                props?.AdvisoryData?.acf?.banner_title &&
                (props?.AdvisoryData?.acf?.mob_banner_image ||
                props?.AdvisoryData?.acf?.banner_image ||
                props?.AdvisoryData?.acf?.breadcrumbs ||
                props?.AdvisoryData?.acf?.banner_viedo ||                
                props?.AdvisoryData?.acf?.banner_type) ? (
                <InnerBanner
                pagename={props?.AdvisoryData?.acf?.banner_title}
                breadcrumb1={props?.AdvisoryData?.acf?.active_breadcrumbs ==='yes' ? props?.AdvisoryData?.acf?.breadcrumbs : '' }
                background={`${isMobile ? props?.AdvisoryData?.acf?.mob_banner_image?.url : props?.AdvisoryData?.acf?.banner_image?.url}`}          
                videoSrc={props?.AdvisoryData?.acf?.banner_viedo?.url}
                banner_type={props?.AdvisoryData?.acf?.banner_type}              

                />
            ) : null
      }
      {props &&
             props?.AdvisoryData &&
                props?.AdvisoryData?.acf &&
                (props?.AdvisoryData?.acf?.overview_title ||
                    props?.AdvisoryData?.acf?.overview_content ) ? (
       <OverviewTwoCol
            head={props?.AdvisoryData?.acf?.overview_title}
            paragraph={props?.AdvisoryData?.acf?.overview_content}            
          />
      ) : null}
      
     
{props &&
      props?.AdvisoryData &&
        props?.AdvisoryData?.acf &&
        (props?.AdvisoryData?.acf?.core_domains_sub_title ||
        props?.AdvisoryData?.acf?.core_domains_title ||
          props?.AdvisoryData?.acf?.advisory_listing) ? (
      <section
        className={`${comon.pt_80} ${comon.pb_80}`}
        style={{
          background: "url(../images/dgital_prdct_bg.png) top left no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
          <div
            className={`${comon.w_100} ${comon.mb_50} ${comon.text_center} ${comon.impact_sub_title}`}
              >
                {props?.AdvisoryData?.acf?.core_domains_sub_title &&
                  <h6
                    data-aos="fade-up"
                    data-aos-duration="1000"
                    className={`${comon.sub_txt} ${comon.mb_10}`}
                  >
                    {props?.AdvisoryData?.acf?.core_domains_sub_title && parse(props?.AdvisoryData?.acf?.core_domains_sub_title)}
                  </h6>
                }
                {props?.AdvisoryData?.acf?.core_domains_title &&
                  <h2
                    data-aos="fade-up"
                    data-aos-duration="1000"
                    className={`${comon.h2} ${comon.mb_15} ${comon.text_white} ${comon.advisory_core_title}`}
                  >
                    {props?.AdvisoryData?.acf?.core_domains_title && parse(props?.AdvisoryData?.acf?.core_domains_title)}
                  </h2>
                }
          </div>

          <div
            className={`${comon.w_100} ${style.tab_block_main} ${style.d_flex_wrap}`}
          >
            <div className={`${style.tab_left_block}`}>
              <ul
                className={`${style.tab_ul_list}`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                {props?.AdvisoryData?.acf?.advisory_listing.map((tab, index) => (
                     <li
                      key={index}
                      onClick={() => handleTabClick(index)}
                      className={`${activeTab === index ? style.activeTab : ""}`}
                    >
                    {tab.advisory_title && parse(tab.advisory_title)}
                  </li>
                ))}
              </ul>
            </div>
               
            <div className={`${style.tab_right_block} ${comon.d_flex_wrap}`}>
              <div className={`${style.tab_img_block}`}>
                {/* <DynamicImage src={imageSrc} alt={title} /> */}
                <video
                  src={activeTabData?.advisory_file?.url}
                  width={672}
                  height={672}
                  autoPlay
                  loop
                  muted
                  playsInline
                  className={style.video}
                ></video>
              </div>
                  <div className={`${style.tab_txt_block} ${comon.tab_txt_block}`}>
                    {activeTabData?.advisory_title &&
                      <h3 data-aos="fade-up" data-aos-duration="1000">
                        {activeTabData?.advisory_title && parse(activeTabData?.advisory_title)}
                      </h3>
                    }
                <p data-aos="fade-up" data-aos-duration="1000">
                   {activeTabData?.advisory_content && parse(activeTabData?.advisory_content)}
                </p>
                    {activeTabData?.advisory_sub_list &&
                      <ul>
                        {activeTabData?.advisory_sub_list && activeTabData?.advisory_sub_list.map((item, index) => (
                          <li key={index} data-aos="fade-up" data-aos-duration="1000">
                            <Link
                              href={item?.page_link?.url || '#.'}
                              target={item?.page_link?.target}
                            >
                              {item.ad_text && parse(item.ad_text)}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    }
                    {activeTabData?.detail_page_link &&
                      <Buttion
                        aosType="fade-up"
                        aosDuration={1500}
                        text={activeTabData?.detail_page_link?.title}
                        href={activeTabData?.detail_page_link?.url}
                        target={activeTabData?.detail_page_link?.target}
                        moduleClass={buttion.strock}
                        imageSrc="/images/buttion_arrow_white.svg"
                      />
                    }
              </div>
            </div>
          </div>
        </div>
          </section>
          
      ) : null}
      
      {/* <InquireNow formtitle={props?.AdvisoryData?.title?.rendered} /> */}
       <InquireNow
        formtitle={props?.AdvisoryData?.title?.rendered}
        optionaltitle= {props?.AdvisoryData?.acf?.inq_title}
        optionalcontent = {props?.AdvisoryData?.acf?.inq_content}
      />
    </>
  );
};

export default Advisory;



export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const AdvisoryData = await getAdvisory(locale);

  return {
    props: {
      AdvisoryData: AdvisoryData || null,  
    },
    revalidate: 10,
  };
};