import React, { useState, useEffect, useRef } from "react";
import Link from "next/link"; // Import Link for navigation
import But<PERSON> from "@/component/buttion/Buttion"; // Import custom Button component
import comon from "@/styles/comon.module.scss"; // Import common styles
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import { useRouter } from "next/router"; // Import useRouter hook for routing
import Image from "next/image";
import impact from "@/styles/impact.module.scss";
import parse from "html-react-parser";

const Impact = ({core_domains_sub_text, core_domains_title, core_domains_content, core_domains_list}) => {
  // Remove opacity class after image loads
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };

  const [activeBlock, setActiveBlock] = useState(null);
  const [impactImage, setimpactImage] = useState(0);

  const handleClick = (index) => {

    setActiveBlock(activeBlock === index ? null : index);
    setimpactImage(index);

  };
// console.log(activeBlock);
  const Imagepath = core_domains_list[impactImage]?.core_file?.url || "";

  //console.log(impactImage);


  const ref = useRef();

  useEffect(() => {
    // Define a function that handles clicks outside the component
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
      }
    };

    // Add event listener for clicks
    document.addEventListener("mousedown", handleClickOutside);

    // Cleanup the event listener when the component is unmounted or dependencies change
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref]);

  return (
    <>
      <div className={`${comon.wrap} ${comon.pt_100} ${comon.pb_250} imapact_sec` }>
        <div
          className={`${comon.w_100} ${comon.mb_50} ${comon.text_center} ${impact.impact_sub_title}`}
        >
          {core_domains_sub_text &&
            <h6 data-aos="fade-up" data-aos-duration="1000" className={`${comon.sub_txt} ${comon.mb_10}`}>{core_domains_sub_text}</h6>
          }
          {core_domains_title &&
          <h2 data-aos="fade-up" data-aos-duration="1000" className={`${comon.h2} ${comon.mb_15} ${comon.text_white}`}>
           {core_domains_title}
          </h2>
          }
          {core_domains_content &&
            <div data-aos="fade-up" data-aos-duration="1000" className={comon.margin_top_arabic}>
              {core_domains_content && parse(core_domains_content)}
            </div>
          }
        </div>
        {core_domains_list && (
          <div className={`${comon.w_100} ${comon.d_flex} ${comon.d_flex_wrap}`}>
            <div className={`${impact.imact_section_left} ${impact.p_relative}`} data-aos="fade-up" data-aos-duration="1000">
              {core_domains_list && core_domains_list?.length > 0 && (
                <div onClick={() => handleClick(0)}
                  className={`${impact.imact_txt_block_01}   ${impact.position_01} ${comon.position_01} ${impact.p_relative} ${activeBlock === 0 ? impact.activeBlock : ""}`}

                >
                  <div className={`${impact.border_top}`}>
                    {/* <Image
                      src="/images/border_top_01-01.svg"
                      alt="call"
                      width={397}
                      height={28.9728}
                    /> */}
                  </div>

                  {core_domains_list && core_domains_list.slice(0, 1).map((data, index) => (
                    <div
                      key={index}
                      className={`${impact.txt_block} ${comon.txt_block} ${impact.txt_block_01} `}
                    >
                      <h3>{data.core_title && parse(data.core_title)}</h3>
                      <span className={`${impact.impact_arrow} ${comon.impact_arrow}`}>
                        <Image
                          src="/images/expand_arrow.svg"
                          alt="call"
                          width={4}
                          height={7}
                        />
                      </span>
                      <div className={`${impact.hover_txt_block}  `} ref={ref}>
                        <p>{data.core_content && parse(data.core_content)}</p>
                        {data.core_know_more &&
                          <Link className={`${impact.impact_link} ${comon.impact_link} `}
                            target={data.core_know_more?.target}
                            href={data.core_know_more?.url}>
                            {data.core_know_more?.title}
                            <Image
                              src="/images/impact_link_arrow.svg"
                              alt="call"
                              width={17}
                              height={17}
                            />
                          </Link>
                        }
                      </div>
                    </div>
                  ))}

                  <div className={`${impact.border_bot}`}>
             
                  </div>
                </div>
              )}
              {core_domains_list && core_domains_list?.length > 1 && (
                <div onClick={() => handleClick(1)}
                  className={`${impact.imact_txt_block_01}  ${impact.position_02}  ${comon.position_02} ${impact.p_relative} ${activeBlock === 1 ? impact.activeBlock : ""}`}
                >
                  <div className={`${impact.border_top}`}>
                
                  </div>

                  {core_domains_list && core_domains_list.slice(1, 2).map((data, index) => (
                    <div
                      key={index}
                      className={`${impact.txt_block} ${comon.txt_block} ${impact.txt_block_02} `}
                    >
                      <h3>{data.core_title && parse(data.core_title)}</h3>
                      <span className={`${impact.impact_arrow}  ${comon.impact_arrow}`}>
                        <Image
                          src="/images/expand_arrow.svg"
                          alt="call"
                          width={4}
                          height={7}
                        />
                      </span>
                      <div className={`${impact.hover_txt_block} `}>
                        <p>{data.core_content && parse(data.core_content)}</p>
                        {data.core_know_more &&
                          <Link className={`${impact.impact_link} ${comon.impact_link} `}
                            target={data.core_know_more?.target}
                            href={data.core_know_more?.url}>
                            {data.core_know_more?.title}
                            <Image
                              src="/images/impact_link_arrow.svg"
                              alt="call"
                              width={17}
                              height={17}
                            />
                          </Link>
                        }
                      </div>
                    </div>
                  ))}
              

                  <div className={`${impact.border_bot}`}>
                
                  </div>
                </div>
              )}
            </div>
            {/* circlie start  */}
            <div className={`${impact.imact_section_home}`} data-aos="fade-up" data-aos-duration="1000">
              <div className={`${impact.imact_section_home_container}`}>
                {Imagepath ? (
                  <video
                    src={Imagepath}
                    width={672}
                    height={672}
                    autoPlay
                    loop
                    muted
                    playsInline
                    className={impact.video}
                  >
                  </video>
                ) : null}
              </div>
              <Image
                src="/images/cercle_svg.svg"
                alt="call"
                width={502}
                height={452}
              />

              <div className={`${impact.shadow_block}`}>
                <Image
                  src="/images/shadow.png"
                  alt="call"
                  width={660}
                  height={120}
                />
              </div>
              {core_domains_list && core_domains_list?.length > 4 && (
                <div onClick={() => handleClick(4)}
                  className={`${impact.imact_txt_block_01}  ${impact.position_05}  ${comon.position_05} ${impact.p_relative} ${activeBlock === 4 ? impact.activeBlock : ""}`}
                >
              

                  {core_domains_list && core_domains_list.slice(4, 5).map((data, index) => (
                    <div
                      key={index}
                      className={`${impact.txt_block} ${comon.txt_block} ${impact.txt_block_05} `}
                    >
                      <h3>{data.core_title && parse(data.core_title)}</h3>
                      <span className={`${impact.impact_arrow}  ${comon.impact_arrow}`}>
                        <Image
                          src="/images/expand_arrow.svg"
                          alt="call"
                          width={4}
                          height={7}
                        />
                      </span>
                      <div className={`${impact.hover_txt_block} `}>
                        <p>{data.core_content && parse(data.core_content)}</p>
                        {data.core_know_more &&
                          <Link className={`${impact.impact_link} ${comon.impact_link}`}
                            target={data.core_know_more?.target}
                            href={data.core_know_more?.url}>
                            {data.core_know_more?.title}
                            <Image
                              src="/images/impact_link_arrow.svg"
                              alt="call"
                              width={17}
                              height={17}
                            />
                          </Link>
                        }
                      </div>
                    </div>
                  ))}
             

                </div>
              )}
            </div>
            {/* circle end  */}
        

            <div className={`${impact.imact_section_right} ${impact.p_relative}`} data-aos="fade-up" data-aos-duration="1000">
              {core_domains_list && core_domains_list?.length > 2 && (
                <div onClick={() => handleClick(2)}
                  className={`${impact.imact_txt_block_01}  ${impact.position_03}  ${comon.position_03} ${impact.p_relative}   ${activeBlock === 2 ? impact.activeBlock : ""}`}
                >
             

                  {core_domains_list && core_domains_list.slice(2, 3).map((data, index) => (
                    <div
                      key={index}
                      className={`${impact.txt_block} ${comon.txt_block} ${impact.txt_block_03} `}
                    >
                      <h3>{data.core_title && parse(data.core_title)}</h3>
                      <span className={`${impact.impact_arrow}  ${comon.impact_arrow}`}>
                        <Image
                          src="/images/expand_arrow.svg"
                          alt="call"
                          width={4}
                          height={7}
                        />
                      </span>

                      <div className={`${impact.hover_txt_block} `}>
                        <p>{data.core_content && parse(data.core_content)}</p>

                        {data.core_know_more &&
                          <Link className={`${impact.impact_link} ${comon.impact_link}`}
                            target={data.core_know_more?.target}
                            href={data.core_know_more?.url}>
                            {data.core_know_more?.title}
                            <Image
                              src="/images/impact_link_arrow.svg"
                              alt="call"
                              width={17}
                              height={17}
                            />
                          </Link>
                        }
                      </div>
                    </div>
                  ))}
              

            
                </div>
              
              )}
            
              {core_domains_list && core_domains_list?.length > 3 && (
                <div onClick={() => handleClick(3)}
                  className={`${impact.imact_txt_block_01}  ${impact.position_02}  ${comon.position_02} ${impact.p_relative} ${activeBlock === 3 ? impact.activeBlock : ""}`}
                >
              

                  {core_domains_list && core_domains_list.slice(3, 4).map((data, index) => (
                    <div
                      key={index}
                      className={`${impact.txt_block} ${comon.txt_block} ${impact.txt_block_04} `}
                    >
                      <h3>{data.core_title && parse(data.core_title)}</h3>
                      <span className={`${impact.impact_arrow}  ${comon.impact_arrow}`}>
                        <Image
                          src="/images/expand_arrow.svg"
                          alt="call"
                          width={4}
                          height={7}
                        />
                      </span>
                      <div className={`${impact.hover_txt_block} `}>
                        <p>{data.core_content && parse(data.core_content)}</p>
                        {data.core_know_more &&
                          <Link className={`${impact.impact_link} ${comon.impact_link}`}
                            target={data.core_know_more?.target}
                            href={data.core_know_more?.url}>
                            {data.core_know_more?.title}
                            <Image
                              src="/images/impact_link_arrow.svg"
                              alt="call"
                              width={17}
                              height={17}
                            />
                          </Link>
                        }
                      </div>
                    </div>
                  ))}
             

                </div>
              )}

              {/* {core_domains_list && core_domains_list?.length > 4 && (
                <div onClick={() => handleClick(4)}
                  className={`${impact.imact_txt_block_01}  ${impact.position_05}  ${comon.position_05} ${impact.p_relative} ${activeBlock === 4 ? impact.activeBlock : ""}`}
                >
              

                  {core_domains_list && core_domains_list.slice(4, 5).map((data, index) => (
                    <div
                      key={index}
                      className={`${impact.txt_block} ${comon.txt_block} ${impact.txt_block_05} `}
                    >
                      <h3>{data.core_title && parse(data.core_title)}</h3>
                      <span className={`${impact.impact_arrow}  ${comon.impact_arrow}`}>
                        <Image
                          src="/images/expand_arrow.svg"
                          alt="call"
                          width={4}
                          height={7}
                        />
                      </span>
                      <div className={`${impact.hover_txt_block} `}>
                        <p>{data.core_content && parse(data.core_content)}</p>
                        {data.core_know_more &&
                          <Link className={`${impact.impact_link} ${comon.impact_link}`}
                            target={data.core_know_more?.target}
                            href={data.core_know_more?.url}>
                            {data.core_know_more?.title}
                            <Image
                              src="/images/impact_link_arrow.svg"
                              alt="call"
                              width={17}
                              height={17}
                            />
                          </Link>
                        }
                      </div>
                    </div>
                  ))}
             

                </div>
              )} */}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Impact;
