@import "variable", "base";
.publication_news_section {
    width: 100%;
    background-color: rgb(255, 255, 255);
    h5{
        font-size: 16px;
        color: rgba(0, 0, 0,.6);
        font-family: var(--helveticaNeue);
        font-weight: 400;
    }
    .publication_news_container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: .7%;
        row-gap: 15px;
        list-style: none;
        @media #{$media-950} {
            gap: 2%;
            row-gap: 15px;
        }
        .publication_news_card_body {
            width: 24%;
            @media #{$media-1024} {
                width: 32%;
            }
            @media #{$media-950} {
                width: 49%;
            }
            @media #{$media-767} {
                width: 100%;
            }
        }
    }
}
.no_posts{
    margin: 0 auto;
    display: flex;
    justify-content: center;
    padding-bottom: 60px;
}