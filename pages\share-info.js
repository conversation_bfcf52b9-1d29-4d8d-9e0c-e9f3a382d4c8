import React, { useEffect, useState } from "react";
import InnerBanner from "@/component/InnerBanner";
import TabSection from "@/component/Tab-section";
import style from "@/styles/shareInfo.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import Share<PERSON>hart from '@/component/shareChart'
import Yoast from "@/component/yoast";
import {
  getShareInfo,
  getIrMenuLinks,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import xml2js from "xml2js";
import { useRouter } from "next/router";

const ShareInfo = (props) => {
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  const router = useRouter();
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const yoastData = props?.shareinfoData?.yoast_head_json;
  // for stock table
  const pathtrim = props?.stocktable["soapenv:Envelope"]["soapenv:Body"]["p451:getDetailQuoteForCompanyResponse"].getDetailQuoteForCompanyReturn;

   const changeAmount = parseFloat(pathtrim?.changeAmount || 0).toFixed(2);
  const changePercentage = parseFloat(pathtrim?.changePercentage || 0).toFixed(2);

  if (!props.shareinfoData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.shareinfoData &&
      props?.shareinfoData?.acf &&
      props?.shareinfoData?.acf?.banner_title &&
      (props?.shareinfoData?.acf?.mob_banner_image ||
        props?.shareinfoData?.acf?.banner_image ||
        props?.shareinfoData?.acf?.breadcrumbs ||
        props?.shareinfoData?.acf?.banner_viedo ||
        props?.shareinfoData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.shareinfoData?.acf?.banner_title}
          breadcrumb1={
            props?.shareinfoData?.acf?.active_breadcrumbs === "yes"
              ? props?.shareinfoData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.shareinfoData?.acf?.mob_banner_image?.url
              : props?.shareinfoData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.shareinfoData?.acf?.banner_viedo?.url}
          banner_type={props?.shareinfoData?.acf?.banner_type}
        />
      ) : null}

      {/* =========Tab Section====== */}
      {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.investors_relations_menu && (
          <TabSection tabs={props?.IRMenuData?.investors_relations_menu} />
        )}

      {/* =========Share info Section====== */}

      {props &&
      props?.shareinfoData &&
      props?.shareinfoData?.acf &&
      (props?.shareinfoData?.acf?.share_info_title ||       
        props?.shareinfoData?.acf?.stock_performance_title ) ? (
        <section className={`${style.share_info_section}`}>
          <div
            className={`${style.share_info_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100}`}
          >
            {props &&
              props?.shareinfoData &&
              props?.shareinfoData?.acf &&
              props?.shareinfoData?.acf?.share_info_title && (
                <h3>
                  {props?.shareinfoData?.acf?.share_info_title &&
                    parse(props?.shareinfoData?.acf?.share_info_title)}
                </h3>
              )}
            <div className={`${style.share_info_content_sec} `}>
              {props &&
                props?.stocktable && (
                  <div className={style.share_info_left}>
                    {/* <Image src={props?.shareinfoData?.acf?.share_info_image?.url} width={594} height={501}  alt="" /> */}

                    <div className={style.inner_banner_share_chart_wrap}>
                      <div className={style.inner_banner_share_chart}>
                        <div className={style.diasmond}>
                          <Image
                            src="/images/diamond_new.svg"
                            alt=""
                            width={25}
                            height={40}
                          />
                        </div>
                        <div className={style.stock_info}>
                          {router.locale === "ar" ?
                            <h5>{pathtrim?.companyLongNameAr && parse(pathtrim?.companyLongNameAr)}</h5>
                            :
                            <h5>{pathtrim?.companyLongName && parse(pathtrim?.companyLongName)}</h5>
                          }
                          <p>{pathtrim?.id && parse(pathtrim?.id)}</p>
                          <h6 className={`${style.stockData} ${style.down}`}>
                            <span>{pathtrim?.lastTradePrice &&
                              parseFloat(pathtrim?.lastTradePrice).toFixed(2)} </span>
                           
                            {/* 
                            {changeAmount > 0 ?
                              <Image src="/images/stock_up.svg" alt="" width={28} height={28} />
                              : changeAmount <= 0 ?
                                <Image src="/images/stock_down.svg" alt="" width={28} height={28} />
                                : null
                            } */
                            }
                            <span className={`changeTxt ${changeAmount > 0 ? "upTxt" : changeAmount <= 0 ? "downTxt" : "normalTxt"}`}>     
                              ({changeAmount}) ({changePercentage}%)
                            </span>                           
                          </h6>
                        </div>
                      </div>
                    </div>
                    <div className={style.share_graph_wrapper}>
                      <ShareChart chartdata={pathtrim} />
                    </div>
                  </div>
                )}
                {props &&
                  props?.stocktable && (
                <div className={style.share_info_right}>
                  {props &&
                    props?.shareinfoData &&
                    props?.shareinfoData?.acf &&
                    props?.shareinfoData?.acf?.stock_performance_title && (
                      <h5>
                        {props?.shareinfoData?.acf?.stock_performance_title &&
                          parse(
                            props?.shareinfoData?.acf?.stock_performance_title
                          )}
                      </h5>
                    )}

                  <div className={style.table_sec}>
                      <table className={style.table}>
                        <tbody>
                          <tr>                            
                              <td>{props?.shareinfoData?.acf?.stock_text_1 && parse(props?.shareinfoData?.acf?.stock_text_1)}</td>
                              <td>{props?.shareinfoData?.acf?.stock_text_1 && parseFloat(pathtrim?.lastTradePrice).toFixed(2)}</td>
                              <td>{props?.shareinfoData?.acf?.stock_text_2 && parse(props?.shareinfoData?.acf?.stock_text_2)}</td>
                              <td>{props?.shareinfoData?.acf?.stock_text_2 && parseFloat(pathtrim?.lastTradeQuantity).toFixed(2)}</td>
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_3 && parse(props?.shareinfoData?.acf?.stock_text_3)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_3 && parseFloat(pathtrim?.lowPrice).toFixed(2)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_4 && parse(props?.shareinfoData?.acf?.stock_text_4)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_4 && parseFloat(pathtrim?.highPrice).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_5 && parse(props?.shareinfoData?.acf?.stock_text_5)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_5 && pathtrim?.noOfTrades}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_6 && parse(props?.shareinfoData?.acf?.stock_text_6)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_6 && parseFloat(pathtrim?.volumeTraded).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_7 && parse(props?.shareinfoData?.acf?.stock_text_7)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_7 && parseFloat(pathtrim?.avgTradeSize).toFixed(2)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_8 && parse(props?.shareinfoData?.acf?.stock_text_8)}</td>
                            <td className={`changeTxt ${changeAmount > 0 ? "upTxt" : changeAmount <= 0 ? "downTxt" : "normalTxt"}`} >
                              ({props?.shareinfoData?.acf?.stock_text_8 && changeAmount})
                            </td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_9 && parse(props?.shareinfoData?.acf?.stock_text_9)}</td>
                            <td className={`changeTxt ${changeAmount > 0 ? "upTxt" : changeAmount <= 0 ? "downTxt" : "normalTxt"}`} >
                              ({props?.shareinfoData?.acf?.stock_text_9 && changePercentage})
                            </td>
                            <td>{props?.shareinfoData?.acf?.stock_text_10 && parse(props?.shareinfoData?.acf?.stock_text_10)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_10 && parseFloat(pathtrim?.change52week).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_11 && parse(props?.shareinfoData?.acf?.stock_text_11)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_11 && pathtrim?.high52weekDate}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_12 && parse(props?.shareinfoData?.acf?.stock_text_12)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_12 && parseFloat(pathtrim?.high52WeeksPrice).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_13 && parse(props?.shareinfoData?.acf?.stock_text_13)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_13 && pathtrim?.low52weekDate}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_14 && parse(props?.shareinfoData?.acf?.stock_text_14)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_14 && parseFloat(pathtrim?.low52WeeksPrice).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_15 && parse(props?.shareinfoData?.acf?.stock_text_15)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_15 && parseFloat(pathtrim?.startOfYearPrice).toFixed(2)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_16 && parse(props?.shareinfoData?.acf?.stock_text_16)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_16 && parseFloat(pathtrim?.yearAgoPrice).toFixed(2)}</td>                            
                          </tr>                          
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_17 && parse(props?.shareinfoData?.acf?.stock_text_17)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_17 && parseFloat(pathtrim?.closePrice).toFixed(2)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_18 && parse(props?.shareinfoData?.acf?.stock_text_18)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_18 && parseFloat(pathtrim?.earning).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_19 && parse(props?.shareinfoData?.acf?.stock_text_19)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_19 && parseFloat(pathtrim?.EPS).toFixed(2)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_20 && parse(props?.shareinfoData?.acf?.stock_text_20)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_20 && parseFloat(pathtrim?.marketCap).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_21 && parse(props?.shareinfoData?.acf?.stock_text_21)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_21 && pathtrim?.numberOfShares}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_22 && parse(props?.shareinfoData?.acf?.stock_text_22)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_22 && pathtrim?.numberOfSharesFloated}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_23 && parse(props?.shareinfoData?.acf?.stock_text_23)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_23 && parseFloat(pathtrim?.PBValue).toFixed(2)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_24 && parse(props?.shareinfoData?.acf?.stock_text_24)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_24 && parseFloat(pathtrim?.PERatio).toFixed(2)}</td>                            
                          </tr>
                          <tr>
                            <td>{props?.shareinfoData?.acf?.stock_text_25 && parse(props?.shareinfoData?.acf?.stock_text_25)}</td>
                            <td>{props?.shareinfoData?.acf?.stock_text_25 && parseFloat(pathtrim?.bookValue).toFixed(2)}</td>
                           <td></td><td></td>                  
                          </tr>                                                  
                        </tbody>                    
                    </table>
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>
      ) : null}
    </div>
  );
};

export default ShareInfo;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const shareinfoData = await getShareInfo(locale);
  const IRMenuData = await getIrMenuLinks(locale);

  //console.log('testingdata', IrMediaData)

  const soapRequest = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://services.RSS.tadawul.com">
      <soapenv:Header/>
      <soapenv:Body>
          <ser:getDetailQuoteForCompany>
              <companyId>9570</companyId>
              <secureKey>*********</secureKey>
          </ser:getDetailQuoteForCompany>
      </soapenv:Body>
  </soapenv:Envelope>`;

  
    const response = await fetch("https://webservices.tadawul.com.sa/Tadawul_WebAPI/services/GetDetailQuote", {
      method: "POST",
      headers: {
        "Content-Type": "text/xml",
        "SOAPAction": "getDetailQuoteForCompany",
      },
      body: soapRequest,
    });

    const xmlText = await response.text(); // Get XML response as text

    // Convert XML to JSON
    const parser = new xml2js.Parser({ explicitArray: false });
    const jsonData = await parser.parseStringPromise(xmlText);

    return {
      props: {
        shareinfoData: shareinfoData || null,
        IRMenuData: IRMenuData || null,
        stocktable: jsonData || null,
      },
      revalidate: 10,
    };
  };

