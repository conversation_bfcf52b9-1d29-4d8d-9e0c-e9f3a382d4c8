import React, { useEffect, useState } from "react";
import comon from "@/styles/comon.module.scss";
import InquireNow from "@/component/InquireNow";
import Image from "next/image";
import styles from "@/styles/awards.module.scss";
// import required modules
import InnerBanner from "@/component/InnerBanner";
import Link from "next/link";
import OverviewTwoCol from "@/component/OverviewTwoCol";
import { Swiper, SwiperSlide } from "swiper/react";
import ExpertPanel from "@/component/expertPanel";
import RelatedNews from "@/component/relatedNews";
import EngagmentProgram from "@/component/EngagmentProgram";

import "swiper/css/navigation";
import { Navigation, Autoplay } from "swiper/modules";
// Import Swiper styles
import "swiper/css";

import { useRouter } from "next/router";
import Yoast from "@/component/yoast";
import {
  getAdvisoryPostSlugs,
  getAdvisorytaxList,
  getAdvisoryPosts,
  getInsightsPosts,
  getInsightsRelatedTaxonamy,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const publicEngagementPage = (props) => {
  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";
  //console.log(router);
  const { slug } = router.query; // Get the slug from the query parameters
  const { taxonamySlug } = router.query; // Get the slug from the query parameters

  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const yoastData = props?.AdvisorySlugData[0]?.yoast_head_json;

  if (!props.AdvisorySlugData[0]) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.AdvisorySlugData[0] &&
      props?.AdvisorySlugData[0]?.acf &&
      props?.AdvisorySlugData[0]?.acf?.banner_title &&
      (props?.AdvisorySlugData[0]?.acf?.mob_banner_image ||
        props?.AdvisorySlugData[0]?.acf?.banner_image ||
        props?.AdvisorySlugData[0]?.acf?.breadcrumbs ||
        props?.AdvisorySlugData[0]?.acf?.banner_viedo ||
        props?.AdvisorySlugData[0]?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.AdvisorySlugData[0]?.acf?.banner_title}
          breadcrumb1={
            props?.AdvisorySlugData[0]?.acf?.active_breadcrumbs === "yes"
              ? props?.AdvisorySlugData[0]?.acf?.breadcrumbs
              : ""
          }
          breadcrumb4={props?.AdvisorySlugData[0]?.acf?.banner_title}
          background={`${
            isMobile
              ? props?.AdvisorySlugData[0]?.acf?.mob_banner_image?.url
              : props?.AdvisorySlugData[0]?.acf?.banner_image?.url
          }`}
          videoSrc={props?.AdvisorySlugData[0]?.acf?.banner_viedo?.url}
          banner_type={props?.AdvisorySlugData[0]?.acf?.banner_type}
        />
      ) : null}

      {/* -----------Overview section----------- */}

      {props &&
      props?.AdvisorySlugData &&
      props?.AdvisorySlugData[0]?.acf &&
      (props?.AdvisorySlugData[0]?.acf?.overview_title ||
        props?.AdvisorySlugData[0]?.acf?.overview_content) ? (
        <OverviewTwoCol
          head={props?.AdvisorySlugData[0]?.acf?.overview_title}
          paragraph={props?.AdvisorySlugData[0]?.acf?.overview_content}
        />
      ) : null}

      {/* /*---------------Why Belive section-------------- */}

      {props &&
      props?.AdvisorySlugData[0] &&
      props?.AdvisorySlugData[0]?.acf &&
      (props?.AdvisorySlugData[0]?.acf?.why_believe_title ||
        props?.AdvisorySlugData[0]?.acf?.why_believe_content ||
        props?.AdvisorySlugData[0]?.acf?.whats_benefit_title ||
        props?.AdvisorySlugData[0]?.acf?.whats_benefit_content) ? (
        <section className={`${styles.why_section}`}>
          <div
            className={`${comon.public_engagement_sub_domain_container}  ${comon.wrap} ${comon.pt_100}  ${comon.pb_100} `}
          >
            {(props?.AdvisorySlugData[0]?.acf?.why_believe_title ||
              props?.AdvisorySlugData[0]?.acf?.why_believe_content) && (
              <div className={styles.why_items}>
                {props?.AdvisorySlugData[0]?.acf?.why_believe_title && (
                  <div className={styles.why_items_left}>
                    <h3 data-aos="fade-up" data-aos-duration="1000">
                      {parse(
                        props?.AdvisorySlugData[0]?.acf?.why_believe_title
                      )}
                    </h3>
                  </div>
                )}
                {props?.AdvisorySlugData[0]?.acf?.why_believe_content && (
                  <div
                    className={styles.why_items_right}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    {parse(
                      props?.AdvisorySlugData[0]?.acf?.why_believe_content
                    )}
                  </div>
                )}
              </div>
            )}
            {(props?.AdvisorySlugData[0]?.acf?.whats_benefit_title ||
              props?.AdvisorySlugData[0]?.acf?.whats_benefit_content) && (
              <div className={styles.why_items}>
                {props?.AdvisorySlugData[0]?.acf?.whats_benefit_title && (
                  <div className={styles.why_items_left}>
                    <h3 data-aos="fade-up" data-aos-duration="1500">
                      {parse(
                        props?.AdvisorySlugData[0]?.acf?.whats_benefit_title
                      )}
                    </h3>
                  </div>
                )}
                {props?.AdvisorySlugData[0]?.acf?.whats_benefit_content && (
                  <div
                    className={styles.why_items_right}
                    data-aos="fade-up"
                    data-aos-duration="1500"
                  >
                    {parse(
                      props?.AdvisorySlugData[0]?.acf?.whats_benefit_content
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </section>
      ) : null}

      {/* /*--------------End of Why Belive section--------------- */}

      {/* -----------Public Engagement Sub Domains section----------- */}

      {props &&
      props?.AdvisorySlugData &&
      props?.AdvisorySlugData[0]?.acf &&
      (props?.AdvisorySlugData[0]?.acf?.services_section_title ||
        props?.AdvisorySlugData[0]?.acf?.services_listing) ? (
        <section className={`${styles.service_sec}`}>
          <div className={` ${comon.wrap} ${comon.pt_100}  ${comon.pb_130} `}>
            <div className={styles.awards_top}>
              {props?.AdvisorySlugData[0]?.acf?.services_section_title && (
                <h2 className={``} data-aos="fade-up" data-aos-duration="1000">
                  {props?.AdvisorySlugData[0]?.acf?.services_section_title &&
                    parse(
                      props?.AdvisorySlugData[0]?.acf?.services_section_title
                    )}
                </h2>
              )}
              {/* <div className={`swiper_custom_arws`}>
                <div className={` swiper_service_button_prev`}>
                  <Image
                    src="/images/left_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
                <div className={` swiper_service_button_next`}>
                  <Image
                    src="/images/right_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
              </div> */}
            </div> 

            {/* <Swiper
              slidesPerView={
                props?.AdvisorySlugData[0]?.acf?.services_listing?.length < 5
                  ? props?.AdvisorySlugData[0]?.acf?.services_listing?.length
                  : 5
              }
              spaceBetween={30}
              dir={language == "ar" ? "rtl" : "ltr"}
              key={language}
              className={`${styles.service_slider} service_slider`}
              navigation={{
                nextEl: ".swiper_service_button_next",
                prevEl: ".swiper_service_button_prev",
              }}
              modules={[Navigation]}
              loop={true}
              breakpoints={{
                0: {
                  slidesPerView: Math.min(
                    props?.AdvisorySlugData[0]?.acf?.services_listing?.length ||
                      2,
                    2
                  ),
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: Math.min(
                    props?.AdvisorySlugData[0]?.acf?.services_listing?.length ||
                      4,
                    4
                  ),
                  spaceBetween: 30,
                },
                1200: {
                  slidesPerView: Math.min(
                    props?.AdvisorySlugData[0]?.acf?.services_listing?.length ||
                      5,
                    5
                  ),
                  spaceBetween: 30,
                },
              }}
            >
              {props?.AdvisorySlugData[0]?.acf?.services_listing &&
                props?.AdvisorySlugData[0]?.acf?.services_listing.map(
                  (service, index) => (
                    <SwiperSlide key={index}>
                      <div className={`${styles.service_set} service_set`}>
                        <div className={`${styles.serv_title} serv_title`}>
                          {service?.services_title && (
                            <h5 data-aos="fade-up" data-aos-duration="1000">
                              {service?.services_title &&
                                parse(service?.services_title)}
                            </h5>
                          )}
                          {service?.services_content && (
                            <p data-aos="fade-up" data-aos-duration="1000">
                              {service?.services_content &&
                                parse(service?.services_content)}
                            </p>
                          )}
                        </div>
                        {service?.services_icon && (
                          <div className={`${styles.serv_icon} serv_icon`}>
                            <div className={styles.icon_inner}>
                              <Image
                                src={service?.services_icon?.url}
                                width={90}
                                height={105}
                                alt="image"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </SwiperSlide>
                  )
                )}
            </Swiper> */}

            {/* New List after client update on 18-03-25 */}

            <ul className={styles.service_listing}>
              {props?.AdvisorySlugData[0]?.acf?.services_listing &&
                props?.AdvisorySlugData[0]?.acf?.services_listing.map((service, index) => (
                  <li data-aos="fade-up" data-aos-duration="1000">
                    <Image src="/images/diamod_shape_service.svg" width={33} height={53} alt="shape" />
                    <div className={styles.service_inside}>
                    <h5 >
                      {service?.services_title &&
                        parse(service?.services_title)}
                    </h5>
                    </div>
                  </li>
                )
                )}
            </ul>
          </div>
        </section>
      ) : null}

      {props &&
      props?.AdvisorySlugData &&
      props?.AdvisorySlugData[0]?.acf &&
      (props?.AdvisorySlugData[0]?.acf?.related_digital_solutions_title ||
        props?.AdvisorySlugData[0]?.acf?.related_digital_solutions) ? (
        <section className={styles.digital_solution_sec}>
          <div className={`${comon.wrap}`}>
            {props?.AdvisorySlugData[0]?.acf
              ?.related_digital_solutions_title && (
              <h2 className={``} data-aos="fade-up" data-aos-duration="1000">
                {props?.AdvisorySlugData[0]?.acf
                  ?.related_digital_solutions_title &&
                  parse(
                    props?.AdvisorySlugData[0]?.acf
                      ?.related_digital_solutions_title
                  )}
              </h2>
            )}
            <div className={styles.solution_sets}>
              {props?.AdvisorySlugData[0]?.acf?.related_digital_solutions &&
                props?.AdvisorySlugData[0]?.acf?.related_digital_solutions.map(
                  (digiSol, dindex) => (
                    <div
                      className={`${styles.solution_set} ${comon.solution_set}`}
                      key={dindex}
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      {digiSol.digital_solutions_title && (
                        <h3>
                          {digiSol.digital_solutions_title &&
                            parse(digiSol.digital_solutions_title)}
                        </h3>
                      )}
                      {digiSol.digital_solutions_content &&
                        parse(digiSol.digital_solutions_content)}
                    </div>
                  )
                )}
            </div>
          </div>
        </section>
      ) : null}
      {props &&
      props?.AdvisorySlugData &&
      props?.AdvisorySlugData[0]?.acf &&
      props?.AdvisorySlugData[0]?.acf?.experts_panel_hide === "show" &&
      (props?.AdvisorySlugData[0]?.acf?.experts_panel_title ||
        props?.AdvisorySlugData[0]?.acf?.experts_panel) ? (
        <section className={styles.expert_panel_sec}>
          <div className={`${comon.wrap}`}>
            <div className={styles.expert_top}>
              {props?.AdvisorySlugData[0]?.acf?.experts_panel_title && (
                <h2 className={``} data-aos="fade-up" data-aos-duration="1000">
                  {props?.AdvisorySlugData[0]?.acf?.experts_panel_title &&
                    parse(props?.AdvisorySlugData[0]?.acf?.experts_panel_title)}
                </h2>
              )}
              <div className={`swiper_custom_arws`}>
                <div className={`swiper_button_prev `}>
                  <Image
                    src="/images/left_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
                <div className={`swiper_button_next`}>
                  <Image
                    src="/images/right_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
              </div>
            </div>
            <Swiper
              slidesPerView={5}
              spaceBetween={30}
              speed={1000}
              loop={true}
              navigation={{
                nextEl: ".swiper_button_next",
                prevEl: ".swiper_button_prev",
              }}
              modules={[Navigation, Autoplay]}
              autoplay={{
                delay: 1000,
                disableOnInteraction: false,
              }}
              dir={language === "ar" ? "rtl" : "ltr"}
              key={language}
              className={`${styles.expert_slider} expert_slider`}
              breakpoints={{
                0: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                },
                650: {
                  slidesPerView: 2,
                  spaceBetween: 30,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 30,
                },
                1200: {
                  slidesPerView: 5,
                  spaceBetween: 30,
                },
              }}
            >
              {props?.AdvisorySlugData[0]?.acf?.experts_panel &&
                props?.AdvisorySlugData[0]?.acf?.experts_panel.map(
                  (expanel, eindex) => (
                    <SwiperSlide key={eindex}>
                      <ExpertPanel
                        image={expanel?.experts_panel_image?.url}
                        name={expanel?.experts_panel_title}
                      />
                    </SwiperSlide>
                  )
                )}
            </Swiper>
          </div>
        </section>
      ) : null}

      {props &&
      props?.AdvisorySlugData &&
      props?.AdvisorySlugData[0]?.acf &&
      props?.InsightsPostsData?.length > 0 &&
      (props?.AdvisorySlugData[0]?.acf?.related_insights_title ||
        props?.AdvisorySlugData[0]?.acf?.related_insights) ? (
        <section className={styles.related_inisights}>
          <div className={`${comon.wrap}`}>
            <div className={styles.expert_top}>
              {props?.AdvisorySlugData[0]?.acf?.related_insights_title && (
                <h2 className={``} data-aos="fade-up" data-aos-duration="1000">
                  {props?.AdvisorySlugData[0]?.acf?.related_insights_title &&
                    parse(
                      props?.AdvisorySlugData[0]?.acf?.related_insights_title
                    )}
                </h2>
              )}
              <div className={`swiper_custom_arws`}>
                <div className={`swiper_button_prev_case_study`}>
                  <Image
                    src="/images/left_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
                <div className={`swiper_button_next_case_study`}>
                  <Image
                    src="/images/right_arw.png"
                    alt="image"
                    width={19}
                    height={17}
                    priority
                  />
                </div>
              </div>
            </div>
            <Swiper
              slidesPerView={4}
              spaceBetween={10}
              speed={1000}
              loop={true}
              navigation={{
                nextEl: ".swiper_button_next_case_study",
                prevEl: ".swiper_button_prev_case_study",
              }}
              modules={[Navigation, Autoplay]}
              autoplay={{
                delay: 1000,
                disableOnInteraction: false,
              }}
              dir={language === "ar" ? "rtl" : "ltr"}
              key={language}
              className={`${styles.news_slider} news_slider`}
              breakpoints={{
                0: {
                  slidesPerView: 1,
                  spaceBetween: 10,
                },
                600: {
                  slidesPerView: 2,
                  spaceBetween: 10,
                },
                1200: {
                  slidesPerView: 4,
                  spaceBetween: 15,
                },
              }}
            >
              {props?.InsightsPostsData &&
                props?.InsightsPostsData.map((rlns, reindex) => (
                  <SwiperSlide key={reindex}>
                    <RelatedNews
                      pic={rlns?.acf?.listing_image?.url}
                      tag={rlns?.acf?.listing_tag}
                      title={rlns?.title?.rendered}
                      link={
                        rlns.slug
                          ? `/insights/${rlns.catslug[0]}/${rlns.slug}`
                          : "#"
                      }
                    />
                  </SwiperSlide>
                ))}
            </Swiper>
          </div>
        </section>
      ) : null}
      {props &&
      props?.AdvisorySlugData &&
      props?.AdvisorySlugData[0]?.acf &&
      props?.AdvisorySlugData[0]?.acf?.related_advisory &&
      props?.AdvisorySlugData[0]?.acf?.related_advisory_title ? (
        <section className={`${styles.community_sec} ${comon.community_sec}`}>
          <div className={`${comon.wrap}`}>
            {props?.AdvisorySlugData[0]?.acf?.related_advisory_title && (
              <h2 className={``} data-aos="fade-up" data-aos-duration="1000">
                {props?.AdvisorySlugData[0]?.acf?.related_advisory_title &&
                  parse(
                    props?.AdvisorySlugData[0]?.acf?.related_advisory_title
                  )}
              </h2>
            )}
            <div className={styles.community_program}>
              {props?.AdvisoryPostdataArray &&
                props?.AdvisoryPostdataArray.map((ep, epindex) => (
                  <div
                    className={styles.community_box}
                    key={epindex}
                    data-aos="fade-up"
                    data-aos-duration="1000"
                  >
                    <EngagmentProgram
                      icon={ep?.acf?.related_advisory_listing_icons?.url}
                      link={`/advisory/${taxonamySlug}/${ep?.slug}`} // Dynamically build the full path
                      title={ep?.title?.rendered}
                    />
                  </div>
                ))}
            </div>
          </div>
        </section>
      ) : null}

      <InquireNow
        formtitle={props?.AdvisorySlugData[0]?.title?.rendered}
        optionaltitle={props?.AdvisorySlugData[0]?.acf?.inq_title}
        optionalcontent={props?.AdvisorySlugData[0]?.acf?.inq_content}
      />
    </div>
  );
};

export default publicEngagementPage;

export async function getStaticPaths() {
  // Fetch all advisory taxonomies (categories) and their associated posts
  const ProductlistData = await getAdvisorytaxList(); // Fetch taxonomy data
  const AdvisoryPosts = await getAdvisoryPosts(); // Fetch all advisory posts

  // Generate paths for all taxonomies and their associated posts
  const paths = AdvisoryPosts.flatMap((post) => {
    return post.advisory_category.map((categoryId) => {
      const taxonomy = ProductlistData.find((tax) => tax.id === categoryId);
      if (taxonomy) {
        return {
          params: {
            taxonamySlug: taxonomy.slug, // Taxonomy slug
            slug: post.slug, // Post slug
          },
        };
      }
      return null; // Skip if taxonomy is not found
    });
  }).filter(Boolean); // Remove null entries

  //   console.log("Generated Paths:", paths);

  return {
    paths,
    fallback: "blocking", // Allow SSR for paths not generated at build time
  };
}

export async function getStaticProps({ params, locale }) {
  const taxonomySlug = params.taxonamySlug; // Taxonomy slug from the URL
  const slug = params.slug;
  //console.log("Generated Paths:", slug);

  const AdvisoryPostArray = await getAdvisoryPostSlugs(slug, locale);

  const query = "";
  const InsightsPostsData = await getInsightsPosts(locale, query);
  const InsightstaxonamyList = await getInsightsRelatedTaxonamy(locale); // Fetch taxonomy data

  let InsightsPosts = [];
  if (
    AdvisoryPostArray[0] &&
    AdvisoryPostArray[0]?.acf &&
    Array.isArray(AdvisoryPostArray[0]?.acf?.related_insights)
  ) {
    InsightsPosts = AdvisoryPostArray[0]?.acf?.related_insights;
  }

  // Format Investors Resources  for use in the component
  let InsightsPostsArray = [];
  if (InsightsPosts.length > 0) {
    InsightsPostsArray = InsightsPosts.map((id) =>
      InsightsPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  // Add `catslug` to each post in InsightsPostsRelation
  InsightsPostsArray = InsightsPostsArray.map((post) => {
    const categorySlugs = post.categories
      ?.map((catId) => {
        const matchingCategory = InsightstaxonamyList.find(
          (taxonomy) => taxonomy.id === catId
        );
        return matchingCategory?.slug; // Return the slug if a match is found
      })
      .filter(Boolean); // Remove undefined values

    return {
      ...post,
      catslug: categorySlugs, // Add category slugs to the post
    };
  });

  const AdvisoryPostsData = await getAdvisoryPosts(locale, query);

  let AdvisoryPosts = [];
  if (
    AdvisoryPostArray[0] &&
    AdvisoryPostArray[0]?.acf &&
    Array.isArray(AdvisoryPostArray[0]?.acf?.related_advisory)
  ) {
    AdvisoryPosts = AdvisoryPostArray[0]?.acf?.related_advisory;
  }

  // Format Investors Resources  for use in the component
  let AdvisoryPostdataArray = [];
  if (AdvisoryPosts.length > 0) {
    AdvisoryPostdataArray = AdvisoryPosts.map((id) =>
      AdvisoryPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  return {
    props: {
      AdvisorySlugData: AdvisoryPostArray || [],
      InsightsPostsData: InsightsPostsArray || [],
      AdvisoryPostdataArray: AdvisoryPostdataArray || [],
    },
    revalidate: 10,
  };
}
