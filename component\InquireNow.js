import React, { useEffect, useRef, useState } from "react";
import Link from "next/link"; // Import Link for navigation
import But<PERSON> from "@/component/buttion/Buttion"; // Import custom Button component
import comon from "@/styles/comon.module.scss"; // Import common styles
import inquire from "@/styles/inquireNow.module.scss"; // Import specific styles for inquire section
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import { useRouter } from "next/router"; // Import useRouter hook for routing
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import Image from "next/image";

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;
const InquireNow = ({formtitle,optionaltitle,optionalcontent}) => {
  // Remove opacity class after image loads
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };
  const router = useRouter();

  const [crycform, setOptions] = useState(null); 

  useEffect(() => {
     
     const fetchMyAcfOptions = async (locale) => {
      try {
        const footerPostsData = await getThemeoptions(locale);
       // console.log("Fetched options:", locale);
        setOptions(footerPostsData);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };
    fetchMyAcfOptions(router.locale);
    
      const restricSpace = document.querySelectorAll('input[type="tel"], input[type="text"], input[type="email"]');
        restricSpace.forEach(function(input) {
            input.addEventListener('keypress', function(e) {
                if (e.which === 32 && !this.value.length) {
                    e.preventDefault();
                }
            });
        });
   
      const restricSymbols = document.querySelectorAll('#organization-name, #first-name, #last-name');
        restricSymbols.forEach(function(input) {
            input.setAttribute("onkeydown", "return /[a-zA-Z ]/.test(event.key)");
        });
    
      // Number only script using plain JavaScript
    const phoneInputs = document.querySelectorAll('input[id="phone"], input[id="mobile"]');
    phoneInputs.forEach(function (input) {
      input.addEventListener('keypress', function (e) {
        const key = e.key;
        if (!/^\d$/.test(key) && key !== 'Backspace' && key !== 'Delete') {
          e.preventDefault();
        }
      });

      input.addEventListener('input', function (e) {
        input.value = input.value.replace(/[^\d]/g, '');
      });
    });
    // Number only end
  }, [router]);

  /* Submitting Contact form */
  const [formTouched, setFormTouched] = useState(false);
  const [formValid, setFormValidation] = useState(false);
  const [formSent, setFormSent] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formError, setFormError] = useState("");
  const [selectedService, setSelectedService] = useState("");
  const [serviceError, setServiceError] = useState("");
  

  const validateService = () => {
  if (!selectedService) {
    // setServiceError("Please select a service.");
    setServiceError(router.locale === "ar" ? `الرجاء اختيار الخدمة.` : `Please select a service.`);
    return false;
  }
  setServiceError("");
  return true;
};

// Update the fieldChangeHandler for the service dropdown
const serviceChangeHandler = (e) => {
  const value = e.target.value;
  setSelectedService(value);
  if (value) {
    setServiceError("");
  }
  };
  
  const [validationMessages, setValidationMessages] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
  });

  const firstnameInputRef = useRef();
  const lastnameInputRef = useRef();
  const emailIdInputRef = useRef();
  const phoneNumberInputRef = useRef();
  const messageInputRef = useRef();

  const validateField = (name, value, label) => {
   // console.log(name)
    let message = "";
    if (!value) {
      //message = `${label} is required.`;
       message = router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
    } else if (name === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        message = router.locale === "ar" ? `تنسيق البريد الإلكتروني غير صالح.` : `Invalid email format.`;
      }
    } else if (name === "phone") {
      if (!value || value.replace(/\D/g, "").length <= 2) {
        message = router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
      }
    }    
    return message;
  };

  const contactFormHandler = (e) => {
    e.preventDefault();

    const firstNameMessage = validateField("firstName", firstnameInputRef.current.value, router.locale === "ar" ? `الاسم الكامل` : `First Name`);
    const lastNameMessage = validateField("lastName", lastnameInputRef.current.value, router.locale === "ar" ? `الاسم العائلة` : `Last Name`);
    const emailMessage = validateField("email", emailIdInputRef.current.value, router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`);
    const phoneMessage = validateField("phone", phoneNumberInputRef.current.value, router.locale === "ar" ? `رقم الهاتف` : `Phone Number`);
    const isServiceValid = validateService();

    setValidationMessages({
      firstName: firstNameMessage,
      lastName: lastNameMessage,
      email: emailMessage,
      phone: phoneMessage,     
    });

    const isValid = !firstNameMessage && !lastNameMessage && !emailMessage && !phoneMessage && isServiceValid;
    setFormValidation(isValid);

    if (isValid) {
      let formData = new FormData();
      formData.append("first-name", firstnameInputRef.current.value);
      formData.append("last-name", lastnameInputRef.current.value);
      formData.append("your-email", emailIdInputRef.current.value);
      formData.append("your-phone", phoneNumberInputRef.current.value);
      formData.append("your-service", selectedService); // Include the selected service
      formData.append("your-message", messageInputRef.current.value);
      formData.append("_wpcf7_unit_tag", "123");

      setFormSent(true);

      fetch(
        `${baseURL}wp-json/contact-form-7/v1/contact-forms/1306/feedback`,
        {
          method: "POST",
          body: formData,
          redirect: "follow",
        }
      )
        .then((response) => response.json())
        .then((data) => {
          if (data.status === "mail_sent") {
            document.getElementById("contact-form").reset();
            // Reset the values of the input fields directly
            firstnameInputRef.current.value = "";
            lastnameInputRef.current.value = "";
            emailIdInputRef.current.value = "";
            phoneNumberInputRef.current.value = "";
            messageInputRef.current.value = ""; 
            setSelectedService("");


            setFormSuccess(data.message);
            // Hide the success message after 5 seconds
            setTimeout(() => {
              setFormSuccess("");
            }, 5000);
          } else {
            setFormError(data.message);
          }
        });
    } else {
      setFormError(
        router.locale === "ar"
          ? `يوجد خطأ في حقل واحد أو أكثر. يرجى المراجعة والمحاولة مرة أخرى` 
          : `One or more fields have an error. Please check and try again.`
      );
      setFormTouched(true);
    }
  };

 const fieldChangeHandler = (label) => (e) => { 
   const message = validateField(e.target.name, e.target.value,label);
    setValidationMessages((prev) => ({
      ...prev,
      [e.target.name]: message
    }));

    if (message) {
      e.target.classList.add("form-invalid");
      e.target.classList.remove("form-valid");
      setFormValidation(false);
    } else {
      e.target.classList.remove("form-invalid");
      e.target.classList.add("form-valid");
      setFormValidation(true);
    }
  };

   if (!crycform) {
    return null;
  }


  return (
    <>
  
      {/* Inquire Now Section */}
      <section
        // id="inquire"
        className={`${comon.pt_100} ${comon.text_white} ${comon.pb_100} inquire`} // Padding classes for top and bottom
        style={{
          background: "url(../images/inquire_bg.png) #5E45FF  center right no-repeat",
          backgroundSize: "contain"
        }}
      >
        <div className={`${comon.wrap}`}> {/* Common wrapper class */}
          {/* Heading */}
          {optionaltitle &&  optionaltitle ? (           
            <h2 data-aos="fade-up" data-aos-duration="1000" className={`${comon.h2} ${comon.mb_20}`}>
              {optionaltitle && parse(optionaltitle)}
            </h2>            
          ): (
            crycform?.ing_form_title && (
                <h2 data-aos="fade-up" data-aos-duration="1000" className={`${comon.h2} ${comon.mb_20}`}>
                  {crycform?.ing_form_title && parse(crycform?.ing_form_title)}
                </h2>
          )
          )}
          {optionalcontent &&  optionalcontent ? (           
               <div data-aos="fade-up" data-aos-duration="1000">
              <p>{optionalcontent && parse(optionalcontent)}</p>
            </div>    
          ): (
            crycform?.ing_form_content && (
            <div data-aos="fade-up" data-aos-duration="1000">
              <p>{crycform?.ing_form_content && parse(crycform?.ing_form_content)}</p>
            </div>
          )
          )}
          
          
          <form action="" className={`${inquire.form_contact} form_contact`} onSubmit={contactFormHandler} id="contact-form">
        <ul className={`${inquire.form_ul} ${comon.mt_20}  inq_form`}>
          <li className={`${inquire.form_Cl_01}`}> {/* First column of the form */}
         <ul className={`${inquire.form_cl_set_2}`}> {/* Sublist for form fields */}
          <li data-aos="fade-up" data-aos-duration="1000">
            <input
              type="text"
              name="firstName"
              id="first-name"
              placeholder={crycform?.in_full_name && crycform?.in_full_name}
              ref={firstnameInputRef}
              onChange={fieldChangeHandler(router.locale === "ar" ? `الاسم الكامل` : `First Name`)}
              className={`${comon.fld_01}`}
            />
            
            {validationMessages.firstName && <span className="form-error">{validationMessages.firstName}</span>}
          </li>
          <li data-aos="fade-up" data-aos-duration="1000" className={`${comon.d_none}`}>
            <input
              type="text"
              name="lastName"
              id="last-name"
              placeholder={crycform?.last_name && crycform?.last_name}
              ref={lastnameInputRef}
              onChange={fieldChangeHandler(router.locale === "ar" ? `الاسم العائلة` : `Last Name`)}
              className={`${comon.fld_01}`}
              value={formtitle}
            />            
            {validationMessages.lastName && <span className="form-error">{validationMessages.lastName}</span>}
          </li>
         <li data-aos="fade-up" data-aos-duration="1000">
            <input
              type="email"
              name="email"
              id="email-id"
              placeholder={crycform?.in_email && crycform?.in_email}
              ref={emailIdInputRef}
              onChange={fieldChangeHandler(router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`)}
              className={`${comon.fld_01}`}
            />            
            {validationMessages.email && <span className="form-error">{validationMessages.email}</span>}
          </li>
         <li data-aos="fade-up" data-aos-duration="1000">
            <input
              type="text"
              name="phone"
              id="phone"              
              maxLength="15"
              placeholder={crycform?.in_phone_number && crycform?.in_phone_number}
              ref={phoneNumberInputRef}
              onChange={fieldChangeHandler(router.locale === "ar" ? `رقم الهاتف` : `Phone Number`)}
              className={`${comon.fld_01}`}
            />            
            {validationMessages.phone && <span className="form-error">{validationMessages.phone}</span>}
                </li>
                  {/* Service Required Dropdown */}               
                    <li data-aos="fade-up" data-aos-duration="1000">
                    <select
                      className={`${comon.fld_01}`}
                      value={selectedService}
                      onChange={serviceChangeHandler}
                       //name="Service"
                      // id="service_list" 
                    >
                      <option value="" disabled>
                          {crycform?.in_servicelist_label}
                      </option>
                      {crycform?.in_servicelist && crycform?.in_servicelist.map((data, index) => (
                        <option key={index} value={data.service_required}>
                          {data.service_required}
                        </option>
                      ))}
                    </select>
                    {serviceError && <span className="form-error">{serviceError}</span>}
                </li>
                  </ul>
            </li>
            <li className={`${inquire.form_Cl_02}`} data-aos="fade-up" data-aos-duration="1000">
                <textarea
                name="Message"
                id="message"
                placeholder={crycform?.in_message && crycform?.in_message }
                className={`${comon.fld_01}`}          
                ref={messageInputRef}
                onChange={fieldChangeHandler}
                />            
              </li>
            {/* <li data-aos="fade-up" data-aos-duration="1000">
                <input
                type="submit"
                className={`${comon.al_btn_link}  `}
                data-aos="fade-up"
                value={crycform?.in_submit}                
                />
            </li>  */}
            </ul>

            <button className={`${comon.contact_form_input_submit_btn} ${inquire.inq_btn}`}>
                {crycform?.in_submit}{" "}
                <div className={`${comon.contact_form_btn_icon} ${inquire.contact_form_btn_icon}`}>
                    <Image
                        src={"/images/buttion_arrow_white.svg"}
                        height={12}
                        width={12}
                        alt=""
                    />
                </div>
            </button>
             {/* <Buttion
                aosType="fade-up"
                aosDuration={1500}
                type="submit"
                text="Submit" // Button text
                href="/page1-details" // Navigation target for the button
                moduleClass={buttion.strock} // Custom button styling
                imageSrc="/images/buttion_arrow.svg" // Image for button
              /> */}
       
        {formSuccess && formValid === true && <div className="msg_success"><span className="form-success">{formSuccess}</span></div>}
        {formError && formValid === false && <div className="msg_error"><span className="form-error">{formError}</span></div>}       
      </form>
         
        </div>
      </section>
    </>
  );
};

export default InquireNow;
