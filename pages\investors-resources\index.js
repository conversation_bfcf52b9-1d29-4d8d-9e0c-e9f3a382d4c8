import React, { useEffect, useState } from "react";
import style from "@/styles/InvestorsResources.module.scss";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import InvestorsResourcesCard from "@/component/InvestorsReportCard";
import TabSection from "@/component/Tab-section";

import Yoast from "@/component/yoast";
import {
    getInvestorsResources,    
    getIrMenuLinks,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";


const InvestorResources = (props) => {

  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);
    
    
  const yoastData = props?.InvestorsResData?.yoast_head_json;

  if (!props.InvestorsResData) {
    return null;
  }
  

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
        props?.InvestorsResData &&
        props?.InvestorsResData?.acf &&
        props?.InvestorsResData?.acf?.banner_title &&
        (props?.InvestorsResData?.acf?.mob_banner_image ||
          props?.InvestorsResData?.acf?.banner_image ||
          props?.InvestorsResData?.acf?.breadcrumbs ||
          props?.InvestorsResData?.acf?.banner_viedo ||
          props?.InvestorsResData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.InvestorsResData?.acf?.banner_title}
          breadcrumb1={props?.InvestorsResData?.acf?.active_breadcrumbs === 'yes' ? props?.InvestorsResData?.acf?.breadcrumbs : ''}
          background={`${isMobile ? props?.InvestorsResData?.acf?.mob_banner_image?.url : props?.InvestorsResData?.acf?.banner_image?.url}`}
          videoSrc={props?.InvestorsResData?.acf?.banner_viedo?.url}
          banner_type={props?.InvestorsResData?.acf?.banner_type}

        />
      ) : null
      }
      
     


      {/* =========Tab Section====== */}
      {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.investors_relations_menu &&
        <TabSection tabs={props?.IRMenuData?.investors_relations_menu} />
      }
      {/* -----------Company Announcement section----------- */}

      {props?.InvestorsResData?.acf?.section_title &&
        <section
          className={`${style.investor_resources_company_announcement_section}`}
        >
          <div
            className={`${style.investor_resources_company_announcement_container} ${comon.wrap} ${comon.pt_100}  `}
          >
            <h3>{props?.InvestorsResData?.acf?.section_title && parse(props?.InvestorsResData?.acf?.section_title)}</h3>
          </div>
          <div className={style.inr_graphics}>
            <Image src='/images/ir_graphic.png' width={292} height={511} crossOrigin="anonymous"/>
          </div>
        </section>
      }

      {/* =======Annual Card Blocks======= */}
      {props.InvestorsResData &&
        props?.InvestorsResData?.acf?.investor_resources_tax &&(
        <section className={`${style.investor_resources_card_block_section}  ${comon.pt_80}  `}>
      <div className={`${style.investor_resources_card_block_section_container} ${comon.wrap}  `} >             
        <ul className={`${style.investor_resources_company_announcement_card_sec}  ${comon.pt_30}  ${comon.pb_100}`} >
          <InvestorsResourcesCard cardData={props?.InvestorsResData?.acf?.investor_resources_tax} />   
        </ul>
                 
      </div>
    </section>
  )
}
    </div>
  );
};

export default InvestorResources;



export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const InvestorsResData = await getInvestorsResources(locale);
  const IRMenuData = await getIrMenuLinks(locale);  
  //  console.log('testingdata', InvestorsResData)
  
  return {
    props: {
      InvestorsResData: InvestorsResData || null,
      IRMenuData: IRMenuData || null,  
    },
    revalidate: 10,
  };
};