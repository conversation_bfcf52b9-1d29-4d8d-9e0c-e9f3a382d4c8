import React, { useState, useRef } from "react";
import <PERSON> from "next/link"; // Import Link for navigation
import But<PERSON> from "@/component/buttion/Buttion"; // Import custom Button component
import comon from "@/styles/comon.module.scss"; // Import common styles
import video from "@/styles/video.module.scss"; // Import specific styles for inquire section
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import { useRouter } from "next/router"; // Import useRouter hook for routing
import Image from "next/image";

const VideoBlock = ({videos,videoPoster}) => {
  // Remove opacity class after image loads
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };
  const [isMuted, setIsMuted] = useState(true); // State to track mute status
  const videoRef = useRef(null); // Reference to the video element

  // Toggle mute state
  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  return (
    <>
      <section
        className={`${video.video_block}`}
        style={{
          background: `url(${videoPoster}) top left no-repeat`,
          backgroundSize: "cover",
          backgroundPosition: "right center",
        }}
      >
        {/* <span
          className={`${video.video_play_but} ${comon.trans}`}
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <Image
            className={`${comon.img_mx_fluid}   transition_opacity opacity-0`}
            onLoad={handleLoad}
            src="/images/play.svg"
            alt="call"
            width={32}
            height={36}
          />
        </span> */}
        <video
         ref={videoRef}
          className={video.videoPlayer}
          src={videos} // Path to the video file in the public folder
          autoPlay        // Auto play on load
          loop            // Loop the video
          muted={isMuted}        // Mute the video by default
          playsInline     // Plays inline on mobile (avoids full-screen autoplay)
        />
        <div className={video.mute_btn} onClick={toggleMute}>
        {isMuted ? (
          <Image src="/images/volume-slash.svg" width={24} height={24} alt="Mute" />
        ) : (
          <Image src="/images/volume-high.svg" width={24} height={24} alt="Unmute" />
        )}
        </div>
      </section>
    </>
  );
};

export default VideoBlock;
