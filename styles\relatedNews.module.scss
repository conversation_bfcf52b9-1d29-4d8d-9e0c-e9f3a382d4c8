@import "variable", "base";

.related_news {
    display: block;
    width: 100%;
    position: relative;

    &::after {
        content: '';
        position: absolute;
        border-radius: 10px 10px 0 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 30%;
        background: linear-gradient(0deg, transparent, rgba(0, 0, 0, .7));
        z-index: 1;

    }

    .news_pic {
        transition: all .3s ease-in-out;
        overflow: hidden;
        border-radius: 10px;
    }

    img {
        border-radius: 10px;
        width: 100%;
        height: 450px;
        object-fit: cover;
        transition: all .3s ease-in-out;

        @media #{$media-1600} {
            height: 400px;
        }
    
        @media #{$media-1024} {
            height: 430px;
        }
    }

    .tag {
        font-size: 0.938rem;
        color: #fff;
        text-transform: uppercase;
        margin-bottom: 20px;
        display: block;

        @media #{$media-1600} {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        @media #{$media-767} {
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

    }

    h5 {
        font-size: 1.563rem;
        line-height: 1.9rem;
        color: #fff;
        font-weight: 300;
        font-family: var(--helveticaneuelt_arabic_55);

        @media #{$media-1600} {
            font-size: 1.9rem;
            line-height: 2.5rem;
        }

            @media #{$media-767} {
                font-size: 2.3rem;
                line-height: 2.8rem;
            }
        }

        .news_title {
            position: absolute;
            inset-inline-start: 0;
            top: 60px;
            padding: 0 60px;
            z-index: 2;
            @media #{$media-1600} {
                top: 40px;
                padding: 0 40px;
            }

            @media #{$media-1024} {
                top: 50px;
                padding: 0 25px;
            }
            @media #{$media-767} {
                top: 30px;
                padding: 0 25px;
            }
        }

        &:hover {
            img {
                transform: scale(1.1);
            }
        }
    }