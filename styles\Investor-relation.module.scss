@import "variable", "base";

.investor_relation_card_block_section {
    background-color: #f5f3ff;

    .investor_relation_card_block_section_container {
        width: 100%;

        .investor_relation_card_block_head_sec {
            display: flex;
            justify-content: space-between;
            @media #{$media-767} {
                flex-wrap: wrap;
                row-gap: 15px;
            }
            h4 {
                display: inline-block;

                color: #000000;
                font-size: 50px;
                font-weight: 400;

                @media #{$media-950} {
                    font-size: 2.9rem;
                }
            }
        }

        .investor_relation_company_announcement_card_sec {
            display: flex;
            flex-wrap: wrap;
            // gap: 2%;
            margin: 0 -1%;
            row-gap: 25px;
            position: relative;

            li {
                margin: 0 1%;
            }
        }
    }
}

// =======================
.investor_relation_media_resource_section {
    width: 100%;
    background-color: white;

    .investor_relation_media_resource_container {
        width: 100%;
        @media #{$media-1024} {
            padding-bottom: 100px;
        }
       

        .investor_relation_media_resource_head_sec {
            display: flex;
            justify-content: space-between;

            h4 {
                display: inline-block;

                color: #000000;
                font-size: 50px;
                font-weight: 400;

                @media #{$media-950} {
                    font-size: 2.9rem;
                }
            }
        }

        .investor_relation_media_resource_card_swiper_sec {
            width: 100%;
            padding-top: 40px;

            position: relative;

            .investor_relation_media_resource_card_swiper_prev_btn,
            .investor_relation_media_resource_card_swiper_next_btn {
                position: absolute;
                display: inline-flex;
                margin: 0;

                img {
                    width: 100%;
                    @media #{$media-767} {
                        width: 15px;
                        height: 15px;
                    }
                }

                &:hover {
                    border: 1px solid rgb(121, 112, 255);
                }

                &:hover img {
                    filter: invert(0) brightness(1.5) !important;
                }
            }

            .investor_relation_media_resource_card_swiper_prev_btn {
                inset-inline-start: -6%;
                @media #{$media-1600} {
                    inset-inline-start: -4%;
                    width: 45px;
                    height: 45px;
                }
                @media #{$media-1440} {
                    inset-inline-start: -22.5px;
                }
                @media #{$media-1024} {
                    inset-inline-start: -55px;
                    inset-inline-end: 0;
                    margin: 0 auto;
                    bottom: -55px;
                    top: auto;
                    padding: 1.2%;
                }
            }

            .investor_relation_media_resource_card_swiper_next_btn {
                inset-inline-end: -6%;
                @media #{$media-1600} {
                    inset-inline-end: -4%;
                    width: 45px;
                    height: 45px;
                }
                @media #{$media-1440} {
                    inset-inline-end: -22.5px;
                }
                @media #{$media-1024} {
                    inset-inline-start: 0;
                    inset-inline-end: -55px;
                    margin: 0 auto;
                    bottom: -55px;
                    top: auto;
                    padding: 1.2%;
                }
            }

            .investor_relation_media_resources_card_list_img {
                width: 100%;
                position: relative;
                height: 300px;
                border: 1px solid #dddddd;
                border-radius: 15px;
                overflow: hidden;
                &:hover{
                    img:not(.investor_relation_media_resources_card_list_img_play_btn img){
                        scale: (1.1);
                    }
                }

                img {
                    height: 100%;
                    max-width: 100%;
                    display: block;
                    object-fit: cover;
                    transition: 0.3s all ease;
                }

                .investor_relation_media_resources_card_list_img_play_btn {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);

                    img {
                        width: 70px;
                        height: 70px;

                       

                        @media #{$media-1400} {
                            width: 60px;
                            height: 60px;
                        }

                        @media #{$media-950} {
                            width: 70px;
                            height: 70px;
                        }
                    }
                    
                }

                .investor_relation_media_resources_card_list_data {
                    width: 100%;
                    left: 0%;
                    bottom: 20px;
                    box-sizing: border-box;

                    padding-left: 10%;
                    padding-right: 5%;
                    position: absolute;
                    @media #{$media-1024} {
                        bottom: 20px;
                        padding-left: 5%;
                    }

                    h5 {
                        color: #000000;
                        font-size: 25px;
                        line-height: 28px;
                        font-weight: 400;
                        display: block;
                        @media #{$media-1024} {
                            font-size: 22px;
                            line-height: 24px;
                        }
                    }

                    span {
                        padding-top: 8px;
                        color: #000000;
                        font-size: 15px;
                        line-height: 100%;
                        font-weight: 400;
                        display: block;
                        @media #{$media-1024} {
                            padding-top: 5px;
                        }
                    }
                    .action_text {
                        color: #5e45ff;
                        font-weight: 600;
                        margin-top: 8px;
                        font-size: 15px;
                    
                        @media #{$media-767} {
                            font-size: 15px;
                        }
                    }
                }
            }
        }
    }
}

// ===================

.contact_form_section {
    width: 100%;
    background-color: $blue;
    position: relative;


    .contact_form_container {
        width: 100%;
        display: flex;
        position: relative;
        z-index: 1;
        @media #{$media-767} {
            flex-wrap: wrap;
        }
        .contact_form_left_sec {
            width: 60%;
            padding-right: 3%;
            @media #{$media-1024} {
                 width: 55%;
            }
            @media #{$media-767} {
                width: 100%;
            }
            h3 {
                color: #ffffff;
                font-size: 50px;
                line-height: 68px;
                font-weight: 400;
                font-family: var(--helveticaneuelt_arabic_55);
                 @media #{$media-1024} {
                     font-size: 35px;
                     line-height: 40px;
                 }
                 @media #{$media-767} {
                    font-size: 25px;
                    line-height: 30px;
                }
            }

            p {
                margin-top: 20px;
                color: #ffffff;
                font-size: 16px;
                line-height: 20px;
                font-weight: 400;
                padding-bottom: 40px;
                 @media #{$media-1024} {
                        padding-bottom: 20px;
                }
            }

            .contact_form_contact_sec {
                display: flex;
                gap: 10px;
                align-items: center;
                margin-top: 20px;

                .contact_form_icon {
                    width: 42px;

                    img {
                        display: block;
                        object-fit: cover;
                        height: auto;
                        width: 100%;
                    }
                }

                h5 {
                    display: block;
                    font-family: var(--helveticaneueltarabicLight);

                    color: #ffffff;
                    font-size: 16px;
                    line-height: 150%;
                    font-weight: 400;
                }
            }
        }

        .contact_form_right_sec {
            width: 40%;
            margin: -10px 0;
            position: relative;
            ul{
                width: 100%;
            }
            @media #{$media-1024} {
                 width: 45%;
            }
            @media #{$media-767} {
                width: 100%;
                padding-top: 35px;
            }
            .contact_form_input_li {
                margin: 10px 0;
                display: flex;

                align-items: center;

                .contact_form_input,
                .contact_form_textarea {
                    display: block;
                    width: 100%;
                    box-sizing: border-box;
                    padding: 10px 4%;
                    font-size: 16px;
                    background-color: transparent;
                    border: 1px solid #bfb5ff;
                    border-radius: 5px;
                    color: rgba(255, 255, 255,.8);

                    &::placeholder {
                        color: rgba(255, 255, 255,.8);
                        font-family: var(--segoe-ui);
                    }

                    &:focus {
                        outline: none;
                    }
                }

                .contact_form_textarea {
                    resize: none;
                    max-width: 100%;
                    min-width: 100%;
                    max-height: 150px;
                    min-height: 150px;
                }

                .contact_form_input_submit_btn {
                    padding: 16px 30px;
                    border-radius: 50px;
                    border: 0;
                    background-color: transparent;
                    border:1px solid #ffffff;
                    gap: 10px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 20px;
                    color: #ffffff;
                    font-size: 16px;
                    line-height: 28px;
                    font-weight: 400;
                    transition: 0.3s all ease-in-out;
                    cursor: pointer;
                    &:hover{
                        background-color: #000000;
                        border: 1px solid #000000;
                    }
                    @media #{$media-1600} {
                        padding: 10px 25px;
                    }
                     @media #{$media-1024} {
                        font-size: 15px;
                        line-height: 18px;
                    }

                    .contact_form_btn_icon {
                        width: 12px;

                        img {
                            display: block;
                            object-fit: cover;
                            height: auto;
                            width: 100%;
                        }
                    }
                }
            }

            .contact_form_terms_and_condition {
                margin: 20px 0 10px 0;
                display: flex;
                font-size: 16px;
                align-items: center;
                position: relative;
                color: #fff;
                @media #{$media-1024} {
                     font-size: 14px;
                }
                .contact_form_checkbox {
                    height: 20px;
                    width: 20px;
                    border: 1px solid white !important;
                    background-color: transparent !important;
                    opacity: 0;
                    position: absolute;
                    left: 0;
                    z-index: 1;
                    cursor: pointer;
                }

                span {
                    height: 20px;
                    width: 20px;
                    position: absolute;
                    border: 1px solid white;
                    inset-inline-start: 0;
                    cursor: pointer;



                }

                .contact_form_checkbox:checked+span::after {
                    position: absolute;
                    content: "";
                    height: 20%;
                    width: 50%;
                    border-left: 2px solid white;
                    border-bottom: 2px solid white;
                    top: 10%;
                    inset-inline-start: 40%;
                    transform: rotate(-45deg) translateX(-50%);
                }

                a {
                    text-decoration: underline;
                    margin-inline-start: 7px;
                }
            }
            .agree_label{
                position: relative;
            }
            .form_group input {
                padding: 0;
                height: initial;
                width: initial;
                margin-bottom: 0;
                display: none;
                cursor: pointer;
              }
            .agree_label:before {
                content:'';
                -webkit-appearance: none;
                background-color: transparent;
                border: 1px solid #fff;
                padding: 10px;
                display: inline-block;
                position: relative;
                vertical-align: middle;
                cursor: pointer;
                margin-inline-end: 15px;
              }
              
              .form_group input:checked + label:after {
                content: '';
                display: block;
                position: absolute;
                top: 3px;
                inset-inline-start: 8px;
                width: 5px;
                height: 10px;
                border: solid #fff;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
              }
        }

    }

    .side_image_logo {
        position: absolute;
        inset-inline-end: 0;
        top: 0;
        width:395px;
        z-index: 0;
        @media #{$media-1440} {
            inset-inline-end: -3%;
        }
        @media #{$media-1024} {
            width: 252px;
        }
        img{
            width: 100%;
            height: auto;
        }
    }
}
.video_cols{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    gap: 15px;
    padding: 100px 0 115px 0;
    @media #{$media-1024} {
        padding: 50px 0 50px 0;
    }
    @media #{$media-767} {
        grid-template-columns: repeat(1,1fr);
        row-gap: 20px;
    }
}
.video_col{
    position: relative;
    border-radius: 10px;
    .video_pic{
        position: relative;
        img{
            max-width: 100%;
            height: auto;
        }
    }
    .video_icon{
        position: absolute;
        right: 0;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        margin: 0 auto;
        text-align: center;
    }
    h3{
        color: #000000;
        font-size: 1.25rem;
        font-family: var(--helveticaNeue);
        font-weight: 400;
        text-align: center;
        margin-top: 20px;
        display: block;
         @media #{$media-767} {
            font-size: 1.7rem;
            margin-top: 8px
         }
         @media #{$media-767} {
            font-size: 2rem;
            margin-top: 8px
         }
    }
    .video_pic{
        img{
            border-radius: 10px;
        }
    }
    a{
        
        background-image: url(/images/video_icon.png);
        width: 68px;
        height: 68px;
        display: block;
        background-repeat: no-repeat;
        background-size: 100%;
        @media #{$media-1024} {
            width: 55px;
            height:55px
        }
    }
}
.modal_video_close{
    display: flex;
    justify-content: flex-end;
    img{
        width: 35px;
        height: auto;
    }
}


// -------------------shareinfo----------
.share_info_section {
    h3 {
        color: #2A2656;
        font-size: 2.5rem;
        margin-bottom: 50px;

        @media #{$media-1200} {
            margin-bottom: 40px;
        }

        @media #{$media-767} {
            font-size: 3rem;
            margin-bottom: 30px;
        }
    }

    .share_info_left {

        // padding-inline-end: 20%;
        img {
            max-width: 100%;
            display: block;
            height: auto;
            margin-top: 5px;
        }

        @media #{$media-1024} {
            margin-bottom: 30px;
        }
    }

    .share_info_right {

        h5 {
            color: #413887;
            font-size: 1.563rem;
            font-family: var(--helveticaNeue);
            margin-bottom: 30px;

            @media #{$media-767} {
                font-size: 3rem;
            }
        }

        overflow: auto;

        ::-webkit-scrollbar {
            display: none;
        }
    }

    .share_info_content_sec {
        display: grid;
        grid-template-columns: repeat(2, 1fr);

        @media #{$media-1024} {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .table_sec {
        border: 1px solid #DFDFDF;
        border-radius: 10px;
        padding-inline-start: 20px;
        width: 100%;
        overflow: auto;

        ::-webkit-scrollbar {
            display: none;
        }

    }

    table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 10px;

        td {
            padding: 15px;
            border-bottom: 1px solid #E2E8F0;
            color: #2D3748;
            font-size: 0.938rem;
            font-family: var(--helveticaNeue);
            width: 25%;
            @media #{$media-1600} {
                font-size: 1.1rem;
            }
            @media #{$media-820} {
                font-size: 1.5rem;
            }

            @media #{$media-767} {
                font-size: 14px;
            }

            &:first-child {
                padding-left: 0;
            }

            &:nth-child(2n+2) {
                background-color: #faf9ff;
                padding-left: 30px;
                color: #5E45FF;
                font-size: 1.125rem;
                @media #{$media-1600} {
                    font-size: 1.2rem;
                }
                @media #{$media-820} {
                    font-size: 1.5rem;
                }
            }

        }

        tr {
            &:last-child {
                td {
                    border-bottom: none;
                    border-bottom-right-radius: 10px;
                }
            }

            &:first-child {
                td {
                    border-top-right-radius: 10px;
                }
            }
        }

    }
}

.inner_banner_share_chart_wrap {

    .inner_banner_share_chart {
        padding: 20px 0;
        padding-inline-end: 50px;
        background-color: #ffffff;
        display: inline-flex;
        gap: 20px;
        border-bottom: 1px solid #dddddd;

        .sharechart_img {
            display: block;
            height: auto;
            width: 100%;
            object-fit: cover;
        }

        @media #{$media-1600} {
            padding: 15px 0;
            padding-inline-end: 40px;
        }

        @media #{$media-767} {
            margin-inline-start: auto;
            bottom: 10%;
            display: flex;
        }

        .diasmond {
            img {
                @media #{$media-1600} {
                    width: 20px;
                }

                @media #{$media-500} {
                    width: 15px;
                }
            }

        }

        .stock_info {


            h5 {
                font-family: var(--helveticaneueltarabicLight);
                color: #5E45FF;
                font-size: 1.9rem;
                line-height: 2.5rem;
                font-weight: 600;
                display: block;

                @media #{$media-767} {
                    font-size: 2.5rem;
                    line-height: 3rem;
                }
            }

            >p {
                color: #5E45FF;
                font-size: 15px;
                line-height: 100%;
                letter-spacing: 0.06em;
                font-weight: 600;
            }

            .stockData {
                margin-top: 10px;
                font-size: 1.9rem;
                line-height: 2.6rem;
                font-weight: 400;
                display: block;
                display: flex;
                align-items: center;
                gap: 5px;
                @media #{$media-767} {
                    font-size: 2.5rem;
                    line-height: 3rem;
                }

                // &.up {
                //     color: #008000;
                // }

                // &.down {
                //     color: #ff0001;
                // }

                >span:first-child {
                    color: #000000 !important;
                }
            }
        }
    }
}

.share_graph_wrapper {
    height: 300px;
    margin-top: 50px;
    margin-inline-end: 15%;

    @media #{$media-1600} {
        height: 250px;
    }

    @media #{$media-1024} {
        margin-inline-end: 0;
    }
}