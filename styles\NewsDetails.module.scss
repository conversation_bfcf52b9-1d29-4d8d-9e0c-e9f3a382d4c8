@import "variable", "base";

.news_details_main_section {
    width: 100%;
    background-color: white;
    border-bottom: 1px solid #DDDDDD;

    .news_details_sub_section {
        width: 100%;
        display: flex;

        @media #{$media-950} {
            flex-wrap: wrap;
        }

        .news_details_sub_left_section {
            width: 68%;

            @media #{$media-950} {
                width: 100%;
            }

            .news_details_sub_left_content1 {
                p {
                    color: #000000;
                    font-size: 1.063rem;
                    line-height: 1.6875rem;
                    font-family: var(--helveticaNeue);
                    margin-bottom: 20px;
                    @media #{$media-1600} {
                        margin-bottom: 15px;
                        font-size: 1.2rem;
                        line-height: 1.7875rem;
                    }
                    @media #{$media-950} {
                        font-size: 1.4rem;
                        line-height: 23px;
                    }

                    @media #{$media-500} {
                        font-size: 1.7rem;
                        line-height: 23px;
                    }
                }

                h3 {
                    display: block;
                    font-size: 2.813rem;
                    line-height: 150%;
                    font-weight: 400;
                }
                h2,h4,h5,h6{
                    line-height: 149%;
                }

                .news_details_video_block {
                    margin-top: 30px;

                    width: 100%;
                    position: relative;
                    border-radius: 20px;
                    overflow: hidden;

                    img {
                        width: 100%;
                        display: block;
                        height: auto;
                        max-height: 380px;
                        object-fit: cover;
                    }

                    .news_details_video_play_btn {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);

                        img {
                            @media #{$media-767} {
                                width: 60px;
                                height: 60px;
                            }
                        }
                    }
                }

                .news_details_highlight_area {
                    border-inline-start: 5px solid #5e45ff;
                    padding-inline-start: 2%;
                    color: #1d1d1d;
                    font-size: 1.563rem;
                    line-height: 33px;
                    font-weight: 400;
                    font-family: var(--helveticaNeue);
                    margin-bottom: 50px;

                    @media #{$media-950} {
                        font-size: 1.7rem;
                        line-height: 25px;
                    }

                    @media #{$media-767} {
                        line-height: 23px;
                    }
                }

                iframe {
                    width: 100%;
                    height: 100%;
                    aspect-ratio: 2/1;
                }
            }

            .news_details_sub_left_policy_labs_sec {
                margin-top: 50px;

                @media #{$media-767} {
                    margin-top: 25px;
                }

                h4 {
                    display: block;
                    font-family: var(--helveticaNeue);
                    color: #000000;
                    font-size: 45px;
                    font-weight: bold;

                    @media #{$media-767} {
                        font-size: 25px;
                        margin-bottom: 15px;
                    }
                }

                p {
                    display: block;

                    color: #000000;
                    text-align: start;
                    font-family: var(--helveticaNeue);
                    font-size: 17px;
                    line-height: 27px;
                    font-weight: 400;
                    margin-bottom: 25px;

                    @media #{$media-767} {
                        font-size: 14px;
                        line-height: 23px;
                        font-size: 14px;
                        margin-bottom: 15px;
                    }
                }

                .quote {
                    p {
                        display: block;
                        color: #282828;
                        font-size: 2.313rem;
                        line-height: 42px;
                        font-weight: bold;
                        margin-bottom: 0;

                        @media #{$media-767} {
                            line-height: 26px;
                        }
                    }

                }

                h5 {
                    display: block;

                    color: #282828;
                    font-size: 2.313rem;
                    line-height: 42px;

                    @media #{$media-950} {
                        line-height: 35px;
                        padding-top: 30px;
                    }

                    @media #{$media-767} {
                        line-height: 25px;
                        padding-top: 15px;
                        font-size: 2rem;
                    }
                }

                span {
                    display: block;
                    color: #a9a9a9;
                    font-size: 15px;
                    font-weight: 400;
                }
            }

            .news_details_how_do_they_work_sec {
                color: #000000;
                margin-bottom: 15px;

                @media #{$media-950} {
                    margin-top: 80px;
                }

                @media #{$media-767} {
                    margin-top: 35px;
                }

                h3 {
                    display: block;
                    font-size: 2.813rem;
                    font-weight: 400;

                    @media #{$media-950} {
                        font-size: 3.813rem;
                    }

                    @media #{$media-767} {
                        font-size: 3.3rem;
                    }
                }

                .news_details_how_do_they_work_ul {
                    list-style: none;

                    li {
                        padding: 5px 0 5px 30px;
                        position: relative;
                        font-family: var(--helveticaNeue);
                        color: #000000;
                        font-size: 1.063rem;
                        font-weight: 400;

                        @media #{$media-767} {
                            font-size: 15px;
                        }

                        &::after {
                            position: absolute;
                            content: "";

                            height: 10px;
                            width: 10px;
                            background-color: #5e45ff;
                            left: 0;
                            top: 15px;
                            border-radius: 50%;
                        }
                    }
                }

                h4 {
                    color: #000000;
                    font-size: 2.813rem;
                    font-weight: 500 !important;
                    font-family: var(--helveticaneuelt_arabic_55);

                    @media #{$media-767} {
                        font-size: 2.513rem;
                        margin-top: 10px;
                    }
                }

                .news_details_how_do_they_work_slider_sec {
                    li {
                        padding-bottom: 30px;

                        .news_details_how_do_they_work_slider_head {
                            color: #9a9a9a;
                            font-size: 19px;
                            line-height: 30px;
                            font-weight: 400;
                            position: relative;
                            padding-inline-start: 25px;
                            margin-bottom: 30px;
                            display: block;

                            @media #{$media-767} {
                                font-size: 16px;
                            }

                            &::after {
                                position: absolute;
                                content: "";
                                inset-inline-start: 0;
                                top: 10px;
                                height: 14px;
                                width: 14px;
                                border-radius: 50%;
                                background-color: #ffd600;
                            }
                        }

                        .news_details_how_do_they_work_slider_outer {
                            width: 100%;
                            height: 10px;
                            background-color: #dbdbdb;
                            border-radius: 10px;
                            position: relative;

                            .news_details_how_do_they_work_slider_inner {
                                position: absolute;
                                left: 0;
                                top: 0;
                                height: 100%;
                                background-color: #5e45ff;
                                border-radius: 10px;

                                &::after {
                                    position: absolute;
                                    content: "";
                                    top: 50%;
                                    transform: translateY(-50%);
                                    right: -5px;
                                    height: 30px;
                                    width: 30px;
                                    border-radius: 50%;
                                    background-color: rgb(255, 255, 255);
                                    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.281);
                                    cursor: pointer;
                                }
                            }
                        }

                        .news_details_how_do_they_work_slider_bottom {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding-top: 10px;

                            p {
                                color: #5e45ff;
                                font-size: 23px;
                                line-height: 30px;
                                font-weight: 600;
                                font-family: var(--helveticaNeue);

                                @media #{$media-1024} {
                                    font-size: 16px;
                                }
                            }
                        }
                    }
                }

                .news_details_how_do_they_work_report_sec {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 3%;
                    row-gap: 30px;

                    @media #{$media-767} {
                        row-gap: 20px;
                    }

                    li {
                        width: 48%;
                        box-sizing: border-box;
                        padding: 30px 4% 50px 5%;
                        border: 1px solid rgb(196, 196, 196);
                        border-radius: 15px;

                        @media #{$media-767} {
                            width: 100%;
                        }

                        .how_do_they_work_report_count_sec {
                            display: flex;
                            align-items: center;
                            margin-bottom: 20px;

                            span {
                                display: inline-block;
                                color: #5e45ff;
                                font-size: 100px;
                                font-weight: 600;
                                line-height: 100%;

                                @media #{$media-767} {
                                    font-size: 70px;
                                }
                            }

                            p {
                                display: inline-block;
                                padding-inline-start: 5px;
                                width: 20%;
                                color: #000000;
                                font-size: 2.125rem;
                                font-weight: bold;
                                line-height: 100%;
                                font-family: var(--helveticaNeue);

                                @media #{$media-1024} {
                                    width: 25%;
                                }
                            }
                        }

                        .how_do_they_work_report_paragraph {

                            font-family: var(--helveticaNeue);
                            color: #000000;
                            font-size: 15px;
                            line-height: 21px;
                            font-weight: 400;
                        }

                        &:nth-child(3) {
                            width: 100%;

                            .how_do_they_work_report_paragraph {


                                // font-size: 19px;
                                // line-height: 29px;
                                // font-weight: 400;
                                // @media #{$media-767} {
                                //      font-size: 15px;
                                //      line-height: 23px;
                                //  }
                            }
                        }
                    }
                }
            }
        }

        .news_details_sub_right_section {
            width: 33%;
            box-sizing: border-box;
            padding-inline-start: 4%;

            @media #{$media-950} {
                width: 100%;
                padding-inline-start: 0;
                padding-top: 40px;
            }

            .news_details_sub_right_content {
                padding-bottom: 50px;

                @media #{$media-767} {
                    padding-bottom: 30px;
                }

                &:last-child {
                    @media #{$media-950} {
                        padding-bottom: 0;
                    }
                }

                h4 {
                    color: #000000;
                    font-size: 18px;
                    font-weight: 400;
                    text-transform: uppercase;
                    font-family: var(--helveticaNeue);

                    @media #{$media-767} {
                        font-size: 15px;
                    }
                }

                ul {
                    display: flex;
                    flex-wrap: wrap;
                    margin: 0 -5px;
                    padding-top: 10px;

                    li {
                        margin: 5px;
                        padding: 10px 20px;
                        color: #7d7d7d;
                        font-size: 0.938rem;
                        font-weight: 400;
                        background: #f1f1f1;
                        border-radius: 30px;
                        font-family: var(--helveticaNeue);

                        @media #{$media-950} {
                            font-size: 1.65rem;
                        }
                    }
                }
            }
        }
    }
}

//   {/* =============We are making bold moves, together=================== */}

.we_are_making_bold_moves_section {
    width: 100%;
    background-color: white;

    .we_are_making_bold_moves_container {
        @media #{$media-950} {
            padding-bottom: 120px;
        }

        h3 {
            display: block;
            font-family: var(--helveticaneuelt_arabic_55);

            color: #000000;
            font-size: 2.813rem;
            line-height: 3rem;
            font-weight: 600;
        }

        .we_are_making_bold_moves_swiper_sec {
            display: flex;
            position: relative;

            .we_are_making_bold_moves_swiper_next_btn {
                inset-inline-end: -8%;
                height: 50px;
                width: 50px;
                background-color: transparent;

                @media #{$media-1600} {
                    inset-inline-end: -4%;
                    padding: 1.5%;
                }

                @media #{$media-767} {
                    height: 40px;
                    width: 40px;
                    inset-inline-end: -50px !important;
                }

                img {
                    @media #{$media-1024} {
                        width: 40%;
                        filter: invert(1);
                    }

                    @media #{$media-767} {
                        width: 25%;
                    }
                }

                @media #{$media-1024} {
                    background-color: #5e45ff;
                    inset-inline-start: 0;
                    inset-inline-end: -65px;
                    margin: 0 auto;
                    bottom: -50px;
                    top: auto;
                }

                @media #{$media-950} {
                    bottom: -75px;
                }
            }

            .we_are_making_bold_moves_swiper_prev_btn {
                inset-inline-start: -8%;
                height: 50px;
                width: 50px;
                background-color: transparent;

                @media #{$media-1600} {
                    inset-inline-start: -4%;
                    padding: 1.5%;
                }

                @media #{$media-767} {
                    height: 40px;
                    width: 40px;
                    inset-inline-start: -50px !important;
                }

                img {
                    @media #{$media-1024} {
                        width: 40%;
                        filter: invert(1);
                    }

                    @media #{$media-767} {
                        width: 25%;
                    }
                }

                @media #{$media-1024} {
                    background-color: #5e45ff;
                    inset-inline-start: -65px;
                    inset-inline-end: 0;
                    margin: 0 auto;
                    bottom: -50px;
                    top: auto;
                }

                @media #{$media-950} {
                    bottom: -75px;
                }
            }

            .we_are_making_bold_moves_card_body {
                background-color: rgb(255, 255, 255);
                border-radius: 10px;
                // border: 1px solid #5e45ff;
                // box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
                list-style: none;
                z-index: 2;
                position: relative;
                margin: 1px;
                height: 100%;

                >a {
                    height: 100%;
                }

                .card_wrapper {
                    display: flex;
                    flex-direction: column;
                    padding-bottom: 2px;
                    height: 100%;
                    border-radius: 10px;
                }

                .we_are_making_bold_moves_card_img {
                    width: 100%;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                    overflow: hidden;

                    img {
                        display: block;
                        object-fit: cover;
                        height: 300px;
                        width: 100%;
                    }
                }

                .we_are_making_bold_moves_card_data {
                    background-color: rgb(255, 255, 255);
                    padding: 30px 8% 50px 8%;
                    border-radius: 0 0 10px 10px;
                    box-sizing: border-box;
                    width: 100%;
                    flex-grow: 1;

                    @media #{$media-767} {
                        padding: 20px 5% 30px 5%;
                    }

                    span {
                        color: #5B5B5B;
                        font-size: 15px;
                        line-height: 24px;
                        letter-spacing: 0.31px;
                        font-weight: 400;
                        position: relative;
                        z-index: 1;
                        font-family: var(--helveticaNeue);

                    }

                    h4 {
                        color: #272727;
                        font-size: 20px;
                        line-height: 28px;
                        font-weight: 500;
                        position: relative;
                        z-index: 1;
                        font-family: var(--helveticaNeueMedium);
                        padding-top: 15px;
                        min-height: 99px;

                        @media #{$media-767} {
                            padding-top: 10px;
                            font-size: 17px;
                        }
                    }

                    p {
                        padding-top: 10px;
                        color: #353535;
                        font-size: 18px;
                        line-height: 28px;
                        font-weight: 400;
                        position: relative;
                        z-index: 1;
                        font-family: var(--helveticaNeue);

                        @media #{$media-767} {
                            font-size: 15px;
                            line-height: 25px;
                        }
                    }
                }

                &::after {
                    content: "";
                    position: absolute;
                    width: calc(100% + 2px);
                    height: calc(100% + 2px);
                    left: -1px;
                    top: -1px;
                    background-image: linear-gradient(to top,
                            rgba(171, 163, 223, 1) 20%,
                            rgba(186, 178, 234, 0.2) 40%);
                    border-radius: 14px;
                    z-index: -1;
                }
            }
        }
    }
}

.plicy_lab_sec {
    h5 {
        font-size: 2.813rem;
        line-height: 3rem;
        font-family: var(--helveticaNeue);
        margin-bottom: 25px;

        @media #{$media-950} {
            padding-top: 40px;
        }
    }

    p {
        font-size: 1.063rem;
        line-height: 1.6rem;
        color: #000;
        font-family: var(--helveticaNeue);

        @media #{$media-950} {
            font-size: 1.7rem;
            line-height: 2rem;
        }
    }
}

.news_details_sub_left_content1 {
    h4 {
        display: block;
        font-family: var(--helveticaNeue);
        color: #000;
        font-size: 2.813rem;
        font-weight: 700;
        margin-bottom: 30px;
        line-height: 3.4rem;

        @media #{$media-950} {
            margin-bottom: 20px;
        }
    }

    h5 {
        font-size: 2.813rem;
        line-height: 3rem;
        font-family: var(--helveticaNeue);
        margin-bottom: 25px;
        
    }

    h2+p {
        margin-top: 15px;
    }
}

.news_details_sub_left_content1 {
    ul {
        margin-top: 20px;
        @media #{$media-1600} {
            margin-top: 20px;
        }

        li {
            padding: 5px 0 5px 0;
            padding-inline-start: 30px;
            position: relative;
            font-family: var(--helveticaNeue);
            color: #000;
            font-size: 1.063rem;
            font-weight: 400;

            @media #{$media-950} {
                font-size: 15px;
            }

            &::before {
                position: absolute;
                content: "";
                height: 10px;
                width: 10px;
                background-color: #5e45ff;
                inset-inline-start: 0;
                top: 0.625rem;
                border-radius: 50%;

                @media #{$media-950} {
                    top: 9px;
                }
            }

            a {
                color: #337ab7;
                font-size: 1.063rem;
                line-height: 28px;
                font-family: var(--helveticaNeue);
                margin-bottom: 20px;

                @media #{$media-950} {
                    font-size: 1.7rem;
                }

                @media #{$media-767} {
                    line-height: 23px;
                }
            }
        }
    }

    ol {
        padding-inline-start: 20px;

        li {
            padding-inline-start: 10px;
        }
    }

    img {
        width: 100%;
        height: auto;
        max-width: unset;
    }
}