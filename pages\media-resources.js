import React, { useEffect, useState } from "react";
import style from "@/styles/MediaResources.module.scss";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import InvestorsResourcesCard from "@/component/InvestorsResourcesCard";
import Image from "next/image";
import TabSection from "@/component/Tab-section";
import { useRouter } from "next/router";

import Yoast from "@/component/yoast";
import {
  getMediaResources,
  getMediaresourcesPostPosts,
  getIrMenuLinks,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const MediaResources = (props) => {
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  const [visiblePosts, setVisiblePosts] = useState(9); // Number of visible posts
  const router = useRouter();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const yoastData = props?.IrMediaData?.yoast_head_json;

  if (!props.IrMediaData) {
    return null;
  }

  const handleLoadMore = () => {
    setVisiblePosts((prev) => prev + 9);
  };

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.IrMediaData &&
      props?.IrMediaData?.acf &&
      props?.IrMediaData?.acf?.banner_title &&
      (props?.IrMediaData?.acf?.mob_banner_image ||
        props?.IrMediaData?.acf?.banner_image ||
        props?.IrMediaData?.acf?.breadcrumbs ||
        props?.IrMediaData?.acf?.banner_viedo ||
        props?.IrMediaData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.IrMediaData?.acf?.banner_title}
          breadcrumb1={
            props?.IrMediaData?.acf?.active_breadcrumbs === "yes"
              ? props?.IrMediaData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.IrMediaData?.acf?.mob_banner_image?.url
              : props?.IrMediaData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.IrMediaData?.acf?.banner_viedo?.url}
          banner_type={props?.IrMediaData?.acf?.banner_type}
        />
      ) : null}

      {/* =========Tab Section====== */}
      {props &&
        props?.IRMenuData &&
        props?.IRMenuData?.investors_relations_menu && (
          <TabSection tabs={props?.IRMenuData?.investors_relations_menu} />
        )}

      {/* -----------Company Announcement section----------- */}

      <section className={`${style.media_resources_card_section}`}>
        <div
          className={`${style.media_resources_card_container} ${comon.wrap} ${comon.pt_100}   ${comon.pb_100}  `}
        >
          {props &&
            props?.IrMediaData &&
            props?.IrMediaData?.acf &&
            props?.IrMediaData?.acf?.section_title && (
              <h3 data-aos="fade-up" data-aos-duration="1000">
                {props?.IrMediaData?.acf?.section_title &&
                  parse(props?.IrMediaData?.acf?.section_title)}
              </h3>
            )}
          <ul
            className={`${style.media_resources_card_list}  ${comon.mt_50}  `}
          >
            {props?.IrMediaPostData.slice(0, visiblePosts).map(
              (Mediadata, medIndex) => (
                <li
                  className={`${style.media_resources_card_list_img} `}
                  data-aos="fade-up"
                  data-aos-duration="1000"
                  key={medIndex}
                >
                  <Link href={Mediadata?.acf?.link || "#."} target="_blank">
                    {Mediadata?.acf?.media_image && (
                      <Image
                        src={Mediadata?.acf?.media_image?.url}
                        height={400}
                        width={500}
                        alt=""
                      />
                    )}

                    {Mediadata?.acf?.media_type === "video" && (
                      <div
                        className={`${style.media_resources_card_list_img_play_btn} `}
                      >
                        <Image
                          src={"/images/media-resource-img-play-btn.svg"}
                          height={95}
                          width={95}
                          alt=""
                        />
                      </div>
                    )}
                    <div
                      className={`${style.media_resources_card_list_data}  `}
                    >
                      {Mediadata?.title && (
                        <h5>
                          {Mediadata?.title &&
                            parse(Mediadata?.title?.rendered)}
                        </h5>
                      )}
                      {Mediadata?.acf?.sub_title && (
                        <span>{Mediadata?.acf?.sub_title} </span>
                      )}
                      <p className={style.action_text}>
                        {Mediadata?.acf?.media_type === "video"
                          ? router.locale === "ar"
                            ? "شاهد الآن"
                            : "Watch Now!"
                          : router.locale === "ar"
                          ? "اضغط هنا"
                          : "Click Here"}
                      </p>
                    </div>
                  </Link>
                </li>
              )
            )}
          </ul>
          {/* Load More Button */}
          {visiblePosts < props?.IrMediaPostData.length && (
            <p className={`${style.loading} ${style.pt_60}`}>
              <a href="#." onClick={handleLoadMore}>
                {" "}
                {router.locale === "ar" ? "تحميل المزيد ..." : "Load More ...."}
              </a>
            </p>
          )}
        </div>
      </section>
    </div>
  );
};

export default MediaResources;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const IrMediaData = await getMediaResources(locale);
  const IRMenuData = await getIrMenuLinks(locale);
  const query = "";
  const IrMediaPostData = await getMediaresourcesPostPosts(locale, query);

  //console.log('testingdata', IrMediaData)

  return {
    props: {
      IrMediaData: IrMediaData || null,
      IRMenuData: IRMenuData || null,
      IrMediaPostData: IrMediaPostData || null,
    },
    revalidate: 10,
  };
};
