@import "variable", "base";



.tab_left_block {
    width: 30%;

    @media #{$media-820} {
        width: 100%;
    }
}

.tab_right_block {
    width: 70%;

    @media #{$media-820} {
        width: 100%;
    }
}

.tab_ul_list {
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    @media #{$media-820} {
        display: flex;
        flex-wrap: wrap;
        column-gap: 25px;
    }

    li {
        cursor: pointer;
        list-style: none;
        margin-bottom: 10px;
        padding: 2px;
        border-radius: 50px;
        background: rgba(4, 0, 31, 0.5);
        color: $white;
        font-size: 1rem;
        padding: 9px 5%;
        transition: all 0.4s ease-in-out;

        @media #{$media-820} {
            font-size: 1.3rem;
            width: 48%;
            align-items: center;
            display: flex;
            justify-content: center;
        }

        @media #{$media-767} {
            width: fit-content;
            font-size: 1.7rem;
        }

        &:hover {
            background: #5E45FF;
        }

        &.activeTab {
            background: #5E45FF;
        }
    }
}

.tab_img_block {
    width: 40%;
    position: relative;

    @media #{$media-820} {
        width: 100%;
        margin: 0 auto;
    }

}

.tab_txt_block {
    width: 60%;

    @media #{$media-820} {
        width: 100%;
    }

    p {
        color: #fff;
    }

    h3 {
        color: #fff;
        font-size: 2.813rem;
        margin-bottom: 20px;
    }

    ul {
        display: flex;
        flex-wrap: wrap;
        gap: 10px 8px;
        margin: 20px 0 30px 0;

        li a{
            display: block;
            background: rgba(94, 69, 255, .3);
            font-size: 1.125rem;
            line-height: 1.6rem;
            color: #fff;
            border-radius: 30px;
            padding: 15px 25px;
            transition: all 0.4s ease-in-out;

            @media #{$media-820} {
                font-size: 1.5rem;
            }

            @media #{$media-767} {
                font-size: 1.7rem;
                line-height: 2.3rem;
                padding: 15px 20px;
            }
            &:hover{
                background: #fcb136
            }
        }
    }
}

.video {
    width: 320px;
    height: 431px;
    position: absolute;
    inset-inline-start: 0;
    object-fit: cover;
    top: 0;
    mix-blend-mode: lighten;

    @media #{$media-1200} {
        width: 240px;
        height: 330px;
    }

    @media #{$media-820} {
        position: initial;
        width: 100%;
        height: 100%;
    }
}