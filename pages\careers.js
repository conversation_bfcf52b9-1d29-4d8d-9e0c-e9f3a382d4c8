import InnerBanner from "@/component/InnerBanner";
import Overview from "@/component/Overview";
import OverviewTwoCol from "@/component/OverviewTwoCol";
import React from "react";
import style from "@/styles/Careers.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import { useState,useEffect } from "react";
import Link from "next/link";
import VideoBlock from "@/component/VideoBlock";

import Yoast from "@/component/yoast";
import { getCareerspage } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";



const Careers = (props) => {
  // Initialize state for multiple dropdowns
  const [isOpen, setIsOpen] = useState([false, false, false, false]);

  // Handle focus: update specific dropdown state
  const handleFocus = (index) => {
    const newIsOpen = [...isOpen];
    newIsOpen[index] = true; // Set only the focused dropdown to open
    setIsOpen(newIsOpen);
  };

  // Handle blur: reset dropdown state
  const handleBlur = (index) => {
    const newIsOpen = [...isOpen];
    newIsOpen[index] = false; // Close only the focused dropdown
    setIsOpen(newIsOpen);
  };

  const career_jobs = [
    {
      location: "Jeddah, Saudi Arabia",
      job_title: "Manager, Advisory",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt labore.",
    },
    {
      location: "Jeddah, Saudi Arabia",
      job_title: "Manager, Advisory",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt labore.",
    },
    {
      location: "Jeddah, Saudi Arabia",
      job_title: "Manager, Advisory",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt labore.",
    },
    {
      location: "Jeddah, Saudi Arabia",
      job_title: "Manager, Advisory",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt labore.",
    },
    {
      location: "Jeddah, Saudi Arabia",
      job_title: "Manager, Advisory",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt labore.",
    },
    {
      location: "Jeddah, Saudi Arabia",
      job_title: "Manager, Advisory",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt labore.",
    },
  ];


  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
     useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
     }, []);
    
  
  const yoastData = props?.CareersData?.yoast_head_json;

  if (!props.CareersData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

     {yoastData && <Yoast meta={yoastData} />}

            {props &&
                props?.CareersData &&
                props?.CareersData?.acf &&
                props?.CareersData?.acf?.banner_title &&
                (props?.CareersData?.acf?.mob_banner_image ||
                props?.CareersData?.acf?.banner_image ||
                props?.CareersData?.acf?.breadcrumbs ||
                props?.CareersData?.acf?.banner_viedo ||                
                props?.CareersData?.acf?.banner_type) ? (
                <InnerBanner
                pagename={props?.CareersData?.acf?.banner_title}
                breadcrumb1={props?.CareersData?.acf?.active_breadcrumbs ==='yes' ? props?.CareersData?.acf?.breadcrumbs : '' } 
                background={`${isMobile ? props?.CareersData?.acf?.mob_banner_image?.url : props?.CareersData?.acf?.banner_image?.url}`}          
                videoSrc={props?.CareersData?.acf?.banner_viedo?.url}
                banner_type={props?.CareersData?.acf?.banner_type}              

                />
            ) : null
      }
      {props &&
             props?.CareersData &&
                props?.CareersData?.acf &&
                (props?.CareersData?.acf?.overview_title ||
          props?.CareersData?.acf?.overview_content) ? (
          
      <div className={`${style.grow_sec} scareer_overview`}>
        <OverviewTwoCol
            head={props?.CareersData?.acf?.overview_title}
            paragraph={props?.CareersData?.acf?.overview_content}     
          equalCol={true}
        />
      </div>
      ) : null}
      {props &&
             props?.CareersData &&
                props?.CareersData?.acf &&
                (props?.CareersData?.acf?.overview_video ||
          props?.CareersData?.acf?.overview_cover_image) ? (
      <VideoBlock
        videos={props?.CareersData?.acf?.overview_video?.url}
        videoPoster={props?.CareersData?.acf?.overview_cover_image?.url}
      />
 ) : null}
      <section className={`${style.careers_latest_opening_section} ${comon.careers_latest_opening_section}`}>
        {/* <div
          className={`${style.careers_latest_opening_containers} ${comon.pt_100}`}
        >
          {props?.CareersData?.acf?.latest_openings_title &&
            <h3 data-aos="fade-up" data-aos-duration="1000" className={`${comon.pb_100}`}>
              {parse(props?.CareersData?.acf?.latest_openings_title)}
            </h3>
          } */}
           {props?.CareersData?.acf?.our_careers_listing &&
           <div className="iframe-container">
              {parse(props?.CareersData?.acf?.our_careers_listing)}
            </div>
          }

          {/*   <ul
            className={`${style.careers_latest_opening_List} ${comon.pt_30} `}
          >
           
            <li
              className={`${style.careers_latest_opening_List_form_sec}`}
              data-aos="fade-up"
              data-aos-duration="1000"
            >
              <ul>
                <li
                  className={`${style.careers_latest_opening_List_form_search}`}
                  data-aos="fade-up"
                  data-aos-duration="1000"
                >
                  <input type="text" placeholder="Search" />
                  <div
                    className={`${style.careers_latest_opening_List_form_search_icon}`}
                  >
                    <Image
                      src={"/images/career_search_icon.png"}
                      height={21}
                      width={26}
                      alt=""
                    />
                  </div>
                </li>

                

                <li
                  className={`${style.careers_latest_opening_List_form_dropdown}`}
                >
                  <select
                    placeholder="Workplace Type"
                    onFocus={() => handleFocus(0)}
                    onBlur={() => handleBlur(0)}
                    onChange={() => handleBlur(0)}
                  >
                    <option value="" disabled selected>
                      Workplace Type
                    </option>
                    <option value="remote">Remote</option>
                    <option value="office">Office</option>
                    <option value="hybrid">Hybrid</option>
                  </select>{" "}
                  <div
                    className={`${
                      style.careers_latest_opening_List_form_search_icon
                    }  ${isOpen[0] ? style.rotate : ""}`}
                  >
                    <Image
                      src={"/images/career_dropdown_icon.svg"}
                      height={14}
                      width={14}
                      alt=""
                    />
                  </div>
                </li>
                <li
                  className={`${style.careers_latest_opening_List_form_dropdown}`}
                >
                  <select
                    placeholder="Workplace Type"
                    onFocus={() => handleFocus(1)}
                    onBlur={() => handleBlur(1)}
                    onChange={() => handleBlur(1)}
                  >
                    <option value="" disabled selected>
                      Location{" "}
                    </option>
                    <option value="remote">Remote</option>
                    <option value="office">Office</option>
                    <option value="hybrid">Hybrid</option>
                  </select>{" "}
                  <div
                    className={`${
                      style.careers_latest_opening_List_form_search_icon
                    }  ${isOpen[1] ? style.rotate : ""}`}
                  >
                    <Image
                      src={"/images/career_dropdown_icon.svg"}
                      height={14}
                      width={14}
                      alt=""
                    />
                  </div>
                </li>
                <li
                  className={`${style.careers_latest_opening_List_form_dropdown}`}
                >
                  <select
                    placeholder="Workplace Type"
                    onFocus={() => handleFocus(2)}
                    onBlur={() => handleBlur(2)}
                    onChange={() => handleBlur(2)}
                  >
                    <option value="" disabled selected>
                      Department
                    </option>
                    <option value="remote">Remote</option>
                    <option value="office">Office</option>
                    <option value="hybrid">Hybrid</option>
                  </select>{" "}
                  <div
                    className={`${
                      style.careers_latest_opening_List_form_search_icon
                    }  ${isOpen[2] ? style.rotate : ""}`}
                  >
                    <Image
                      src={"/images/career_dropdown_icon.svg"}
                      height={14}
                      width={14}
                      alt=""
                    />
                  </div>
                </li>
                <li
                  className={`${style.careers_latest_opening_List_form_dropdown}`}
                >
                  <select
                    placeholder="Workplace Type"
                    onFocus={() => handleFocus(3)}
                    onBlur={() => handleBlur(3)}
                    onChange={() => handleBlur(3)}
                  >
                    <option value="" disabled selected>
                      Work Type
                    </option>
                    <option value="remote">Remote</option>
                    <option value="office">Office</option>
                    <option value="hybrid">Hybrid</option>
                  </select>{" "}
                  <div
                    className={`${
                      style.careers_latest_opening_List_form_search_icon
                    }  ${isOpen[3] ? style.rotate : ""}`}
                  >
                    <Image
                      src={"/images/career_dropdown_icon.svg"}
                      height={14}
                      width={14}
                      alt=""
                    />
                  </div>
                </li>
              </ul>
            </li>
            {career_jobs.map((data, index) => (
              <li
                className={`${style.careers_latest_opening_List_job_card_sec}`}
                key={index}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <ul>
                  <li>
                    <div
                      className={`${style.careers_latest_opening_List_job_icon}`}
                    >
                      <Image
                        src={"/images/career-job-icon.svg"}
                        height={150}
                        width={150}
                        alt=""
                      />
                    </div>
                  </li>
                  <li>
                    <div className={style.career_info}>
                      <div className={style.career_title}>
                        <span>{data.location}</span>
                        <h4>{data.job_title}</h4>
                      </div>
                      <div className={style.career_description}>
                        <p>{data.description}</p>
                      </div>
                    </div>
                  </li>
                  <li>
                    <Link
                      href={"/career-detail"}
                      className={`${style.careers_latest_opening_btn_link}`}
                    >
                      <Image
                        src={"/images/btn-right-arrow.svg"}
                        height={15}
                        width={15}
                        alt=""
                      />
                    </Link>
                  </li>
                </ul>
              </li>
            ))}
          </ul> */ }
        {/* </div> */}
      </section>
    </div>
  );
};

export default Careers;



export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const CareersData = await getCareerspage(locale);
  
  return {
    props: {
      CareersData: CareersData || null,
    },
    revalidate: 10,
  };
};