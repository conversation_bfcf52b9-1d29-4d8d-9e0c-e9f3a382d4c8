import React from 'react';
import Link from 'next/link';
import comon from "@/styles/comon.module.scss";
import styles from '@/styles/engagmentPrg.module.scss';
import Image from 'next/image';
import parse from "html-react-parser";

const EngagmentProgram = ({icon,link,title}) => {
  return (
    <Link href={`${link}`} className={styles.eng_prg_list } >
         <div className={styles.eng_prg_set}>
        <div className={styles.prg_top}>
          {icon &&
            <div className={styles.icon}>
              <Image src={icon} width={136} height={136} alt='image' />
            </div>
          }
                {title &&
                  <h3>{title && parse(title)}</h3>
                }          
             </div>
             <div  className={`${styles.link} ${comon.eng_prg_link}`}>
            <Image
                className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                src={`/images/next_black_ic.svg`}
                alt="call"
                width={13}
                height={12}
            />
             </div>
         </div>
    </Link>
  )
}

export default EngagmentProgram