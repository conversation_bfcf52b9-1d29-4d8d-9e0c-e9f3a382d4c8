import React, { useEffect, useRef, useState } from "react";
import style from "@/styles/TabSection.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";
import parse from "html-react-parser";

const TabSection = ({ tabs}) => {
  const route = useRouter();
  const { locale, pathname } = route;
  const tabWrapperRef = useRef(null); // Ref for scrolling container
  const [isOverflowing, setIsOverflowing] = useState(false);

  // Function to normalize paths (removes locale for comparison)
  const normalizePath = (path) => {
    return path.startsWith(`/${locale}`) ? path.replace(`/${locale}`, "") : path;
  };

  useEffect(() => {
    const checkOverflow = () => {
      if (tabWrapperRef.current) {
        const tabWrapper = tabWrapperRef.current;
        setIsOverflowing(tabWrapper.scrollWidth > tabWrapper.clientWidth);
      }
    };

    checkOverflow(); // Check initially
    window.addEventListener("resize", checkOverflow); // Check on resize

    return () => {
      window.removeEventListener("resize", checkOverflow); // Cleanup event listener
    };
  }, [tabs]);

  useEffect(() => {
    if (!isOverflowing) return; // Ensure scrolling only happens when overflowing
  
    setTimeout(() => {
      if (!tabWrapperRef.current) return;
  
      const activeTab = tabWrapperRef.current.querySelector(`.${style.tab_active}`);
      if (activeTab) {
        const tabWrapper = tabWrapperRef.current;
        const tabWrapperRect = tabWrapper.getBoundingClientRect();
        const activeTabRect = activeTab.getBoundingClientRect();
  
        // Check if the active tab is too close to the right edge
        const isLastTab = activeTabRect.right > tabWrapperRect.right - 20; // 20px buffer
  
        activeTab.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: isLastTab ? "end" : "center",
        });
      }
    }, 300);
  }, [pathname, isOverflowing]);

  return (
    <div>
      {/* -----------Tab section----------- */}
      <section className={style.tab_section}>
        <div className={comon.wrap}>
          <div className={`${style.tab_wrapper} ${isOverflowing ? style.tab_wrapper_overflowing : ""}`} ref={tabWrapperRef}>
            <ul>             
              {tabs.map((data, index) => (
                <li key={index}>
                  {data?.ir_menu && (
                    <Link
                      href={data.ir_menu?.url}
                      className={
                        normalizePath(pathname).startsWith(normalizePath(data?.ir_menu?.url))
                          ? style.tab_active
                          : ""
                      }
                      target={data?.ir_menu?.target}
                    >           
                      {data?.ir_menu?.title && parse(data?.ir_menu?.title)}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TabSection;
