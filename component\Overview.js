import React, { useRef, useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { useInView } from "react-intersection-observer";
import { useRouter } from "next/router";
import parse from "html-react-parser";
import CountUp from "react-countup";
import Image from "next/image";
import "swiper/css";
import "swiper/css/pagination";
import overview from "@/styles/Overview.module.scss";
import comon from "@/styles/comon.module.scss";

const chunkArray = (array, chunkSize) => {
  return array.reduce((chunks, item, index) => {
    const chunkIndex = Math.floor(index / chunkSize);
    chunks[chunkIndex] = [...(chunks[chunkIndex] || []), item];
    return chunks;
  }, []);
};

const Overview = ({ head, paragraph, swiperhead, swiperContents, tab, videoBlock }) => {
  const [isMobile, setIsMobile] = useState(false);
  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";
  const parentRef = useRef();
  const { inView } = useInView({ triggerOnce: false, threshold: 0.5, ref: parentRef });

  // Responsive check
  useEffect(() => {
    const checkScreenSize = () => setIsMobile(window.innerWidth <= 767);
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const formattedContents = chunkArray(swiperContents || [], isMobile ? 4 : 3);

  const renderSwiperContent = (contentData) => (
    <ul
      className={`${overview.overview_swiper_card_data_sec} ${comon.overview_swiper_card_data_sec} ${comon.mt_30} ${contentData.length % 2 === 0 && overview.overview_even}`}
      ref={parentRef}
    >
      {contentData.map((content, itemIndex) => (
        <React.Fragment key={itemIndex}>
          <li data-aos="fade-up" data-aos-duration="1000">
            {content.counter_number && (
               <h4>
               {content.counter_symbol && parse(content.counter_symbol)}
              {inView ? (
                <CountUp
                  start={0}
                  end={parseInt(content.counter_number, 10)}
                  duration={2}
                  key={`count_${itemIndex}`}
                />
              ) : (
                content.counter_number || 0
              )}
              <span className={overview.big}>{content.counter_suffix && parse(content.counter_suffix)}</span>
            </h4>
            )}
            {content.counter_title && <p>{parse(content.counter_title)}</p>}
          </li>
          {contentData.length !== 1 && (
          <li className={overview.dot_icon}>
            <Image src="/images/diamond_icon.png" alt="icon" width={25} height={40} />
          </li>
          )}
        </React.Fragment>
      ))}
      {contentData.length % 2 === 0 && (
        <li className={overview.extra_item} data-aos="fade-up" data-aos-duration="1000">
          <h4>
            {contentData.at(-1)?.counter_symbol}
            {contentData.at(-1)?.counter_number || 0}
            <span className={overview.big}>{contentData.counter_suffix && parse(contentData.at(-1)?.counter_suffix)}</span>
          </h4>
          <p>{contentData?.counter_title && parse(contentData.at(-1)?.counter_title || "")}</p>
        </li>
      )}
      
    </ul>
  );

  return (
    <div>
      <section className={`${overview.overview_section} ${overview.pb_40} overview_section`}>
        <div className={`${overview.overview_container} ${comon.wrap} ${comon.pt_100} ${comon.pb_40}`}>
          {/* Section Heading */}
          {head && (
            <div
              className={`${overview.overview_left_sec} ${comon.overview_left_sec} ${comon.mb_30} ${
                tab && overview.overview_left_investor_relation_sec
              }`}
            >
              <h3 data-aos="fade-up" data-aos-duration="1000">{parse(head)}</h3>
            </div>
          )}

          {/* Paragraph Content */}
          <div className={overview.overview_right_sec}>
            {paragraph && <div data-aos="fade-up" data-aos-duration="1000">{parse(paragraph)}</div>}

            {/* Swiper Section */}
            {swiperhead && swiperContents && (
              <div className={`${overview.overview_swiper_sec} ${comon.mt_70}`}>
                <div className={overview.overview_swiper_card}>
                  <div className={`${overview.overview_swiper_card_head_sec} ${comon.overview_swiper_card_head_sec}`}>
                    <h4 data-aos="fade-up" data-aos-duration="1000">{parse(swiperhead)}</h4>
                    <div className={`${overview.overview_swiper_btn_sec} ${comon.overview_swiper_btn_sec}`}>
                      <span className={`${comon.custom_next} ${comon.custom_btn} ${comon.custom_btn_border} overview_prev`}>
                        <Image src="/images/prev_ic.svg" alt="prev" width={11} height={11} />
                      </span>
                      <span className={`${comon.custom_prev} ${comon.custom_btn} ${comon.custom_btn_border} overview_next`}>
                        <Image src="/images/next_ic.svg" alt="next" width={11} height={11} />
                      </span>
                    </div>
                  </div>
                  <Swiper
                    slidesPerView={1}
                    spaceBetween={30}
                    pagination={{ clickable: true }}
                    speed={2000}
                    modules={[Navigation]}
                    navigation={{ nextEl: ".overview_next", prevEl: ".overview_prev" }}
                    breakpoints={{
                      0: { slidesPerView: "auto" },
                      768: { slidesPerView: 1 },
                    }}
                    dir={language === "ar" ? "rtl" : "ltr"}
                    key={language}
                  >
                    {formattedContents.map((contentData, index) => (
                      <SwiperSlide key={index}>{renderSwiperContent(contentData)}</SwiperSlide>
                    ))}
                  </Swiper>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Video Block */}
        {videoBlock && (
          <div className={`${overview.overview_video_container} ${comon.wrap} ${comon.pb_100}`}>
            <div className={overview.overview_video_sec}>
              {videoBlock.map((data, index) => (
                <div className={overview.overview_video_card} key={index}>
                  <div className={overview.overview_video_card_img}>
                    <Image src={data.path} height={400} width={690} alt="" />
                  </div>
                  <h4>{data.description}</h4>
                </div>
              ))}
            </div>
          </div>
        )}
      </section>
    </div>
  );
};

export default Overview;
