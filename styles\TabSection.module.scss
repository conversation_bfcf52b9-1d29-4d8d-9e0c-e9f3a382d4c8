@import "variable", "base";

//   {/* -----------Tab section----------- */}

.tab_section {
  width: 100%;
  background-color: #382999;
  display: flex;
  justify-content: center;
  align-items: center;

  .tab_wrapper {
    &.tab_wrapper_overflowing {
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      ul {
        justify-content: flex-start;
      }
    }
  }

  ul {
    list-style: none;
    justify-content: center;
    display: flex;
    align-items: center;
    height: auto;


    li {
      position: relative;

      a {
        padding: 20px 25px;
        display: block;
        color: rgb(255, 255, 255);
        text-transform: uppercase;
        font-size: 0.938rem;
        line-height: 100%;
        letter-spacing: 0.06em;
        font-weight: 400;
        z-index: 2;
        position: relative;


        &:hover {
          background-color: #2c2174;
          color: #FCB136;
        }

        &.tab_active {
          position: relative;
          color: #FCB136;

          &::after {
            position: absolute;
            content: "";
            top: 100%;
            left: 0;
            height: 5px;
            width: 100%;
            z-index: 5;
            background-color: #459ae0;

            @media #{$media-820} {
              display: none;
            }
          }
        }

        @media #{$media-820} {
          font-size: 14px;
          white-space: nowrap;
        }

        @media #{$media-767} {
          padding: 20px 10px;
        }
      }
    }
  }
}