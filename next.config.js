/** @type {import('next').NextConfig} */

const { i18n } = require("./next-i18next.config");
const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL
const apiHostname = new URL(baseURL).hostname;
 
const ContentSecurityPolicy = ` 
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval' ${apiHostname};
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  img-src 'self' data: https: ${apiHostname};
  font-src 'self' https://fonts.gstatic.com;
  connect-src 'self' ${apiHostname} https://fonts.googleapis.com https://fonts.gstatic.com https://webservices.tadawul.com.sa https://apply.workable.com https://www.youtube.com/ https://www.googletagmanager.com/;
  media-src 'self' blob: https:; 
  frame-src 'self' https://apply.workable.com https://www.youtube.com/ https://www.googletagmanager.com/; 
  object-src 'none';
  base-uri 'self';
  form-action 'self';
`;

const securityHeaders = [
  {
    key: "X-DNS-Prefetch-Control",
    value: "on",
  },
  {
    key: "Strict-Transport-Security",
    value: "max-age=63072000; includeSubDomains; preload",
  },
  {
    key: "X-XSS-Protection",
    value: "1; mode=block",
  },
  {
    key: "X-Frame-Options",
    value: "SAMEORIGIN",
  },
  {
    key: "Permissions-Policy",
    value: "null",
  },
  {
    key: "X-Content-Type-Options",
    value: "nosniff",
  },
  {
    key: "Referrer-Policy",
    value: "origin-when-cross-origin",
  },
  {
    key: "Content-Security-Policy",
    value: ContentSecurityPolicy.replace(/\n/g, ""),
  },
  {
    key: "Access-Control-Allow-Origin",
    value: `${baseURL}`,
  },
];

const nextConfig = {
  // images: {
  //   domains: ["'scontent-iad3-2.cdninstagram.com', 'scontent-iad3-1.cdninstagram.com'"],
  // },
  poweredByHeader: false,
  // reactStrictMode: true,
  images: {
    // domains: ['api.parisima.e8demo.com','scontent-iad3-2.cdninstagram.com', 'scontent-iad3-1.cdninstagram.com'],
    remotePatterns: [
      {
        protocol: "https",
        hostname: apiHostname,
        pathname: "/wp-content/uploads/**",
      },
    ],
  },
  i18n,
  async headers() {
    return [
      {
        // Apply these headers to all routes in your application.
        source: "/:path*",
        headers: securityHeaders,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/sitemap.xml",
        destination: "/sitemap.xml",
      },
      {
        source: "/robots.txt",
        destination: "/robots.txt",
      },
    ];
  },
  async redirects() {
    return [
      {
        source: "/advisory/public-engagement/stakeholder-engagement-programs-to-increase-demand-in-a-sector",
        destination: "/advisory/public-engagement/stakeholder-engagement-programs",
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
