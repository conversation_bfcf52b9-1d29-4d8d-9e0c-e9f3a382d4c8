import React from "react";
import news from "@/styles/NewsCard.module.scss";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import Link from "next/link";
import { parseISO, format } from "date-fns";
import parse from "html-react-parser";
import { useRouter } from "next/router";
  
const DateConvert = (datetimeString) => {
  if (!datetimeString || typeof datetimeString !== "string") {
    console.error("Invalid input for DateConvert:", datetimeString);
    return "Invalid Date";
  }

  try {
    const parsedDate = parseISO(datetimeString);
    const formattedDate = format(parsedDate, "dd-MM-yyyy");
    return formattedDate;
  } catch (error) {
    console.error("Error parsing or formatting date:", error);
    return "Invalid Date";
  }
};
  
const NewsCard = ({ cardData, className, slug, isLinkEnabled = true }) => {
  const router = useRouter();
   const newsurl = router.locale === "ar" ? "/ar" : "";
  return (
    <>
      {cardData && cardData.map((data, index) => (
        <li className={`${news.news_card_body} ${className}`} key={index}  data-aos="fade-up"
        data-aos-duration="1000">
          
          {isLinkEnabled ? (
            
            <Link
              //  href={data.slug ? `/insights/${slug}/${data.slug}` : "#"}
               href={
                  data?.acf?.external_news_link?.url
                    ? data?.acf?.external_news_link?.url
                    : `${newsurl}/insights/${slug}/${data.slug}`
                }
              target={
                        data?.acf?.external_news_link
                          ? data?.acf?.external_news_link?.target
                          : "_self"
                      }
              className={`${news.news_card_link}`}>
              
              <CardContent data={data} />
            </Link>
          ) : (
            <CardContent data={data} />
          )}
        </li>
      ))}
    </>
  );
};

// Component for the card content to avoid duplication
const CardContent = ({ data }) => (
  <>
    {data?.acf?.listing_image && (
      <div className={`${news.news_card_img}`}>
        <Image src={data?.acf?.listing_image?.url} height={240} width={340} alt={""} />
      </div>
    )}
    <div className={`${news.news_card_content} news_card_content`}>
      <div className={`${news.news_card_data} news_card_data`}>
        <div classname={news.date_type}>
        {data.date && <span>{DateConvert(data.date)}</span>}
        {data.acf.listing_tag && <p className={news.notification}>{parse(data.acf.listing_tag)}</p>}
        </div>
      
        {data.title.rendered &&
          <h4>{data.title.rendered && parse(data.title.rendered)}</h4>
        }
      </div>
    </div>
  </>
);

export default NewsCard;
