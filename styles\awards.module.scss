@import "variable", "base";

.service_sec {
    background-color: #F5F3FF;

    h2 {
        color: #2A2656;
        font-size: 3.125rem;


    }

    .awards_top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 70px;

        @media #{$media-767} {
            margin-bottom: 20px;
        }
    }

    .serv_icon {
        width: 132px;
        height: 132px;
        border: 1px solid #5E45FF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        padding: 10px;
        margin: 0 auto;
        background-color: #fff;
    }

    .icon_inner {
        width: 110px;
        height: 110px;
        background-color: #5E45FF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        padding: 10px;

        img {
            @media #{$media-950} {
                width: 70px;
                height: auto;
                margin: 0 auto;
            }
        }
    }

    .serv_title {
        text-align: center;
        min-height: 116px;

        h5 {
            color: #212142;
            font-size: 1.563rem;
            line-height: 1.8rem;
            margin-bottom: 8px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;

            @media #{$media-1440} {
                font-size: 1.863rem;
                line-height: 2.3rem;
            }

            @media #{$media-767} {
                font-size: 2.2rem;
                line-height: 2.5rem;
            }
        }

        p {
            color: #4F4F4F;
            font-size: 1.125rem;

            @media #{$media-1440} {
                font-size: 1.425rem;
            }

            @media #{$media-767} {
                font-size: 1.6rem;
            }
        }
    }

    .service_set {
        display: flex;
        flex-direction: column;
        row-gap: 25px;
    }

    .service_listing {
        display: flex;
        flex-wrap: wrap;
        row-gap: 30px;
        column-gap: 40px;
        padding-inline-start: 16px;

        @media #{$media-1440} {
            padding-inline-start: 13px;
        }

        @media #{$media-500} {
            row-gap: 20px;
            column-gap: 20px;
        }

        >li {
            display: flex;
            align-items: center;
            width: calc(33.33% - 27px);
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.1);

            @media #{$media-1200} {
                width: calc(50% - 27px);
            }

            @media #{$media-500} {
                width: 100%;
            }


            .service_inside {
                padding: 15px;
                padding-inline-start: 0;

                @media #{$media-1200} {
                    padding: 12px;
                    padding-inline-start: 0;
                }

                h5 {
                    color: #5E45FF;
                    font-size: 1.563rem;
                    line-height: 1.8rem;

                    @media #{$media-1440} {
                        font-size: 1.863rem;
                        line-height: 2.3rem;
                    }

                    @media #{$media-767} {
                        font-size: 2.2rem;
                        line-height: 2.5rem;
                    }
                }


            }

            img {
                position: relative;
                inset-inline-start: -16px;
                width: 33px;
                height: auto;

                @media #{$media-1440} {
                    width: 28px;
                    inset-inline-start: -13px;
                }

                @media #{$media-500} {
                    width: 20px;
                    inset-inline-start: -9px;
                }
            }
        }
    }
}

.service_slider {
    &::after {
        content: "";
        width: 76%;
        height: 2px;
        background-color: #5E45FF;
        display: block;
        inset-inline-start: 15px;
        position: absolute;
        bottom: 50%;
        margin: 0 auto;
        inset-inline-end: 0;
    }
}

.digital_solution_sec {
    background-image: url(/images/history_new_bg.png);
    background-repeat: no-repeat;
    background-position: center bottom;
    background-size: cover;
    background-color: rgb(34, 35, 69);
    padding: 110px 0;

    @media #{$media-950} {
        padding: 50px 0;
    }

    h2 {
        color: #fff;
        font-size: 3.125rem;
        margin-bottom: 40px;

        @media #{$media-950} {
            margin-bottom: 20px;
        }
    }

    .solution_set {
        border: 1px solid #fff;
        padding: 60px 80px;
        border-radius: 10px;

        @media #{$media-1024} {
            padding: 40px 30px;
        }

        @media #{$media-767} {
            padding: 25px 20px;
        }

        &+.solution_set {
            margin-top: 30px;
        }

        h3 {
            font-size: 1.875rem;
            color: #fff;
            margin-bottom: 20px;
            font-family: var(--helveticaneueltarabicBold);
            ;

            @media #{$media-767} {
                margin-bottom: 15px;
                font-size: 2rem;
                line-height: 2.7rem;
            }

            @media #{$media-500} {
                font-size: 2.2rem;
                line-height: 2.7rem;

            }

        }

        p {
            color: #fff;
            font-size: 1.25rem;
            font-family: var(--helveticaneueltarabicLight);

            @media #{$media-1440} {
                font-size: 1.5rem;
                line-height: 2rem;

            }

            @media #{$media-767} {
                font-size: 1.5rem;
            }

            @media #{$media-500} {
                font-size: 2rem;
                line-height: 2.6rem;
            }
        }
    }
}

.expert_panel_sec {
    padding: 75px 0 105px 0;

    @media #{$media-1024} {
        padding: 50px 0 85px 0;
    }

    @media #{$media-767} {
        padding: 40px 0;
    }

    h2 {
        font-size: 3.125rem;
        color: #2A2656;

        @media #{$media-820} {
            margin-bottom: 0px;
        }
    }

}

.related_inisights {
    background-color: #F5F3FF;
    padding: 115px 0 100px 0;

    @media #{$media-1024} {
        padding: 50px 0 70px 0;
    }

    @media #{$media-767} {
        padding: 40px 0 50px 0;
    }

    h2 {
        font-size: 3.125rem;
        color: #000000;
        margin-bottom: 40px;

        @media #{$media-767} {
            margin-bottom: 25px;
        }
    }
}

.community_sec {
    background-image: url(/images/community_bg.png);
    background-repeat: no-repeat;
    background-position: center bottom;
    background-size: cover;
    background-color: rgb(34, 35, 69);
    padding: 120px 0;

    @media #{$media-1024} {
        padding: 50px 0 70px 0;
    }

    h2 {
        color: #fff;
        font-size: 3.438rem;
        margin-bottom: 40px;

        @media #{$media-1024} {
            font-size: 3rem;
        }
    }
}

.community_program {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;

    .community_box {
        width: 32%;
        background-color: #5E45FF;
        border-radius: 10px;
        overflow: hidden;

        // padding: 30px 25px 20px 25px;
        @media #{$media-1024} {
            width: 48%;
        }

        @media #{$media-767} {
            width: 100%;
            // padding: 20px;
        }
    }
}

.expert_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40px;

    h2 {
        margin-bottom: 0 !important;
    }

    @media #{$media-1600} {
        margin-bottom: 30px;
    }

    @media #{$media-820} {
        margin-bottom: 20px;
    }
}


//   {/* -----------recognition section----------- */}

.about_recognition_section {
    background: url(/images/dgital_prdct_bg.png);
    background-size: cover;
    background-position: top center;
    background-repeat: no-repeat;
    width: 100%;

    .about_recognition_container {
        .about_recognition_head_sec {
            width: 100%;
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            justify-content: space-between;

            h4 {
                color: #ffffff;
                font-size: 3.4375rem;
                font-weight: 400;

                @media #{$media-700} {
                    width: 100%;
                    margin-bottom: 15px;
                }
            }

            .about_recognition_swiper_btn_sec {
                display: flex;
                gap: 20px;

                span {
                    position: unset;
                    margin: 0;

                    img {
                        width: 50%;
                    }

                    &:hover {
                        border: 1px solid rgb(121, 112, 255);
                    }

                    &:hover img {
                        filter: invert(0) brightness(1.5) !important;
                    }
                }

                @media #{$media-1440} {
                    gap: 15px;
                }

                @media #{$media-768} {
                    gap: 10px;
                }
            }
        }

        .about_recognition_swiper_sec {
            width: 100%;
            margin-top: 50px;
            display: flex;
            flex-wrap: wrap;
            gap: 11px;
            row-gap: 11px;
            list-style: none;

            .about_recognition_swiper_card {
                width: 100%;
                text-align: center;
                padding: 40px 3%;
                box-sizing: border-box;
                border: 1px solid rgba(100, 74, 255, 1);
                border-radius: 15px;
                position: relative;
                min-height: 440px;

                @media #{$media-1600} {
                    min-height: 400px;
                    padding: 25px 3%;
                }

                @media #{$media-1024} {
                    min-height: 390px;
                }

                @media #{$media-820} {
                    min-height: 330px;
                }

                @media #{$media-767} {
                    min-height: 310px;
                }

                .about_recognition_swiper_card_logo {
                    height: auto;
                    width: 60%;
                    margin: auto;
                    min-height: 190px;
                    display: flex;
                    align-items: center;

                    @media #{$media-1024} {
                        min-height: 160px;
                    }

                    img {
                        height: auto;
                        width: 100%;
                        object-fit: contain;
                        display: block;
                        z-index: 5;
                        position: relative;
                    }

                    @media #{$media-500} {
                        width: 50%;
                    }
                }

                .about_recognition_swiper_card_title {
                    h5 {
                        // font-size: 30px;
                        color: #ffffff;
                        font-size: 1.875rem;
                        line-height: 132.54%;
                        font-weight: 400;
                        z-index: 10;
                        position: relative;
                        margin-top: 10px;

                        @media #{$media-767} {
                            font-size: 2.5rem;
                        }
                    }
                }

                p {
                    color: #ffffff;
                    font-size: 20px;
                    line-height: 132.54%;
                    font-weight: 400;
                    z-index: 10;
                    position: relative;
                    padding-left: 50px;
                    padding-right: 50px;

                    @media #{$media-1280} {
                        font-size: 17px;
                    }

                    @media #{$media-820} {
                        font-size: 16px;
                        padding-left: 0;
                        padding-right: 0;
                    }
                }

                &::after {
                    position: absolute;
                    content: "";
                    top: -2px;
                    left: -2px;
                    // background-image: linear-gradient(110deg,
                    //         rgb(33 32 53 / 82%),
                    //         rgba(168, 148, 255, 0));

                    // 90deg, rgba(194, 153, 255, 0.5) 0%, rgba(100, 74, 255, 1) 100%

                    height: calc(100% + 1px);
                    width: 100%;
                    display: block;
                    z-index: 0;
                }
            }
        }
    }
}

.about_recognition_swiper_btn_sec {
    span {
        img {
            width: 34% !important;

            @media #{$media-767} {
                width: auto !important;
            }
        }
    }
}


.awards_slider {
    width: 24% !important;
    height: auto !important;
    display: flex !important;


    @media #{$media-1024} {
        width: 32% !important;
    }

    @media #{$media-820} {
        width: 47% !important;
    }

    @media #{$media-767} {
        width: 99% !important;
    }
}

.loading {
    font-family: var(--helveticaneueltarabicBold);
    color: #ffffff;
    text-align: center;
    font-size: 13px;
    font-weight: 500;
    width: 100%;

    a {
        color: inherit;
        text-decoration: none;
    }
}


.why_section {
    background-image: url(/images/community_bg.png);
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: cover;
    background-color: #222345;

    .why_items {
        display: flex;
        flex-wrap: wrap;

        .why_items_left {
            width: 35%;
            padding-inline-end: 25px;

            @media #{$media-767} {
                width: 100%;
            }

            h3 {
                color: #ffffff;
                font-size: 3.438rem;
                line-height: normal;
                font-family: var(--helveticaneueltarabicLight);

                @media #{$media-767} {
                    font-size: 3rem;
                    margin-bottom: 20px;
                }
            }
        }

        .why_items_right {
            width: 65%;

            @media #{$media-767} {
                width: 100%;
            }

            ul {
                margin-inline-start: 15px;

                li {
                    font-family: var(--helveticaneueltarabicLight);
                    font-size: 1.25rem;
                    line-height: 1.625rem;
                    font-weight: 300;
                    color: #ffffff;
                    padding-inline-start: 15px;
                    position: relative;
                    list-style-image: url('/images/diamond_3.png');

                    @media #{$media-767} {
                        font-size: 1.8rem;
                        line-height: 2.6rem;
                    }
                }

                li+li {
                    margin-top: 10px;
                }
            }
        }
    }

    .why_items+.why_items {
        margin-top: 80px;
        border-top: 1px solid #ffffff;
        padding-top: 80px;

        @media #{$media-1600} {
            margin-top: 60px;
            padding-top: 60px;
        }

        @media #{$media-1024} {
            margin-top: 50px;
            padding-top: 50px;
        }

        @media #{$media-600} {
            margin-top: 40px;
            padding-top: 40px;
        }
    }
}