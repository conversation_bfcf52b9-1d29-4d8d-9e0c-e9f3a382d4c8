@import "variable", "base";










// {/* -----------Announcement section----------- */}

.investor_relation_announcement_section {
    width: 100%;
    background-color: white;

    .investor_relation_announcement_container {
        width: 100%;

        h4 {
            color: rgba(79, 79, 79, 0.7);
            font-size: 25px;
            line-height: 100%;
            font-weight: 400;
            font-family: var(--helveticaNeue);
        }

        .investor_relation_form_field_sec {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            justify-content: space-between;

            .investor_relation_input_sec {
                display: flex;
                flex-wrap: wrap;
                list-style: none;
                width: 85%;
                gap: 3%;

                li {
                    width: 31%;
                    
                    input {
                        width: 100%;
                        display: block;
                        padding: 10px 5%;
                        box-sizing: border-box;
                        background-color: rgb(255, 255, 255);
                        border: 1px solid #dbdbdb;
                        color: rgba(79, 79, 79, 0.7);
                        font-size: 15px;
                        line-height: 28px;
                        font-weight: 300;
                        border-radius: 3px;

                        &:focus {
                            outline: none !important;
                        }
                    }
                }
            }

            .investor_relation_input_btn {
                width: 10%;
                background: #5e45ff;
                border-radius: 50px;
                border: 0;
                cursor: pointer;
                text-transform: uppercase;
                display: inline-block;
                padding: 10px;
                transition: all 0.3s ease;

                &:hover {

                    background: #2f1ea0;

                }
            }
        }
    }
}

// {/* -----------Company Announcement section----------- */}
.investor_resources_company_announcement_section {
    width: 100%;
    background-color: #FFFFFF;
    position: relative;
    .inr_graphics{
        position: absolute;
        right: 0;
        top: 0;
    }
    .investor_resources_company_announcement_container {
        width: 100%;

        h3 {

            color: #2a2656;
            // font-size: 40px;
            font-size: 2.5rem;
            line-height: 100%;
       

            @media #{$media-950} {

                font-size: 3.3rem;

            }

        }

    }
}

.investor_resources_card_block_section {
    background-color: white;

    .investor_resources_card_block_section_container {
        width: 100%;

        h4 {

            font-family: var(--helveticaNeue);
            color: #000000;
            // font-size: 30px;
            font-size: 1.875rem;
            font-weight: 400;

            @media #{$media-950} {
                font-size: 2.9rem;
            }
        }

        .investor_resources_company_announcement_card_sec {
            display: flex;
            flex-wrap: wrap;
            gap: 2%;
            row-gap: 25px;
            

        }
    }
}