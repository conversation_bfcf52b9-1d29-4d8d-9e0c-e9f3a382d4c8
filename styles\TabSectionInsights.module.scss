@import "variable", "base";

//   {/* -----------Tab section----------- */}

// .tab_section_insights {
//     width: 100%;
//     padding: 80px 0 50px 0;
//     background-color: #ffffff;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     @media #{$media-767} {
//         padding: 40px 0 30px 0;
//     }
//     ul {
//         list-style: none;
//         justify-content: center;
//         display: flex;
//         align-items: center;
//         height: auto;
//         column-gap: 1px;
//         @media #{$media-767} {
//             justify-content: flex-start;
//             overflow-x: scroll;
//             &::-webkit-scrollbar{
//                 display: none;
//             }
//         }
//         li {
//             position: relative;

//             a {
//                 padding: 20px 25px;
//                 display: inline-flex;
//                 width: 230px;
//                 height: 55px;
//                 align-items: center;
//                 justify-content: center;
//                 text-transform: uppercase;
//                 color: #4f4f4f;
//                 font-size: 13px;
//                 line-height: 24px;
//                 letter-spacing: 0.06em;
//                 font-weight: 500;
//                 font-family: var(--helveticaNeueMedium);
//                 z-index: 2;
//                 border-radius: 10px;
//                 position: relative;
//                 border: 1px solid #5e45ff;

//                 &:hover {
//                     background-color: #8370ff1f;
//                 }

//                 &.tab_active {
//                     background-color: #5e45ff;
//                     color: white;
//                 }

//                 @media #{$media-820} {
//                     font-size: 14px;
//                 }
//                 @media #{$media-767} {
//                     font-size: 13px;
//                     width: 180px;
//                     height: 45px;
//                     white-space: nowrap;
//                 }
//             }
//         }
//     }
// }
.tab_section_insights {
    width: 100%;
    background-color: #382999;
    display: flex;
    justify-content: center;
    align-items: center;
    .tab_wrapper{
        &.tab_wrapper_overflowing {
            overflow:auto;
        
            &::-webkit-scrollbar {
                display: none;
            }
            ul{
                justify-content: flex-start;
            }
        }
    }
    ul {
        list-style: none;
        justify-content: center;
        display: flex;
        align-items: center;
        height: auto;
        
        

        li {
            position: relative;

            a {
                padding: 20px 25px;
                display: block;
                color: rgb(255, 255, 255);
                text-transform: uppercase;
                font-size: 0.938rem;
                line-height: 100%;
                letter-spacing: 0.06em;
                font-weight: 400;
                z-index: 2;
                position: relative;


                &:hover {
                    background-color: #2c2174;
                    color: #FCB136;
                }

                &.tab_active {
                    position: relative;
                    color: #FCB136;

                    &::after {
                        position: absolute;
                        content: "";
                        top: 100%;
                        left: 0;
                        height: 5px;
                        width: 100%;
                        z-index: 5;
                        background-color: #459ae0;
                        @media #{$media-820} {
                            display: none;
                        }
                    }
                }
                @media #{$media-820} {
                    font-size: 14px;
                    white-space: nowrap;
                  }
                  @media #{$media-767} {
                    padding: 20px 10px;
                  }
            }
        }
    }
}