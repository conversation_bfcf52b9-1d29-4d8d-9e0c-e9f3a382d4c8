@import "variable", "base";

.client_logo_block {
    width: 100%;
    border-radius: 5px;
    border: solid 1px #716b78c4;
    padding: 15%;
    min-height: 154px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;


    @media #{$media-820} {
        min-height: 117px;
    }

    @media #{$media-700} {
        min-height: 155px;
    }

}

.client_sec {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1;

    @media #{$media-767} {
        padding-top: 0;
    }
}

.client_marquee {
    display: flex;
    column-gap: 20px;
    direction: ltr;

    .client_logo_block {
        width: 270px;
        height: 100%;
    }

    *::-webkit-scrollbar {
        display: none;

    }
}