@import "variable", "base";









.latest_publications_section {
    background-color: rgb(255, 255, 255);

    .latest_publications_container {
        width: 100%;

        @media #{$media-1024} {
            padding-bottom: 150px;
        }

        @media #{$media-767} {
            padding-top: 40px;
            padding-bottom: 130px;

        }

        .latest_publications_header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            @media #{$media-820} {
                align-items: center;
            }

            @media #{$media-767} {
                flex-wrap: wrap;
            }

            h3 {
                color: #2a2656;
                font-size: 3.125rem;
                line-height: 100%;
                font-weight: 400;
                display: inline-block;

                @media #{$media-767} {
                    width: 100%;
                    margin-bottom: 15px;
                }
            }
        }

        .latest_publications_swiper_sec {
            width: 100%;
            margin-top: 40px;
            position: relative;
            .latest_publication_swipper{
                display: flex !important;
                height: auto !important;
                >a{
                    width: 100%;
                }
            }

            .latest_publications_swiper_next_btn {
                inset-inline-end: -5%;
                height: 55px;
                width: 55px;
                background-color: transparent;
                @media #{$media-1600} {
                    inset-inline-end: -4%;
                    padding: 1.5%;
                }

                @media #{$media-1024} {
                    background-color: #5e45ff;
                    inset-inline-start: 0;
                    inset-inline-end: -70px;
                    margin: 0 auto;
                    bottom: -80px;
                    top: auto;
                }

                @media #{$media-767} {
                    height: 40px;
                    width: 40px;
                    inset-inline-end: -50px;
                }

                img {
                    @media #{$media-1024} {
                        width: 40%;
                        filter: invert(1);
                    }

                    @media #{$media-820} {
                        width: 25%;

                    }
                }
            }

            .latest_publications_swiper_prev_btn {
                inset-inline-start: -5%;
                height: 55px;
                width: 55px;
                background-color: transparent;
                @media #{$media-1600} {
                    inset-inline-start: -4%;
                    padding: 1.5%;
                }

                @media #{$media-1024} {
                    inset-inline-start: -4%;
                    background-color: #5e45ff;
                    inset-inline-start: -70px;
                    inset-inline-end: 0;
                    margin: 0 auto;
                    bottom: -80px;
                    top: auto;
                }

                @media #{$media-767} {
                    height: 40px;
                    width: 40px;
                    inset-inline-start: -50px;
                }

                img {
                    @media #{$media-1024} {
                        width: 40%;
                        filter: invert(1);
                    }

                    @media #{$media-820} {
                        width: 25%;

                    }
                }
            }

            .latest_publications_swiper_card {
                position: relative;
                z-index: 2;
                border-radius: 10px;
                margin: 1px;
                height: calc(100% - 1px);
                display: flex;
                flex-direction: column;
                padding-bottom: 1px;

                .latest_publications_swiper_card_img {
                    width: 100%;

                    img {
                        object-fit: cover;
                        height: 340px;
                        width: 100%;
                        border-top-left-radius: 10px;
                        border-top-right-radius: 10px;
                        @media #{$media-767} {
                            height: 275px;
    
                        }
                    }
                }

                .latest_publications_swiper_card_data {
                    box-sizing: border-box;
                    padding: 30px 10%;
                    background-color: white;
                    border-bottom-left-radius: 9px;
                    border-bottom-right-radius: 9px;
                    flex-grow: 1;

                    @media #{$media-767} {
                        padding: 25px 5%;

                    }

                    span {
                        color: #474646;
                        font-size: 16px;
                        line-height: 24px;
                        letter-spacing: 0.31px;
                        font-family: var(--helveticaNeue);
                    }

                    p {
                        padding-top: 20px;
                        color: #272727;
                        font-size: 20px;
                        line-height: 28px;
                        font-family: var(--helveticaNeue);
                        min-height: 105px;
                        @media #{$media-767} {
                            font-size: 15px;
                            line-height: 22px;
                            padding-top: 5px;
                            min-height: auto;
                        }
                    }
                }

                &::after {
                    position: absolute;
                    z-index: -1;
                    top: -1px;
                    left: -1px;
                    content: "";
                    width: calc(100% + 2px);
                    height: calc(100% + 2px);
                    background-image: linear-gradient(to bottom,
                            rgba(255, 255, 255, 0) 40%,
                            rgba(171, 163, 223, 1));

                    border-radius: 12px;
                }
            }
        }
    }
}

// ==================================

.news_center_section {
    background-color: #f4f4f4;

    .news_center_container {
        width: 100%;

        @media #{$media-767} {
            padding-top: 40px;
            padding-bottom: 40px;
        }

        .news_center_header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            @media #{$media-820} {
                align-items: center;
            }

            h3 {
                color: #2a2656;
                font-size: 3.125rem;
                line-height: 100%;
                font-weight: 400;
                display: inline-block;
            }
        }

        .news_center_card_sec {
            display: flex;
            margin-top: 40px;
            width: 100%;
            gap: 1.5%;
            row-gap: 20px;

            @media #{$media-820} {
                flex-wrap: wrap;
            }

            .news_center_left_card_sec {
                width: 64%;
                display: flex;

                @media #{$media-820} {
                    width: 100%;
                }

                .news_center_left_card {
                    width: 100%;
                    background-color: white;
                    border-radius: 10px;
                    font-family: var(--helveticaneuelt_arabic_55);
                    position: relative;

                    .news_center_left_card_notification {
                        background: #ffffff;
                        border-radius: 5px;
                        padding: 2px 18px;
                        position: absolute;
                        top: 25px;
                        right: 25px;
                        color: #5b5b5b;
                        font-size: 13px;
                        line-height: 24px;
                        letter-spacing: 0.31px;
                        font-weight: 600;

                        @media #{$media-767} {
                            font-size: 11px;
                        }
                    }

                    .news_center_left_card_img {
                        width: 100%;
                        border-radius: 10px 10px 0 0 ;
                        overflow: hidden;

                        img {
                            height: auto;
                            width: 100%;
                            display: block;
                            object-fit: cover;
                            max-height: 380px;
                        }
                    }

                    .news_center_left_card_data {
                        padding: 30px 5%;

                        span {
                            color: #5b5b5b;
                            font-size: 16px;
                            letter-spacing: 0.31px;
                            font-weight: 300;
                            padding-bottom: 20px;
                            display: block;

                            @media #{$media-767} {
                                padding-bottom: 10px;
                            }
                        }

                        h4 {
                            color: #272727;
                            font-size: 30px;
                            font-weight: 400;
                            padding-bottom: 20px;

                            @media #{$media-767} {
                                font-size: 18px;
                                line-height: 24px;
                                padding-bottom: 10px;
                            }
                        }

                        p {
                            color: #353535;
                            font-size: 18px;
                            font-weight: 400;
                            padding-inline-end: 3%;
                            line-height: 30px;

                            @media #{$media-767} {
                                font-size: 16px;
                                line-height: 25px;

                            }

                        }
                    }
                }
            }

            .news_center_right_card_sec {
                width: 35%;
                margin: -20px 0;

                @media #{$media-820} {
                    width: 100%;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    column-gap: 15px;
                }

                @media #{$media-767} {
                    grid-template-columns: repeat(1, 1fr);
                    margin-top: 2px;
                    row-gap: 15px;
                }

                .news_center_right_card {
                    background-color: white;
                    width: 100%;
                    border-radius: 10px;
                    padding:45px 7%;
                    margin: 20px 0;
                    font-family: var(--helveticaneuelt_arabic_55);
                    position: relative;

                    @media #{$media-820} {
                        padding: 25px 7%;
                    }

                    @media #{$media-767} {
                        margin: 0px 0;
                    }

                    .news_center_right_card_notification {

                        background: #eeeeee;
                        border-radius: 5px;
                        padding: 2px 18px;
                        position: absolute;
                        top: 25px;
                        inset-inline-end: 25px;
                        color: #5b5b5b;
                        font-size: 13px;
                        line-height: 24px;
                        letter-spacing: 0.31px;
                        text-transform: uppercase;
                        font-weight: 600;

                        @media #{$media-820} {
                            position: initial;
                            display: table;
                            margin-left: auto;
                            margin-bottom: 15px;
                            padding: 2px 10px;
                            font-size: 12px;
                        }
                    }

                    span {
                        color: #5b5b5b;
                        font-size: 15px;
                        letter-spacing: 0.31px;
                        font-weight: 300;

                        @media #{$media-820} {
                            font-size: 17px;
                        }
                    }

                    p {
                        padding-top: 15px;

                        color: #272727;
                        font-size: 16px;
                        line-height: 30px;
                        font-weight: 400;
                        padding-bottom: 20px;

                        @media #{$media-820} {
                            font-size: 17px;
                            line-height: 27px;
                            padding-top: 0;
                        }
                    }
                }
            }
        }
    }
}
















// ==================================

.knowledge_center_section {
    background-color: #ffffff;

    .knowledge_center_container {
        width: 100%;

        .knowledge_center_header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            @media #{$media-820} {
                align-items: center;
            }

            h3 {
                color: #2a2656;
                font-size: 3.125rem;
                line-height: 100%;
                font-weight: 400;
                display: inline-block;
            }
        }

        .knowledge_center_card_sec {
            display: flex;
            margin-top: 40px;
            width: 100%;
            gap: 1.5%;
            row-gap: 20px;

            @media #{$media-820} {
                flex-wrap: wrap;
            }

            .knowledge_center_left_card_sec {
                width: 64%;
                display: flex;

                @media #{$media-820} {
                    width: 100%;
                }

                .knowledge_center_left_card {
                    width: 100%;




                    background-image: linear-gradient(to bottom,
                            rgba(255, 255, 255, 0) 40%,
                            rgba(171, 163, 223, 1));








                    border-radius: 10px;
                    font-family: var(--helveticaneuelt_arabic_55);
                    position: relative;
                    z-index: 2;
                    overflow: hidden;



                    .knowledge_center_left_card_img {
                        width: 100%;
                        position: relative;
                        z-index: 2;

                        img {
                            height: auto;
                            width: 100%;
                            display: block;
                            object-fit: cover;
                            max-height: 380px;
                        }
                    }

                    .knowledge_center_left_card_data {
                        padding: 30px 5%;
                        position: relative;
                        z-index: 2;

                        span {
                            color: #5b5b5b;
                            font-size: 16px;
                            letter-spacing: 0.31px;
                            font-weight: 300;
                            padding-bottom: 20px;
                            display: block;

                            @media #{$media-767} {
                                padding-bottom: 10px;
                            }
                        }

                        h4 {
                            color: #272727;
                            font-size: 30px;
                            font-weight: 400;
                            padding-bottom: 20px;

                            @media #{$media-767} {
                                font-size: 18px;
                                line-height: 24px;
                                padding-bottom: 10px;
                            }
                        }

                        p {
                            color: #353535;
                            font-size: 18px;
                            font-weight: 400;
                            padding-inline-end: 3%;
                            line-height: 30px;

                            @media #{$media-767} {
                                font-size: 16px;
                                line-height: 25px;
                            }
                        }
                    }

                    &::after {

                        background-color: white;

                        position: absolute;
                        top: 1px;
                        left: 1px;
                        content: "";
                        width: calc(100% - 2px);
                        height: calc(100% - 2px);

                        border-radius: 10px;
                    }
                }
            }

            // ==========
            .knowledge_center_right_card_sec {
                width: 35%;
                // margin: -20px 0;
                display: flex;
                flex-direction: column;

                @media #{$media-820} {
                    width: 100%;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    column-gap: 15px;
                }

                @media #{$media-767} {
                    grid-template-columns: repeat(1, 1fr);
                    margin-top: 0;
                    row-gap: 15px;

                }

                .knowledge_center_right_card {
                    background-image: linear-gradient(170deg,
                            rgb(241, 239, 255),
                            rgba(171, 163, 223, 1));

                    width: 100%;
                    border-radius: 10px;
                    padding: 40px 7%;
                    
                    font-family: var(--helveticaneuelt_arabic_55);
                    position: relative;
                    z-index: 2;
                    flex-grow: 1;
                    &+.knowledge_center_right_card{
                        margin-top: 20px;
                        @media #{$media-820} {
                            margin-top: 0;
                        }
                    }
                    @media #{$media-1600} {
                        padding: 35px 7%;
                    }
                    @media #{$media-1200} {
                        padding: 30px 7%;
                    }
                    @media #{$media-767} {
                        margin: 0;
                        padding: 25px 5%;

                    }


                    span {
                        color: #5b5b5b;
                        font-size: 15px;
                        letter-spacing: 0.31px;
                        font-weight: 300;
                        z-index: 2;
                        position: relative;

                        @media #{$media-820} {
                            font-size: 16px;
                        }
                    }

                    p {
                        padding-top: 15px;

                        color: #272727;
                        font-size: 16px;
                        line-height: 30px;
                        font-weight: 400;
                        padding-bottom: 20px;
                        z-index: 2;
                        position: relative;
                        @media #{$media-1600} {
                            padding-top: 10px;
                            padding-bottom: 10px;
                        }
                        @media #{$media-820} {
                            font-size: 17px;
                            line-height: 25px;
                            padding-top: 0;
                        }
                    }
                    img {

                        width: 180px;
                        height: 90px;
                        padding-top: 10px;
                       @media #{$media-820} {
                            width: 110px;
                            height: 70px;
                        }
                    }



                    &::after {
                        content: '';
                        position: absolute;
                        top: 1px;
                        left: 1px;
                        width: calc(100% - 2px);
                        height: calc(100% - 2px);
                        background-color: white;

                        border-radius: 9px;
                        z-index: -1;

                    }











                }
            }
        }
    }
}
.banner_sec{
    position: relative;
}