import React, { useState, useEffect } from "react";
import comon from "@/styles/comon.module.scss";
import InnerBanner from "@/component/InnerBanner";
import Yoast from "@/component/yoast";
import {
  getTermspage,  
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";



const Index = (props) => {
  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // ============our team============

  const yoastData = props?.otherData?.yoast_head_json;

  if (!props.otherData) {
    return null;
  }

  return (
    
    <div>
      {/* -----------Banner section----------- */}
      
      {yoastData && <Yoast meta={yoastData} />}

      {props &&
        props?.otherData &&
        props?.otherData?.acf &&
        props?.otherData?.acf?.banner_title &&
        (props?.otherData?.acf?.mob_banner_image ||
          props?.otherData?.acf?.banner_image ||
          props?.otherData?.acf?.banner_viedo ||
          props?.otherData?.acf?.breadcrumbs ||
          props?.otherData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.otherData?.acf?.banner_title}
          breadcrumb1={props?.otherData?.acf?.active_breadcrumbs ==='yes' ? props?.otherData?.acf?.breadcrumbs : '' }          
          background={`${isMobile ? props?.otherData?.acf?.mob_banner_image?.url : props?.otherData?.acf?.banner_image?.url}`}
          videoSrc={props?.otherData?.acf?.banner_viedo?.url}
          banner_type={props?.otherData?.acf?.banner_type}
        />
      ) : null
      }

      {/* -----------Image section----------- */}
      {props &&
        props?.otherData &&      
        props?.otherData?.content &&
        <section         
          className={`${comon.privacy_section}`}
        >
          <div
            className={`${comon.about_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100}`}
            data-aos="fade-up"
            data-aos-duration="1000"
          >
           <div className={comon.privacy_container}>
            {props?.otherData?.content && parse(props?.otherData?.content.rendered)}
            </div>
          </div>
        </section>
      }
     
    </div>
  );
};

export default Index;


export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const otherData = await getTermspage(locale);
  

  
  return {
    props: {
      otherData: otherData || null,      
    },
    revalidate: 10,
  };
};
