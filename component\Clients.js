import React from "react";
import Image from "next/image";
import comon from "@/styles/comon.module.scss";
import clients from "@/styles/clients.module.scss";
import useImageDimensions from './ImageDimensions';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { Autoplay } from 'swiper/modules';

const Clients = () => {
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };

  const images = [
    "/images/cl1.png",
    "/images/cl2.png",
    "/images/cl3.png",
    "/images/cl4.png",
    "/images/cl5.png",
    "/images/cl1.png",
    "/images/cl2.png",
    "/images/cl3.png",
    "/images/cl4.png",
    "/images/cl5.png"
  ];

  return (
    <>
      <div className={`${comon.wrap} ${comon.pt_100} ${comon.pb_50} ${clients.client_sec}`} data-aos="fade-up" data-aos-duration="1000">
        <h2 className={`${comon.h2} ${comon.text_white}`}>Our Clients</h2>
      </div>

      <Swiper
        slidesPerView={2}
        spaceBetween={10}
        loop={true}
        speed={3000} // Slow speed for continuous scroll effect
        autoplay={{
          delay: 0, 
          disableOnInteraction: false,
        }}
        breakpoints={{
          640: {
            slidesPerView: 2,
            spaceBetween: 5,
          },
          768: {
            slidesPerView: 4,
            spaceBetween: 15,
          },
          1200: {
            slidesPerView: 7,
            spaceBetween: 15,
          },
        }}
        modules={[Autoplay]}
        className="mySwiper"
        onSwiper={(swiper) => {
          // Only add event listeners if autoplay is defined
          if (swiper && swiper.autoplay) {
            swiper.el.addEventListener("mouseenter", () => swiper.autoplay?.stop());
            swiper.el.addEventListener("mouseleave", () => swiper.autoplay?.start());
          }
        }}
      >
        {images.map((src, index) => {
          const { width, height } = useImageDimensions(src);

          return (
            <SwiperSlide key={index}>
              <div className={`${clients.client_logo_block}`} data-aos="fade-up" data-aos-duration="1000">
                {width && height ? (
                  <Image
                    className={`${comon.img_mx_fluid} transition_opacity opacity-0`}
                    onLoad={handleLoad}
                    src={src}
                    alt={`client-logo-${index + 1}`}
                    width={width}
                    height={height}
                  />
                ) : null}
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper>
    </>
  );
};

export default Clients;
