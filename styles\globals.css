:root {
  --background: #ffffff;
  --foreground: #171717;
  --marq_height: 48px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}


:root {
  --almarai-arabic: "Almarai", sans-serif;

}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;

  font-size: 16px;

}

a {
  transition: all .4s ease-out 0s;
  -moz-transition: all .4s ease-out 0s;
  -webkit-transition: all .4s ease-out 0s;
  -o-transition: all .4s ease-out 0s;
}


body {

  font-family: var(--helveticaneueltarabicLight);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body.rtl {
  font-family: "Almarai", sans-serif !important;
  font-weight: 400;
  font-style: normal;
}

.rtl h1,
.rtl h2,
.rtl h3,
.rtl h4,
.rtl h5,
.rtl h6,
.rtl p,
.rtl a,
.rtl button,
.rtl span,
.rtl li,
.rtl input,
.rtl textarea,
.rtl table td {
  font-family: "Almarai", sans-serif !important;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

ul {
  list-style: none;
}




h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--helveticaneueltarabicBold);
  line-height: 100%;
  display: inline-block;
}

p {
  color: #4F4F4F;
}

.font70 {
  font-size: 4.375rem !important;
}

.paddingTop370 {
  padding-top: 370px !important;
}

.paddingBottom150 {
  padding-bottom: 150px !important;
}

.swipper_overflow .swiper {
  overflow: unset;
}

.service_slider .swiper-slide:nth-child(2n+2) .serv_title {
  order: 2;
}

.service_slider .swiper-slide:nth-child(2n+2) .service_set {
  transform: translateY(141px);
}

.service_slider .swiper-slide::after {
  content: "";
  background-image: url(../public/images/serv_diamond.png);
  width: 22px;
  height: 33px;
  position: absolute;
  inset-inline-end: -25px;
  bottom: 50%;
  transform: translateY(45%);
}

.service_slider .swiper-wrapper {
  height: 420px;
}

.swiper_custom_arws {
  display: flex;
  column-gap: 15px;
}

.rtl .swiper_custom_arws {
  /* flex-direction: row-reverse; */
}

.rtl .swiper_custom_arws img {
  /* flex-direction: row-reverse; */
  transform: scale(-1);
}

.swiper_custom_arws .swiper_button_prev,
.swiper_custom_arws .swiper_button_next {
  width: 60px;
  height: 60px;
  border: 1px solid #5E45FF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.swiper_custom_arws .swiper-button-disabled {
  opacity: .5 !important;
}

.swiper_custom_arws .swiper-button-lock {
  display: none !important;
}

.swiper_custom_arws .swiper_button_prev_case_study,
.swiper_custom_arws .swiper_button_next_case_study,
.swiper_service_button_prev,
.swiper_service_button_next {
  width: 60px;
  height: 60px;
  border: 1px solid #5E45FF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.swiper_custom_arws .swiper-button-disabled {
  opacity: .5 !important;
}

.swiper_custom_arws .swiper-button-lock {
  display: none !important;
}



#font_size {
  font-size: 100%;

}

.about_overview .overview_container .overview_right_sec {
  width: 89%;
}

.about_overview .overview_right_sec p {
  font-size: 1.25rem !important;
  font-family: var(--helveticaNeue);
}

.over_view_graphic_sec {
  position: relative;
  overflow: hidden;
}

.over_view_vector {
  position: absolute;
  inset-inline-end: 0;
  top: 0;
}

.rtl .over_view_vector img {
  transform: scaleX(-1);
}

.iv_over_view .over_wrap {
  justify-content: space-between;
}

.iv_over_view .over_wrap .overview_left_cl {
  width: 40%;
}

.iv_over_view .over_wrap .overview_right_cl {
  width: 55%;
}

.banner_overlay .inner_banner_section::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg,
      rgba(34, 33, 65, 1) 0%,
      rgba(34, 33, 65, 0) 100%),
    linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.no_border .news_card_content,
.border_none .news_card_content {
  background-color: initial !important;
  padding: 0 !important;
}

.no_border .news_card_data {
  border: 1px solid #CACACA;
  border-top: none;
}

.border_none .news_card_data {
  background-color: #F4F4F4 !important
}

.border_none .news_card_data h4 {
  font-size: 25px !important;
  line-height: 35px !important;
}

.client_slider .swiper-wrapper {
  -webkit-transition-timing-function: linear !important;
  transition-timing-function: linear !important;
}

.client_slider .swiper-slide {
  animation: scroll 30s linear infinite;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

.thumb_swiper .swiper-slide-active {
  opacity: .5;
}

.team_tumb .swiper-slide-thumb-active .about_our_team_content_profile_photo_sec::before {
  content: '';
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  z-index: 3;
  opacity: 0.5;
}

.home_bg_color {
  position: relative;
}

.home_bg_color::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background: url(../public/images/gradient_bg.png);
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
  position: absolute;
  top: 0;
  left: 0;
}

.imapact_sec {
  position: relative;
  z-index: 1;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

video {
  user-select: none;
  /* Prevents text selection */
  -webkit-user-select: none;
  /* For WebKit-based browsers like Chrome, Safari */
  -moz-user-select: none;
  /* For Firefox */
  -ms-user-select: none;
  /* For old Microsoft browsers */
}

video::-webkit-media-controls-fullscreen-button {
  display: none;
}

.form-error {
  color: #ff7e7e;
  font-size: 12px;
  position: absolute;
  left: 15px;
  bottom: 5px;
  display: block;
}

.msg_error .form-error {
  position: initial;
  margin-top: 10px;
}

.inv_form .form-error {
  position: absolute;
  right: 15px;
  left: auto;
  bottom: 13px;
}

.inv_form .msg_error .form-error {
  right: auto;
  left: 15px;
  bottom: -20px;
}

.hm_news_img {
  height: 510px !important;
  object-fit: cover;
}

.filterform {
  width: 100%;
  display: flex;
}

.nav_bar>ul>li>a,
.nav_bar>ul>li>div>a {
  white-space: nowrap;

}

.nav_bar>ul {
  padding-top: 0 !important;
}


.news_slider .swiper-slide {
  height: auto !important;
  display: flex !important;
}

.home_bg_color .custom_prev_1,
.home_bg_color .custom_next_1 {
  padding: 1.5%;
}

.home_bg_color .custom_prev_1 {
  inset-inline-start: 38px;
}

.home_bg_color .custom_next_1 {
  inset-inline-end: 20px;
}

@media (min-resolution: 120dpi) {
  #font_size {
    font-size: 80%;
  }
}

@media only screen and (max-width: 1600px) {

  .home_bg_color .custom_prev_1,
  .home_bg_color .custom_next_1 {
    padding: 1.4%;
  }

  #font_size {
    font-size: 75%;
  }


}

@media only screen and (max-width: 1440px) {
  #font_size {
    font-size: 75%;
  }


}

@media only screen and (max-width: 1024px) {
  #font_size {
    font-size: 75%;
  }

  /* .about_overview .overview_right_sec p{
    font-size: 1.1rem !important;
  } */

  #container_txt {
    min-height: unset;
  }

  .border_none .news_card_data h4 {
    font-size: 18px !important;
    line-height: 25px !important;
  }

  .home_bg_color .custom_prev_1,
  .home_bg_color .custom_next_1 {
    display: none;
  }
}

@media only screen and (max-width: 1000px) {
  #font_size {
    font-size: 70%;
  }
}

@media only screen and (max-width: 820px) {
  .nav_bar {
    width: 100% !important;
    padding-top: 100px;
  }

  .active .nav_bar {
    right: 0;
  }

  .active .toggle_line:first-child {
    transform: rotate(45deg);
    top: 16px;
  }

  .active .toggle_line:nth-child(2) {
    display: none;
  }

  .active .toggle_line:nth-child(3) {
    transform: rotate(130deg);
    top: 16px;
  }

  .active .togle_menu {
    position: fixed;
    top: 20px;
    inset-inline-end: 15px;
  }

  .active header {
    z-index: 999;
    top: 20px !important;
    padding: 0 !important;
  }

  .active .right_block {
    width: 125px;
  }

  .active .digital_header .right_block {
    width: 125px;
  }

  .active .right_block li:first-child {
    z-index: 999;
  }

  .active .nav_bar>ul {
    overflow: auto;
    height: 620px;
    display: block;
    padding-bottom: 90px;
  }

  .hm_news_img {
    height: 380px !important;
  }

  .filterform {
    display: block;
  }
}

@media only screen and (max-width: 767px) {
  #font_size {
    font-size: 70%;
  }

  .about_overview .overview_right_sec p {
    font-size: 1.7rem !important;
  }

  .svg-container {
    width: 118%;
    left: -11%;
    top: -7%;
  }

  .rtl .svg-container {
    left: 7%;
  }

  /* .rtl  .svg-container{} */
  .paddingTop370 {
    padding-top: 190px !important;
  }

  .paddingBottom150 {
    padding-bottom: 100px !important;
  }

  .iv_over_view .over_wrap .overview_left_cl {
    width: 100%;
  }

  .iv_over_view .over_wrap .overview_right_cl {
    width: 100%;
  }

  .swiper_custom_arws .swiper_button_prev,
  .swiper_custom_arws .swiper_button_next {
    width: 35px;
    height: 35px;
  }

  .swiper_custom_arws .swiper_button_prev img,
  .swiper_custom_arws .swiper_button_next img {
    width: 12px;
    height: auto;
  }

  .swiper_custom_arws {
    column-gap: 10px;
  }

  .form-error {
    position: initial;
    margin-top: 7px;
  }

  :root {
    --marq_height: 35px;
  }

  .swiper_custom_arws .swiper_button_prev_case_study,
  .swiper_custom_arws .swiper_button_next_case_study,
  .swiper_service_button_prev,
  .swiper_service_button_next {
    width: 35px;
    height: 35px;

  }

  .swiper_custom_arws .swiper_button_prev_case_study img,
  .swiper_button_next_case_study img,
  .swiper_service_button_prev img,
  .swiper_service_button_next img {
    width: 12px;
    height: auto;
  }
}

@media only screen and (max-width: 600px) {
  #font_size {
    font-size: 60%;
  }


}

@media only screen and (max-width: 500px) {
  #font_size {
    font-size: 50%;
  }
  .active .nav_bar>ul{
    height: 100vh;
  }

}


.swiper-button-disabled {

  opacity: 0.4;
  cursor: default !important;
  /*  position: relative;  */
  /* display: none !important; */
}

/* .swiper-button-disabled:hover {
  background: #fff !important;
  cursor: crosshair !important;
} */
/* 
.swiper-button-disabled:hover img {
  filter: invert(0) !important;
} */



/* 
.swiper-button-disabled::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 800;
  cursor: pointer;
}

.swiper-button-disabled::after:hover {} */


.active_tab {
  background-color: #0070f3;
  color: white;
  border-radius: 4px;
}

.responsiveImage {
  max-width: 100%;
  height: auto;
}

.inquire .inq_form+a:hover {
  background-color: #212035;
  border: 1px solid #212035;
}



.swiper {
  width: 100%;
}

.form-success {
  color: rgb(102, 236, 12);
  margin-top: 15px;
  display: block;
}

.view_btn_style span {
  border: none !important;
}

.service_slider .swiper-slide:nth-child(2n+1) .serv_title {
  transform: translateY(50px);
}

.css-ldg15u {
  box-shadow: none !important;
}

.center_tumb .swiper-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

@media only screen and (max-width: 1200px) {
  .center_tumb .swiper-wrapper {
    justify-content: unset;
    align-items: unset;
  }
}
.downTxt{
   color: #FDB236 !important;
}
.upTxt{
  color: #008000 !important;
}
.rtl .inv_form .form-error {
  right: auto;
  left: 15px;
  font-family: "Almarai", sans-serif !important;
}