@import "variable", "base";


.inner_banner_section {
    width: 100%;
    position: relative;
    overflow: hidden;

    .inner_banner_container {
        width: 100%;
        position: relative;
        padding-top: 345px;
        padding-bottom: 100px;
        z-index: 9;
        height: 690px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;

        @media #{$media-1600} {
            height: 640px;
        }

        @media #{$media-500} {
            height: unset;
        }

        @media #{$media-500} {
            min-height: 340px;
        }

        .inner_banner_insight_ul {

            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            display: flex;

            @media #{$media-1600} {
                padding-left: 3.5%;
            }

            .inner_banner_insight_li {
                cursor: pointer;
                width: 35%;
                padding: 17px 30px 17px 40px;
                border: 1px solid white;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-left: -1px;
                font-size: 20px;

                @media #{$media-950} {
                    width: 48%;
                }

                @media #{$media-767} {
                    padding: 15px 20px;
                    font-size: 15px;
                }

                >div {
                    display: flex;
                    align-items: center;
                }

                .inner_banner_insight_ul_icon {
                    width: 15px;
                    height: auto;
                    transition: all 0.5s ease;

                    img {
                        height: auto;
                        display: block;
                        object-fit: contain;
                        width: 100%;
                    }

                    &.toggle {
                        transform: rotate(180deg);
                    }
                }

                &:nth-child(1) {
                    margin: 0;
                }
            }

            .inner_banner_insight_inner_sec {

                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: #5E45FF;
                z-index: 55;
                padding: 50px 3%;
                display: flex;
                flex-wrap: wrap;
                // margin: 0 -5px;
                border-radius: 20px;

                @media #{$media-1600} {
                    left: 3.5%;
                    width: calc(100% - 6.5%)
                }

                @media #{$media-1024} {
                    border-radius: 10px;
                }

                @media #{$media-950} {
                    width: 93%;
                }

                @media #{$media-767} {
                    padding: 25px 3%;
                    width: 94%;
                }

                li {
                    border: 1px solid white;
                    padding: 10px 20px;
                    width: fit-content;
                    height: fit-content;
                    margin: 5px;
                    color: #fff;

                    a {
                        height: 100%;
                        width: 100%;
                        display: block;
                        font-size: 15px;
                        font-family: var(--helveticaNeueMedium);

                        @media #{$media-767} {
                            font-size: 13px;
                        }
                    }

                    &.active {

                        background-color: #fff;
                        color: #5E45FF;

                    }
                }
            }
        }







        @media #{$media-820} {
            padding-top: 250px;
        }

        @media #{$media-767} {
            padding-top: 130px;
            padding-bottom: 65px;

            // text-align: center;
        }
.inner_banner_date_wrapper{
            margin-top: 20px;
        .inner_banner_date {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 4px 14px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            line-height: 28px;
            font-weight: 400;


            @media #{$media-767} {
                inset-inline-start: 10px;
            }
        }
    }



        .inner_banner_breadcrumb_sec {
            display: flex;
            margin-bottom: 20px;

            @media #{$media-1024} {
                flex-wrap: wrap;
                row-gap: 5px;
                margin-bottom: 10px;
            }

            @media #{$media-767} {
                row-gap: 3px;
            }

            .inner_banner_breadcrumb_head {
                position: relative;

                // white-space: nowrap;
                span {
                    padding: 3px 15px;
                    color: $white;
                    font-size: 16px;
                    font-weight: 500 !important;
                    display: block;
                    font-family: var(--helveticaNeue);
                    text-transform: capitalize;

                    @media #{$media-500} {

                        padding: 0px 10px;
                        font-size: 16px;
                    }
                }

                &::after {
                    position: absolute;
                    content: "";
                    height: 50%;
                    width: 1px;
                    top: 50%;
                    inset-inline-start: -3px;
                    transform: rotate(25deg) translateY(-50%);
                    background-color: rgb(255, 255, 255);

                    @media #{$media-767} {
                        height: 12px;
                        width: 1px;
                        top: 8px;
                    }
                }

                &:nth-child(1)::after {
                    display: none;
                }


                &:nth-child(1) {
                    margin-inline-start: -15px;


                    @media #{$media-500} {
                        margin-inline-start: -10px;

                    }
                }

                a {
                    padding: 3px 15px;
                    color: $white;
                    font-size: 16px;
                    font-weight: 500 !important;
                    display: block;
                    font-family: var(--helveticaNeue);
                    text-transform: capitalize;

                    @media #{$media-500} {

                        padding: 0px 10px;
                        font-size: 16px;
                    }

                }


            }


        }

        h1, h2 {
            color: white;
            // font-size: 80px;
            font-size: 5rem;
            line-height: 100%;
            font-weight: 400;
            max-width: 78%;
            filter: drop-shadow(5px 7px 3px rgba(22, 22, 22, 0.6));
            z-index: 1;

            @media #{$media-1440} {
                max-width: 76%;
            }

            @media #{$media-500} {
                font-size: 5rem;
                padding-top: 0;
                max-width: 80%;
                text-shadow: 0px 2px 3px rgba(0, 0, 0, .6);

            }
            &.share_banner_title{
                max-width: 74%;
                @media #{$media-1440} {
                    max-width: 70%;
                }
                @media #{$media-995} {
                    max-width: 67%;
                }
                @media #{$media-767} {
                    max-width: 100%;
                }
            }
        }

        .inner_banner_share_chart {
            padding: 20px;
            position: absolute;
            inset-inline-end: 0;
            bottom: 15%;
            z-index: 0;
            background-color: #ffffff;
            display: flex;
            align-items: flex-start;
            gap: 10px;
            border-radius: 12px;
            position: absolute;
            inset-inline-end: 0;
            bottom: 15%;
            width: 28%;
            min-width: 300px;

        

        .sharechart_img{
            display: block;
            height: auto;
            width: 100%;
            object-fit: cover;
        }
        @media #{$media-1600} {
            inset-inline-end: 3%;
            padding: 15px;
            width: 25%;
        }

        @media #{$media-767} {
            position: relative;
            inset-inline-end: auto;
            bottom: -15px;
            width: 45%;
        }

        @media #{$media-767} {
            width: 57%;
        }
        @media #{$media-500} {
            width: 100%;
        }

        .diasmond {
            img {
                @media #{$media-1600} {
                    width: 20px;
                }

                @media #{$media-500} {
                    width: 15px;
                }
            }

        }

        .stock_info {
            flex-grow: 1;

            h5 {
                color: #5E45FF;
                font-size: 1.7rem;
                line-height: 2.5rem;
                font-weight: 400;
                display: block;
                @media #{$media-767} {
                    font-size: 2rem;
                line-height: 2.5rem;
                }
                @media #{$media-500} {
                    font-size: 2.5rem;
                line-height: 3rem;
                }
            }

            >p {
                color: #5E45FF;
                font-size: 0.938rem;
                line-height: 100%;
                letter-spacing: 0.06em;
                font-weight: 600;
                @media #{$media-1440} {
                    font-size: 13px;
                }

            }

            .stockData {
                margin-top: 10px;
                font-size: 1.6rem;
                line-height: 2.3rem;
                font-weight: 400;
                display: block;
                display: flex;
                align-items: center;
                gap: 5px;
                @media #{$media-767} {
                    font-size: 2.2rem;
                line-height: 2.7rem;
                }
                @media #{$media-500} {
                    font-size: 2.8rem;
                line-height: 3.3rem;
                }

                &.up {
                    color: #008000;
                }

                &.down {
                    color: #ff0001;
                }

                >span:first-child {
                    color: #000000 !important;
                }
            }
        }

    }
}
}

.background_video {
    // width:700px;
    // height:665px;
    object-fit: cover;
    position: absolute;
    // right: 0;
    // bottom: -50px; 
    // width: 700px;
    // height: 800px;
    // right: -195px;
    // bottom: -165px;
    width: 655px;
    height: 820px;
    inset-inline-end: 0;
    bottom: -180px;
    object-position: center;
    mix-blend-mode: lighten;

    @media #{$media-767} {
        width: 60%;
        height: 100%;
        inset-inline-end: -40px;
        bottom: -60px;
        // width: 100%;
        // height: 100%;
        // right: -118px;
        // bottom: -95px;
    }
}

.inner_banner_section.icon_bg {
    @media #{$media-1024} {
        background-position: bottom right !important;
        background-size: 100% !important;
    }

    @media #{$media-767} {
        background-size: 121% !important;
    }
}

.icon_bg {
    overflow: initial !important;
}

.filtertabs {
    position: relative;
    z-index: 2;
    margin: 0 auto;

    @media #{$media-1024} {
        padding: 0 4%;
    }
}

.filtertabs>ul {
    max-width: 700px;
    margin: 0 -10px;

    @media #{$media-995} {
        max-width: unset;
    }

    @media #{$media-820} {
        margin: 0 -7.5px;
    }

    @media #{$media-400} {
        margin: 0 -5px;
    }

}

.filtertabs>ul>li {
    width: calc(50% - 20px);
    padding: 14px 25px 14px 25px;
    border: 1px solid #5e45ff;
    border-radius: 300px;
    color: #5e45ffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 10px;
    font-size: 20px;
    font-weight: 400;
    cursor: pointer;

    @media #{$media-820} {
        padding: 10px 25px 10px 25px;
        width: calc(50% - 15px);
        margin: 0 7.5px;
    }

    @media #{$media-700} {
        font-size: 15px;
        // padding: 10px 20px 8px 20px;
    }

    @media #{$media-400} {
        width: calc(50% - 10px);
        margin: 0 5px;
    }

    .toggle {
        img {
            transform: rotate(-180deg);
        }
    }
}

.filtertabs>ul>li>ul {
    position: absolute;
    width: 100%;
    max-width: 1400px;
    background-color: #5e45ff;
    z-index: 55;
    padding: 40px 3% 50px 3%;
    left: 0;
    border-radius: 15px;
    top: 55px;
    display: flex;
    flex-wrap: wrap;

    @media #{$media-1024} {
        width: 93%;
        margin: 0 auto;
        right: 0;
    }

    @media #{$media-767} {
        top: 48px;
        padding: 20px 5% 25px 4%;
    }

    li {

        width: -moz-fit-content;
        width: fit-content;
        height: -moz-fit-content;
        height: fit-content;
        margin: 5px;
        color: #fff;

        a {
            width: 100%;
            display: block;
            font-size: 15px;
            padding: 10px 20px;
            border: 1px solid #fff;
            transition: all .3s ease-in-out;

            @media #{$media-767} {
                font-size: 14px;
                padding: 8px 17px;
            }
        }

        &:hover {
            a {
                background-color: #fff;
                color: #000000;
            }
        }


    }
}

.inner_banner_insight_ul_icon img {
    position: relative;
    top: 3px;
    transition: all 0.3s ease-in-out;
}

.text_wrap {
    position: absolute;
    bottom: 200px;
    width: 100%;
}