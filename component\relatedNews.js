import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import styles from '@/styles/relatedNews.module.scss'
import parse from "html-react-parser";

const RelatedNews = ({pic,tag,title,link}) => {
  return (
    <Link href={`${link}`} className={styles.related_news}>
      {pic &&
        <div className={styles.news_pic}>
          <Image src={pic} width={458} height={592} alt='image' />
        </div>
      }
         <div className={styles.news_title}>
        {tag &&
          <span className={styles.tag}>{tag && parse(tag)}</span>
        }
        {title &&
          <h5>{title && parse(title)}</h5>
        }
         </div>
    </Link>
  )
}

export default RelatedNews