@import "variable", "base";

//   {/* -----------Tab section----------- */}

.investor_relation_tab_section {
    width: 100%;
    background-color: #382999;
    display: flex;
    justify-content: center;
    align-items: center;

    ul {
        list-style: none;
        display: flex;
        gap: 50px;

        li {
            a {
                color: rgb(255, 255, 255);
                text-transform: uppercase;
                font-size: 15px;
                line-height: 24px;
                letter-spacing: 0.06em;
                font-weight: 400;

                @media #{$media-820} {
                    font-size: 14px;
                }
            }
        }
    }
}

// {/* -----------Announcement section----------- */}

.investor_relation_announcement_section {
    width: 100%;
    background-color: white;

    .investor_relation_announcement_container {
        width: 100%;

        h4 {
            color: rgba(79, 79, 79, 0.7);
            font-size: 25px;
            line-height: 100%;
            font-weight: 400;
            font-family: var(--helveticaneuelt_arabic_55);
        }

        .investor_relation_form_field_sec {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            justify-content: space-between;

            .investor_relation_input_sec {
                display: flex;
                flex-wrap: wrap;
                list-style: none;
                width: 85%;
                gap: 3%;

                li {
                    width: 31%;
                    
                    input {
                        width: 100%;
                        display: block;
                        padding: 10px 5%;
                        box-sizing: border-box;
                        background-color: rgb(255, 255, 255);
                        border: 1px solid #dbdbdb;
                        color: rgba(79, 79, 79, 0.7);
                        font-size: 15px;
                        line-height: 28px;
                        font-weight: 300;
                        border-radius: 3px;

                        &:focus {
                            outline: none !important;
                        }
                    }
                }
            }

            .investor_relation_input_btn {
                width: 10%;
                background: #5e45ff;
                border-radius: 50px;
                border: 0;
                cursor: pointer;
                text-transform: uppercase;
                display: inline-block;
                padding: 10px;
                transition: all 0.3s ease;
              
                &:hover {

                    background: #2f1ea0;

                }
            }
        }
    }
}

// {/* -----------Company Announcement section----------- */}
.investor_relation_company_announcement_section {
    width: 100%;
    background-color: #f5f3ff;

    .investor_relation_company_announcement_container {
        width: 100%;

        h4 {

            color: #2a2656;
            font-size: 40px;
            line-height: 100%;
            font-weight: 400;
        }

        .investor_relation_company_announcement_card_sec {
            display: flex;
            flex-wrap: wrap;
            gap: 2%;
            row-gap: 20px;

            .investor_relation_company_announcement_card {
                width: 48%;
                box-sizing: border-box;
                padding: 60px 2%;
                border-radius: 10px;
                z-index: 0;

                background-image: linear-gradient(180deg,
                        rgba(186, 178, 234, 0.2) 0%,
                        rgba(171, 163, 223, 1) 100%);
                position: relative;

                span {
                    color: rgba(0, 0, 0, 0.5);
                    font-size: 18px;
                    letter-spacing: 0.05em;
                    font-weight: 400;
                    z-index: 1;
                    font-family: var(--helveticaneuelt_arabic_55);
                    position: relative;
                }

                h5 {
                    color: #000000;
                    text-align: left;
                    font-size: 20px;
                    line-height: 28px;
                    font-weight: 400;
                    font-family: var(--helveticaneuelt_arabic_55);
                    z-index: 1;
                    padding-top: 20px;
                    position: relative;

                }

                &::after {
                    z-index: 0;

                    position: absolute;
                    content: "";
                    height: calc(100% - 2px);
                    width: calc(100% - 2px);
                    top: 1px;
                    left: 1px;
                    background-color: white;
                    border-radius: 9px;
                }
            }
        }
    }
}
