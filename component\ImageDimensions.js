// ImageDimensions.js
import { useState, useEffect } from 'react';

const useImageDimensions = (src) => {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!src) return;

    const img = new Image();
    img.src = src;
    img.onload = () => {
      setDimensions({ width: img.naturalWidth, height: img.naturalHeight });
    };
  }, [src]);

  return dimensions;
};

export default useImageDimensions;
