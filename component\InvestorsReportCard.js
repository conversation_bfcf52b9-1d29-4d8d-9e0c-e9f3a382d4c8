import React from "react";
import style from "@/styles/InvestorsResourcesCard.module.scss";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import parse from "html-react-parser";
const InvestorsReportCard = ({ cardData, className }) => {
  return (
    <>
      {cardData.map((data, index) => (
        <li
          className={`${style.InvestorsResourcesCard} ${className} `}
          key={index}
          data-aos="fade-up"
          data-aos-duration="1000"
        >
        
          {data?.section_title && (
            <h5 className={`${style.description}`} >{data.section_title && parse(data?.section_title)}</h5>
          )}
          {data?.section_link && (
            <Link
              href={data?.section_link?.url }
              // target="_self"
              download
              className={`${style.InvestorsResourcesCard_btn}`}
            >
              {data?.section_link?.title && parse(data?.section_link?.title)}{" "}
              {/* <div className={`${style.InvestorsResourcesCard_btn_icon}`}>
               <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8 12L3 7L4.4 5.55L7 8.15V0H9V8.15L11.6 5.55L13 7L8 12ZM2 16C1.45 16 0.979333 15.8043 0.588 15.413C0.196666 15.0217 0.000666667 14.5507 0 14V11H2V14H14V11H16V14C16 14.55 15.8043 15.021 15.413 15.413C15.0217 15.805 14.5507 16.0007 14 16H2Z"
                    fill="white"
                  />
                </svg> 
              </div> */}
            </Link>
          )}

         
        </li>
      ))}
    </>
  );
};

export default InvestorsReportCard;
