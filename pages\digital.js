import React, { useEffect, useRef, useState } from "react";
import digital from "@/styles/Digital.module.scss";
import comon from "@/styles/comon.module.scss";
import CountUp from "react-countup";
import InnerBanner from "@/component/InnerBanner";
import Overview from "@/component/Overview";
import Link from "next/link";
import Image from "next/image";
import Button from "@/component/buttion/Buttion";
import button from "@/styles/buttion.module.scss";
import InquireNow from "@/component/InquireNow";
import { Fancybox } from "@fancyapps/ui";
import "@fancyapps/ui/dist/fancybox/fancybox.css";
import { useInView } from "react-intersection-observer";

import Yoast from "@/component/yoast";
import {
  getDigitalpage,
  getDigitalPosts,
} from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Digital = (props) => {
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0); // Initial active slider index
  const [selectedCard, setSelectedCard] = useState(null);
  const [isAnimated, setIsAnimated] = useState(false);
  const [formTitle, setFormTitle] = useState(
    props?.DigitalData?.title?.rendered
  ); // Default form title

  const handleLinkClick = (title) => {
    setFormTitle(title || props?.DigitalData?.title?.rendered); // Set the form title
    sidebarsecHide(); // Close the sidebar
    // scroller.scrollTo("inquire", {
    //   smooth: true,
    //   offset: -100, // Adjust for header height if needed
    //   duration: 500,
    // });
  };

  useEffect(() => {
    Fancybox.bind("[data-fancybox]", {});
  }, []);
  const { ref, inView } = useInView({
    triggerOnce: true, // only trigger once
    threshold: 0.5, // adjust based on when you want to start the animation
  });

  useEffect(() => {
    // Set up intersection observer
    const observer = new IntersectionObserver(
      (entries) => {
        // Trigger animation when in view
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stop observing after the animation triggers
        }
      },
      { threshold: 0.3 } // Trigger when 30% of the element is visible
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => {
      if (counterRef.current) observer.unobserve(counterRef.current);
    };
  }, []);

  const handleThumbClick = (index) => {
    setActiveIndex(index);
    runAnimate();
  };

  const handlePrevClick = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? props?.DigitalPostArray?.length - 1 : prevIndex - 1
    );
    runAnimate();
  };

  const handleNextClick = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === props?.DigitalPostArray?.length - 1 ? 0 : prevIndex + 1
    );
    runAnimate();
  };
  const runAnimate = () => {
    setIsAnimated(true);

    // Reset animation after it completes (optional)
    setTimeout(() => {
      setIsAnimated(false);
    }, 1000); // Match this to the duration of the fade-in animation
  };

  // ====================Sidebar section===============

  const [sidebarsec, setSidebarsec] = useState(false);
  const [sidebarContent, setSidebarContent] = useState(0);

  const sidebarsecActive = (e, activeIndex) => {
    const shouldNavigate = false;

    if (!shouldNavigate) {
      e.preventDefault();
      setSidebarsec(true);
      setSidebarContent(activeIndex);
    } else {
      alert("Navigation prevented!");
    }
  };
  const sidebarsecHide = (e) => {
    setSidebarsec(false);
  };

  useEffect(() => {
    if (sidebarsec) {
      document.documentElement.classList.add(comon.overflow_hidden);
    } else {
      document.documentElement.classList.remove(comon.overflow_hidden);
    }

    return () =>
      document.documentElement.classList.remove(comon.overflow_hidden);
  }, [sidebarsec]);

  // ============ Popup Section Start ==========
  const [popupShow, setPopupShow] = useState(false);
  const [popupSec, setPopupSec] = useState(null); // To store the index of the clicked item.
  const popupRef = useRef(null);

  const popupHandler = (index) => {
    if (index >= 0) {
      setPopupSec(index);
      setPopupShow(true);
      document.documentElement.style.overflow = "hidden"; // add class to body
    } else {
      closePopup();
    }
  };

  const closePopup = () => {
    setPopupShow(false);
    setPopupSec(null); // Reset the index.
    document.documentElement.style.overflow = "initial"; // remove class from body
  };

  const whatWeOffer = props?.DigitalData?.acf?.what_we_offer || [];
  // what_we_offer poup code

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        closePopup();
      }
    };

    if (popupShow) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [popupShow]);

  const [isMobile, setIsMobile] = useState(false); // Track if it's mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767); // Set true for screens <= 768px (mobile)
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const yoastData = props?.DigitalData?.yoast_head_json;

  if (!props.DigitalData) {
    return null;
  }

  return (
    <div>
      {/* -----------Banner section----------- */}

      {yoastData && <Yoast meta={yoastData} />}

      {props &&
      props?.DigitalData &&
      props?.DigitalData?.acf &&
      props?.DigitalData?.acf?.banner_title &&
      (props?.DigitalData?.acf?.mob_banner_image ||
        props?.DigitalData?.acf?.banner_image ||
        props?.DigitalData?.acf?.breadcrumbs ||
        props?.DigitalData?.acf?.banner_viedo ||
        props?.DigitalData?.acf?.banner_type) ? (
        <InnerBanner
          pagename={props?.DigitalData?.acf?.banner_title}
          breadcrumb1={
            props?.DigitalData?.acf?.active_breadcrumbs === "yes"
              ? props?.DigitalData?.acf?.breadcrumbs
              : ""
          }
          background={`${
            isMobile
              ? props?.DigitalData?.acf?.mob_banner_image?.url
              : props?.DigitalData?.acf?.banner_image?.url
          }`}
          videoSrc={props?.DigitalData?.acf?.banner_viedo?.url}
          banner_type={props?.DigitalData?.acf?.banner_type}
        />
      ) : null}

      {/* -----------Overview section----------- */}
      {props &&
      props?.DigitalData &&
      props?.DigitalData?.acf &&
      (props?.DigitalData?.acf?.overview_title ||
        props?.DigitalData?.acf?.overview_content) ? (
        <div className="over_view_graphic_sec">
          <div className="over_view_vector">
            <Image
              src="/images/over_view_vector.png"
              alt="image"
              width={197}
              height={691}
            />
          </div>
          {/* <Overview
            head={props?.DigitalData?.acf?.overview_title}
            paragraph={props?.DigitalData?.acf?.overview_content} 
                       
          /> */}
          <Overview
            head={props?.DigitalData?.acf?.overview_title}
            paragraph={props?.DigitalData?.acf?.overview_content}
            swiperhead={props?.DigitalData?.acf?.engagement_in_numbers_title}
            swiperContents={props?.DigitalData?.acf?.engagement_in_numbers}
          />
        </div>
      ) : null}

      {/* -----------What We Offer section----------- */}
      {/* {props &&
             props?.DigitalData &&
                props?.DigitalData?.acf &&
                (props?.DigitalData?.acf?.engagement_in_numbers_title || 
                        props?.DigitalData?.acf?.engagement_in_numbers) ? (
           
            <section className={digital.counter_sec}>
                        <div className={`${comon.wrap}`}>
                            {props?.DigitalData?.acf &&
                                props?.DigitalData?.acf?.engagement_in_numbers_title &&
                            <h2
                            data-aos="fade-up"
                            data-aos-duration="1000">{props?.DigitalData?.acf?.engagement_in_numbers_title && parse(props?.DigitalData?.acf?.engagement_in_numbers_title)}</h2>
                            }
                            
                    <ul className={digital.counter_list} ref={ref}>
                                {props?.DigitalData?.acf?.engagement_in_numbers &&
                                    props?.DigitalData?.acf?.engagement_in_numbers?.map((content, index) => (
                        <li key={index}>
                            <div className={digital.count_set}>
                                  <span className={digital.plus}>{content.counter_symbol}</span>
                                  {content.counter_number &&
                                      <span className={digital.count}>                                          
                                        {inView ? (
                                        <CountUp
                                          start={0}
                                          end={parseInt(content.counter_number, 10)}
                                          duration={2}                                          
                                        />
                                      ) : (
                                        content.counter_number || 0
                                      )}
                                      </span>
                                  }
                                  {content.counter_suffix &&
                                      <span className={digital.suffix}>{content.counter_suffix}</span>
                                  }
                              </div>
                              {content.counter_title &&
                                  <h5>{content.counter_title && parse(content.counter_title)}</h5>
                              }   
                        </li>
                       ))}
                    </ul>
                </div>
            </section>
            ) : null} */}
      <div
        style={{
          background: "url(../images/dgital_prdct_bg.png) top left no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "top center",
        }}
      >
        {props &&
        props?.DigitalData &&
        props?.DigitalData?.acf &&
        (props?.DigitalData?.acf?.what_we_offer_title ||
          props?.DigitalData?.acf?.what_we_offer_content ||
          props?.DigitalData?.acf?.what_we_offer) ? (
          <section className={`${digital.digital_what_we_offer_section}`}>
            <div
              className={`${digital.digital_what_we_offer_container} ${comon.digital_what_we_offer_container} ${comon.wrap} ${comon.pt_130} ${comon.pb_130}`}
            >
              {props?.DigitalData?.acf?.what_we_offer_title && (
                <h3 data-aos="fade-up" data-aos-duration="1000">
                  {props?.DigitalData?.acf?.what_we_offer_title &&
                    parse(props?.DigitalData?.acf?.what_we_offer_title)}
                </h3>
              )}
              {props?.DigitalData?.acf?.what_we_offer_content && (
                <div data-aos="fade-up" data-aos-duration="1000">
                  {props?.DigitalData?.acf?.what_we_offer_content &&
                    parse(props?.DigitalData?.acf?.what_we_offer_content)}
                </div>
              )}
              <ul className={`${digital.digital_what_we_offer_card_sec}`}>
                {props?.DigitalData?.acf?.what_we_offer &&
                  props?.DigitalData?.acf?.what_we_offer.map((data, index) => (
                    <li
                      key={index}
                      onClick={() => popupHandler(index)}
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      <div
                        className={`${digital.digital_what_we_offer_card_logo_sec}`}
                      >
                        {data.what_we_image && (
                          <div
                            className={`${digital.digital_what_we_offer_card_logo}`}
                          >
                            <Image
                              src={data.what_we_image?.url}
                              height={110}
                              width={110}
                              alt=""
                            />
                          </div>
                        )}
                      </div>
                      {data.what_we_title && (
                        <div
                          className={`${digital.digital_what_we_offer_card_footer_sec}`}
                        >
                          <h4>
                            {data.what_we_title && parse(data.what_we_title)}
                          </h4>
                        </div>
                      )}
                    </li>
                  ))}
              </ul>

              {/* Popup section */}
              {popupShow && popupSec !== null && whatWeOffer[popupSec] && (
                <div
                  className={`${
                    digital.digital_what_we_offer_card_popup_container
                  } ${popupShow ? digital.popup_active : ""}`}
                >
                  <div
                    ref={popupRef}
                    className={digital.digital_what_we_offer_card_popup_sec}
                  >
                    <div
                      className={`${digital.digital_what_we_offer_card_popup_close} ${comon.digital_what_we_offer_card_popup_close}`}
                      onClick={closePopup}
                    >
                      <Image
                        src={"/images/close-icon.png"}
                        height={50}
                        width={50}
                        alt="Close popup"
                      />
                    </div>

                    <div
                      className={digital.digital_what_we_offer_card_popup_title}
                    >
                      <h4 data-aos="fade-up" data-aos-duration="1000">
                        {parse(whatWeOffer[popupSec].what_we_title)}
                      </h4>
                    </div>

                    <div
                      className={
                        digital.digital_what_we_offer_card_popup_content
                      }
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      {parse(whatWeOffer[popupSec].what_we_content)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </section>
        ) : null}

        {props &&
        props?.DigitalData &&
        props?.DigitalData?.acf &&
        (props?.DigitalData?.acf?.our_digital_products_title ||
          props?.DigitalData?.acf?.our_digital_products_content ||
          props?.DigitalData?.acf?.our_digital_products) ? (
          <section className={digital.digital_product}>
            <div className={`${comon.wrap}`}>
              <div
                className={digital.digital_top}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                {props?.DigitalData?.acf?.our_digital_products_title && (
                  <h2>
                    {props?.DigitalData?.acf?.our_digital_products_title &&
                      parse(
                        props?.DigitalData?.acf?.our_digital_products_title
                      )}
                  </h2>
                )}
                {props?.DigitalData?.acf?.our_digital_products_content &&
                  parse(props?.DigitalData?.acf?.our_digital_products_content)}
              </div>

              <div
                className={digital.digi_slide_sec}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <div className={digital.digi_main_slider}>
                  <div
                    className={`${digital.digi_slider} ${comon.digi_slider}`}
                  >
                    <div className={digital.main_slider_pic}>
                      <Image
                        src={
                          props?.DigitalPostArray[activeIndex]?.acf?.dis_image
                            ?.url
                        }
                        height={282}
                        width={438}
                        alt="Slider Image"
                        className={`${
                          isAnimated ? digital.fade_in_opacity : ""
                        } `}
                      />
                    </div>
                    <div
                      className={`${digital.main_slider_text} ${
                        isAnimated ? digital.fade_in_opacity : ""
                      }`}
                    >
                      <h3>
                        {props?.DigitalPostArray[activeIndex]?.title?.rendered}{" "}
                        <br />
                        {
                          props?.DigitalPostArray[activeIndex]?.acf
                            ?.dis_sub_text
                        }
                      </h3>
                      {props?.DigitalPostArray[activeIndex]?.acf
                        ?.short_description &&
                        parse(
                          props?.DigitalPostArray[activeIndex]?.acf
                            ?.short_description
                        )}

                      {/* ====Sidebar section Button===== */}
                      {props?.DigitalPostArray[activeIndex]?.acf
                        ?.learn_more && (
                        <button
                          aosType="fade-up"
                          aosDuration={1500}
                          onClick={(e) => sidebarsecActive(e, activeIndex)}
                          className={`${digital.learnmore_btn} ${comon.learnmore_btn}`}
                        >
                          {props?.DigitalPostArray[activeIndex]?.acf
                            ?.learn_more &&
                            parse(
                              props?.DigitalPostArray[activeIndex]?.acf
                                ?.learn_more
                            )}
                          <div>
                            <Image
                              src={"/images/right_blk_arw.svg"}
                              height={40}
                              width={40}
                              className={`${digital.learnmore_btn_img} ${comon.learnmore_btn_img}`}
                              alt=""
                            />
                          </div>
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="slider-arrows">
                    <button
                      onClick={handlePrevClick}
                      className={`${digital.arrow_left} ${digital.digi_arws} ${comon.digi_arws}`}
                    >
                      <Image
                        src="/images/digi_left.svg"
                        height={11}
                        width={11}
                        alt="Slider Image"
                      />
                    </button>
                    <button
                      onClick={handleNextClick}
                      className={`${digital.arrow_right} ${digital.digi_arws} ${comon.digi_arws}`}
                    >
                      <Image
                        src="/images/digi_right.svg"
                        height={11}
                        width={11}
                        alt="Slider Image"
                      />
                    </button>
                  </div>
                </div>

                {/* Arrows */}

                {/* Thumbnail List */}
                {props?.DigitalPostArray && (
                  <div className={digital.thumb_list_wrap}>
                    <ul className={digital.thumb_list}>
                      {props?.DigitalPostArray &&
                        props?.DigitalPostArray.map((item, index) => (
                          <li
                            key={index}
                            onClick={() => handleThumbClick(index)}
                            className={`${
                              activeIndex === index ? digital.active : ""
                            } ${digital.thumb_item}`}
                          >
                            {item?.acf?.dis_image && (
                              <Image
                                src={item?.acf?.dis_image?.url}
                                height={80}
                                width={80}
                                alt={`Thumbnail ${index + 1}`}
                              />
                            )}
                          </li>
                        ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {/* ================side content shown===================== */}

            <div
              className={`${digital.digital_side_bar_container} ${
                sidebarsec ? digital.digital_side_bar_container_active : ""
              }  ${sidebarsec ? comon.overflow_scroll : ""}`}
              onClick={sidebarsecHide}
            >
              <div
                className={`${digital.digital_side_bar_section}  `}
                onClick={(event) => event.stopPropagation()}
              >
                <div
                  className={digital.digital_what_we_offer_card_popup_close}
                  onClick={sidebarsecHide}
                >
                  <Image
                    src={"/images/close-icon.png"}
                    height={50}
                    width={50}
                    alt=""
                  />
                </div>

                <div className={digital.digital_side_top_section}>
                  <div className={digital.digi_side_bar_head}>
                    <h3>
                      {props?.DigitalPostArray[sidebarContent]?.title?.rendered}
                    </h3>
                    <span>
                      {
                        props?.DigitalPostArray[sidebarContent]?.acf
                          ?.dis_sub_text
                      }
                    </span>
                    {props?.DigitalPostArray[sidebarContent]?.acf?.content &&
                      parse(
                        props?.DigitalPostArray[sidebarContent]?.acf?.content
                      )}
                  </div>
                  {props?.DigitalPostArray[
                    sidebarContent
                  ]?.acf?.listing_content.map((list, index) => (
                    <div className={digital.digi_side_bar_content} key={index}>
                      <h4>{list.title}</h4>
                      {list.layout === "listing" ? (
                        <>
                          {list.listing_type && (
                            <ul className={digital.digi_side_bar_first_ul}>
                              {list.listing_type &&
                                list.listing_type.map(
                                  (listing_type, lindex) => (
                                    <li key={lindex}>
                                      <span>
                                        {String(lindex + 1).padStart(2, "0")}
                                      </span>
                                      <h5>
                                        {listing_type.list_title &&
                                          parse(listing_type.list_title)}
                                      </h5>
                                      {listing_type.list_content &&
                                        parse(listing_type.list_content)}
                                    </li>
                                  )
                                )}
                            </ul>
                          )}
                        </>
                      ) : (
                        <>
                          {list?.box_type && (
                            <ul
                              className={digital.digi_side_bar_functionality_ul}
                            >
                              {list?.box_type &&
                                list?.box_type.map((box_type, bindex) => (
                                  <li key={bindex}>
                                    <p>
                                      {box_type?.box_text &&
                                        parse(box_type?.box_text)}
                                    </p>
                                  </li>
                                ))}
                            </ul>
                          )}
                        </>
                      )}
                    </div>
                  ))}
                </div>
                {props?.DigitalPostArray[sidebarContent]?.acf
                  ?.request_a_demo_title && (
                  <div className={digital.digi_side_bar_footer}>
                    <h4>
                      {props?.DigitalPostArray[sidebarContent]?.acf
                        ?.request_a_demo_title &&
                        parse(
                          props?.DigitalPostArray[sidebarContent]?.acf
                            ?.request_a_demo_title
                        )}
                    </h4>
                    {props?.DigitalPostArray[sidebarContent]?.acf
                      ?.request_a_demo_button && (
                      <Link
                        href="#inquire"
                        className={`${digital.digi_side_bar_footer_btn} ${comon.digi_side_bar_footer_btn}`}
                        onClick={() =>
                          handleLinkClick(
                            props?.DigitalPostArray[sidebarContent]?.title
                              ?.rendered
                          )
                        }
                      >
                        {props?.DigitalPostArray[sidebarContent]?.acf
                          ?.request_a_demo_button &&
                          parse(
                            props?.DigitalPostArray[sidebarContent]?.acf
                              ?.request_a_demo_button?.title
                          )}
                        <Image
                          src="/images/right_blk_arw.svg"
                          alt="arrow"
                          width={13}
                          height={13}
                        />
                      </Link>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* ================side content show end===================== */}
          </section>
        ) : null}
      </div>

      <InquireNow formtitle={formTitle} />
    </div>
  );
};

export default Digital;

export const getStaticProps = async (locale) => {
  // const { getHome } = await usePublicServices();
  const DigitalData = await getDigitalpage(locale);

  const query = "";
  const DigitalPostData = await getDigitalPosts(locale, query);

  let DigitalPosts = [];
  if (
    DigitalData &&
    DigitalData?.acf &&
    Array.isArray(DigitalData?.acf?.our_digital_products)
  ) {
    DigitalPosts = DigitalData?.acf?.our_digital_products;
  }

  // Format Investors Resources  for use in the component

  let DigitalPostArray = [];
  if (DigitalPosts.length > 0) {
    DigitalPostArray = DigitalPosts.map((id) =>
      DigitalPostData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

  //  console.log('testingdata', locale)

  return {
    props: {
      DigitalData: DigitalData || null,
      DigitalPostArray: DigitalPostArray || [],
    },
    revalidate: 10,
  };
};
