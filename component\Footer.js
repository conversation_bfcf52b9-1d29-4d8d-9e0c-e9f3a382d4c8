import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/router";
import comon from "@/styles/comon.module.scss";
import footer from "@/styles/footer.module.scss";
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
// fancy-box---------import--

const Footer = (props) => {

  const [options, setOptions] = useState(null);
  const router = useRouter();

  useEffect(() => {
   // console.log("Fetching options...");
    const fetchMyAcfOptions = async (locale) => {
      try {
        const footerPostsData = await getThemeoptions(locale);
        //console.log("Fetched options:", footerPostsData);
        setOptions(footerPostsData);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };
    fetchMyAcfOptions(router.locale);
  }, [router]);
  
  const [isMobile, setIsMobile] = useState(false);
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };
  //---------Setting language ------ end
  useEffect(() => {
    // Function to check the screen width
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 767); // Adjust breakpoint as needed
    };

    // Initial check
    checkScreenSize();

    // Add resize listener
    window.addEventListener("resize", checkScreenSize);

    // Cleanup listener on unmount
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  if (!options) {
    return null;
  }

  return (
    <>
      <footer className={`${footer.footer} ${comon.pt_70} ${comon.pb_60}`}  >
        <div className={`${comon.wrap}`} >
          <ul className={`${footer.footer_cl_ul}`} >
            <li className={`${footer.footer_cl_01}`} >
              <div className={`${comon.w_100}`}>
                <div className={`${footer.footer_logo} ${footer.pb_40}`}>
                  <Image
                    className={`${comon.img_mx_fluid}   transition_opacity opacity-0`}
                    onLoad={handleLoad}
                    src={options?.footer_logo?.url || "/images/tam_logo.svg"}
                    alt="call"
                    width={146}
                    height={53}
                  />
                </div>
                {options?.footer_menu &&
                  <ul className={`${comon.d_flex_wrap} ${footer.footer_link}`} >
                    {options?.footer_menu && options?.footer_menu.map((footermeu, findex) => (
                      <li key={findex}>
                        {footermeu?.footer_menu &&
                          <Link href={footermeu?.footer_menu?.url} target={footermeu?.footer_menu?.target}>{footermeu?.footer_menu?.title && parse(footermeu?.footer_menu?.title)}</Link>
                        }
                      </li>
                    ))}                 
                  </ul>
                }

                <div className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.align_items_center}  ${comon.mt_30}`}>
                  {options?.social_media &&
                    <ul className={`${footer.social_media}`} >
                      {options?.social_media && options?.social_media.map((social, sindex) => (
                        <li key={sindex}>
                          {social.social_icon &&
                            <Link href={social.social_link || '#.'} target="_blank">
                              <Image
                                className={`transition_opacity opacity-0`}
                                onLoad={handleLoad}
                                src={`/images/${social.social_icon}.svg`}
                                alt="call"
                                width={23}
                                height={23}
                              />
                            </Link>
                          }
                        </li>
                      ))}
                   

                    </ul>
                  }
                  {(options?.fd_phone || options?.fd_email) && (
                    <ul className={`${comon.d_flex_wrap} ${comon.align_items_center} ${footer.footer_contact} ${footer.footer_emaillist} ${comon.footer_contact}`}>
                      {options?.fd_phone &&
                        <li>

                          <Image
                            className={`${footer.contact_icn}   transition_opacity  `}

                            src="/images/phone.svg"
                            alt="call"
                            width={18}
                            height={18}
                          />

                          <Link href={`tel:${options?.fd_phone}`}>{options?.fd_phone}</Link></li>
                      }
                      {options?.fd_email &&
                        <li>
                          <Image
                            className={`${footer.contact_icn}   transition_opacity  `}

                            src="/images/email.svg"
                            alt="call"
                            width={18}
                            height={18}
                          />
                          <Link href={`mailto:${options?.fd_email}`}>{options?.fd_email}</Link></li>
                      }
                      {options?.fd_email_2 &&
                        <li>
                          <Image
                            className={`${footer.contact_icn}   transition_opacity  `}

                            src="/images/email.svg"
                            alt="call"
                            width={18}
                            height={18}
                          />
                          {options?.fd_email_2 && <p>{options?.fd_email_text && parse(options?.fd_email_text)}
                            <Link href={`mailto:${options?.fd_email_2}`}>{options?.fd_email_2}</Link>
                            </p>}
                          </li>
                      }
                    </ul>
                  )}
                </div>
              </div>
            </li>
            {options?.address && options?.address.map((address, aindex) => (
              <li className={`${footer.footer_cl_02}`} key={aindex}>
                {address?.address_title &&
                  <h5>{parse(address?.address_title)}</h5>
                }
                {address?.address_title &&
                  <p>{parse(address?.address_content)}
                  </p>
                }

              </li>
            ))}
            
          </ul>


          <div className={`${comon.w_100} ${comon.d_flex_wrap} ${footer.footer_row_2}  ${comon.mt_20} ${comon.pt_30} `}>
         {options?.copyright &&
            <div className={`${comon.mr_auto} ${footer.bot_row} ${footer.foot_copy}`}>
              <p>{options?.copyright && parse(options?.copyright)}</p>
          </div>
}
            {options?.privacy_menu &&
              <div className={`${comon.ml_auto} ${footer.bot_row} ${footer.foot_bot_links}`}>
                <ul className={`${footer.policy_link_ul}`}>
                  {options?.privacy_menu && options?.privacy_menu.map((privacy, pindex) => (
                    <li key={pindex}>
                      {privacy?.privacy_sub_menu &&
                        <Link href={privacy?.privacy_sub_menu?.url} target={privacy?.privacy_sub_menu?.target}>{privacy?.privacy_sub_menu?.title && parse(privacy?.privacy_sub_menu?.title)}</Link>
                      }
                    </li>
                  ))}
                
                </ul>
              </div>
            }
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
