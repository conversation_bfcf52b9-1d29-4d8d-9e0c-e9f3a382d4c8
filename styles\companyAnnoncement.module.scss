@import "variable", "base";



// {/* -----------Announcement section----------- */}

.share_info_announcement_section {
    width: 100%;
    background-color: white;

    .share_info_announcement_container {
        width: 100%;

        h4 {
            color: rgba(79, 79, 79, 0.7);
            font-size: 25px;
            line-height: 100%;
            font-weight: 400;
            font-family: var(--helveticaneuelt_arabic_55);
        }

        .share_info_form_field_sec {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            justify-content: space-between;

            .share_info_input_sec {
                display: flex;
                flex-wrap: wrap;
                list-style: none;
                width: 89%;
                gap: 2%;

                @media #{$media-820} {
                    width: 100%;
                    row-gap: 15px;

                }

                li {
                    width: 31%;
                    border: 1px solid #dbdbdb;
                    border-radius: 3px;
                    position: relative;
                    display: flex;
                    justify-content: flex-start;

                    @media #{$media-820} {
                        width: 49%;
                    }

                    @media #{$media-700} {
                        width: 100%;
                    }

                    &:first-child {
                        @media #{$media-820} {
                            width: 100%;
                        }
                    }

                    input {
                        width: 100%;
                        display: block;
                        padding: 10px 5%;
                        box-sizing: border-box;
                        background-color: rgb(255, 255, 255);
                        color: rgba(79, 79, 79, 0.7);
                        border: none;
                        font-size: 15px;
                        line-height: 100%;
                        font-weight: 300;
                        appearance: none;
                        font-family: var(--helveticaNeue);
                        height: 54px;

                        &::placeholder {
                            color: rgba(79, 79, 79, 0.7);
                        }

                        @media #{$media-820} {
                            padding-left: 15px;
                            font-size: 16px;
                        }

                        @media #{$media-767} {
                            width: 100%;
                        }

                        &:focus {
                            outline: none !important;
                        }
                    }

                    &.date_li {
                        display: block;

                        .date_feild {
                            appearance: none !important;
                            opacity: 0;
                            text-align: start;

                            &:focus {
                                opacity: 1;
                            }

                            &.active {
                                opacity: 1;
                            }

                        }

                        .input_placeholder {
                            position: absolute;
                            top: 50%;
                            inset-inline-start: 12px;
                            transform: translateY(-50%);
                            color: rgba(79, 79, 79, 0.7);
                            pointer-events: none;
                            /* Prevents blocking input click */
                            font-size: 15px;
                            line-height: 100%;
                            font-weight: 300;

                            @media #{$media-820} {
                                font-size: 16px;
                            }
                        }

                        input[type="date"]:focus+.input_placeholder {
                            display: none;
                        }
                    }
                }
            }

            .share_info_input_btn {
                width: 10%;
                background: #5e45ff;
                border-radius: 50px;
                border: 0;
                cursor: pointer;
                text-transform: uppercase;
                display: inline-block;
                padding: 10px;
                transition: all 0.3s ease;
                color: #fff;
                font-family: var(--helveticaNeue);
                font-weight: 400;

                @media #{$media-820} {
                    width: 49%;
                    margin-top: 20px;
                    height: 60px;
                    font-size: 18px;
                }

                @media #{$media-767} {
                    height: 50px;
                    font-size: 14px;
                }

                &:hover {

                    background: #2f1ea0;

                }

                &:last-child {
                    margin-inline-start: 5px;
                }
            }
        }
    }
}

// {/* -----------Company Announcement section----------- */}
.share_info_company_announcement_section {
    width: 100%;
    background-color: #f5f3ff;

    .share_info_company_announcement_container {
        width: 100%;

        h4 {

            color: #2a2656;
            font-size: 40px;
            line-height: 100%;
            font-weight: 400;

            @media #{$media-700} {
                font-size: 25px;
            }
        }

        .share_info_company_announcement_card_sec {
            display: flex;
            flex-wrap: wrap;
            gap: 2% 3.5%;
            row-gap: 20px;

            .share_info_company_announcement_card {
                width: 48%;
                box-sizing: border-box;
                padding: 60px 2%;
                border-radius: 10px;
                z-index: 0;

                @media #{$media-700} {
                    width: 100%;
                    padding: 30px 6%;
                }

                background-image: linear-gradient(180deg,
                    rgba(186, 178, 234, 0.2) 0%,
                    rgba(171, 163, 223, 1) 100%);
                position: relative;

                span {
                    color: rgba(0, 0, 0, 0.5);
                    font-size: 18px;
                    letter-spacing: 0.05em;
                    font-weight: 400;
                    z-index: 1;
                    font-family: var(--helveticaNeue);
                    position: relative;

                    @media #{$media-700} {
                        font-size: 16px;
                    }
                }

                h5 {
                    color: #000000;
                    text-align: start;
                    font-size: 20px;
                    line-height: 28px;
                    font-weight: 400;
                    font-family: var(--helveticaNeue);
                    z-index: 1;
                    padding-top: 20px;
                    position: relative;

                    @media #{$media-1024} {
                        font-size: 18px;
                    }

                    @media #{$media-767} {
                        font-size: 17px;
                        line-height: 25px;
                    }
                }

                &::after {
                    z-index: 0;

                    position: absolute;
                    content: "";
                    height: calc(100% - 2px);
                    width: calc(100% - 2px);
                    top: 1px;
                    left: 1px;
                    background-color: white;
                    border-radius: 9px;
                }
            }

        }

        .loading {
            font-family: var(--helveticaneueltarabicBold);
            ;
            color: #353a3f;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
        }


    }
}