@import "variable", "base";
 .footer{
  background: #212035;
  .footer_cl_ul{
     display: flex; flex-wrap: wrap;
    li{ list-style: none;  margin-top: 2px; padding-bottom: 2px;
      @media #{$media-767} {
        width: 50%;
      }
      &.footer_cl_01{ width: 52.2%; padding-inline-end: 2%;flex-grow: 1;

        @media #{$media-820} {
          padding-right: 5%;
        }
      
        @media #{$media-700} {
          width: 100%; padding-bottom: 20px; margin-bottom: 20px; border-bottom: solid 1px #6D6D73;padding-right: 0;
        }


      }
      &.footer_cl_02{ width: 23.49%;
        @media #{$media-700} {
          width: 50%; 
        }
      }
      &.footer_cl_03{ width: 23.49%;
        @media #{$media-700} {
          width: 50%;
        }
      }
    }

    h5{ font-size: 1.25rem; font-family: var(--helveticaneueltarabicBold); line-height: 100%; margin-bottom: 25px; color: $white; 
      @media #{$media-700} {
        margin-bottom: 10px;
        font-size: 14px;
      }
    }
    p{
      color: $white;
      font-size: 15px;
      line-height: 22px;
      @media #{$media-1024}  { 

        font-size: 13px;
      }
    }
  }

  .footer_logo{ width: 15%;
  
    @media #{$media-700}  {
      width: 30%;
     }
  
  
  
  }

  .footer_link{ margin: 0 -3%;
    li{ padding: 0 3%; font-size: 0.938rem;
      @media #{$media-767}  { 
        padding: 5px 0;
        width: 50%;
      }
      a{ color: $white;
        font-size: 15px;
        // font-family: var(--helveticaNeue);
        &:hover{
          color:#ad87ff;
        }
        @media #{$media-1024}  { 

          font-size: 15px;
        }
        @media #{$media-767}  { 
          font-size: 14px;
          
        }
      }
    }
    @media #{$media-767}  {
       margin: 0;
       padding-top: 15px;
    }
  }

  a{ 
    color: $white;
    font-size: 15px;
    &:hover{
      color:#ad87ff;
    }
    @media #{$media-1024}  { 
      font-size: 14px;
    }
  }
  @media #{$media-1500}  {
.footer_emaillist{
  display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      column-gap: 15px;
      width: 100%;
      justify-content: flex-start;
      text-align: left;
      align-items: flex-start;
      margin-top: 20px;display: flex
      ;
          flex-wrap: wrap;
          flex-direction: column;
          column-gap: 15px;
          width: 100%;
          justify-content: flex-start;
          text-align: left;
          align-items: flex-start;
          margin-top: 20px;
          padding-left: 0;
          row-gap: 6px;
      padding-left: 0;
      row-gap: 6px;
    li{
      margin-left: 0;
    }
}
  }
.social_media{
  margin: 0 -10px; display: flex; 
  @media #{$media-767}  {
    padding: 10px 0 15px 0;
    width: 100%;
  }
  li{ list-style: none;  padding: 0 10px;
    a{
      display: flex; align-items: center;justify-content: center; width: 47px; height: 47px; border: solid 1px #fff; border-radius: 100%; padding: 12px;

      &:hover{
        background: $blue;
      }

      @media #{$media-1024}  {
        width:35px; height: 35px; padding:8px;
      }
      @media #{$media-767}  {
        width:30px; height: 30px;
      }
      img{
        @media #{$media-1024}  {
          padding: 2px;
        }
        @media #{$media-767}  {
          padding: 4px;
        }
      }
    }
    @media #{$media-767}  {
      width: auto;
    }
  }
}


.policy_link_ul{ margin: 0 -15px; display: flex;
  li{ padding: 0 15px; list-style: none;}
}


.footer_row_2{ border-top: solid 1px #6D6D73;

  p{ color:$white; font-size: 0.875rem;
  
    @media #{$media-1024}  { 

      font-size: 13px;
    }
    @media #{$media-767}  { 

      font-size: 12px;
    }
  }

  a{
    color:$white; font-size: 0.875rem; text-decoration: underline;
    @media #{$media-1024}  { 

      font-size: 13px;
    }
    @media #{$media-1024}  { 

      font-size: 12px;
    }
  }

  @media #{$media-767}  { 
    margin-top: 20px !important;
    padding-top: 20px;
    display: flex;
    flex-wrap: wrap;
  }
.foot_copy{
  @media #{$media-767}  { 
    order: 2;
    margin-top: 15px;
    
  }
}

}
.bot_row{
  @media #{$media-700}  { width: 100%; text-align: center; display: flex; flex-wrap: wrap; justify-content: center;} 

}


 

 }



 .contact_icn{ max-width: 17px;}

 .footer_contact{
  padding-left: 30px;
  li{
    display: flex; flex-wrap: wrap; align-items: center; margin-left: 10px; margin-right: 10px;

    img{ margin-left: 10px; margin-right: 10px;}

    @media #{$media-1024}  { margin-left: 0; margin-right: 0;}
    
  }

  @media #{$media-1024}  { 
   
    margin-left: -10px; margin-right: -10px;  margin-top: 15px;padding-left: 0;
  }
  @media #{$media-767} {
    width: 100%;
    border-top: solid 1px #6D6D73;
    padding-top: 17px;
    margin-left: 0;
  }
 

 }