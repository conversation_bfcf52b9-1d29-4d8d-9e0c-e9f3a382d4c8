import React, { useEffect, useRef, useState } from "react";
import Link from "next/link"; // Import Link for navigation
import But<PERSON> from "@/component/buttion/Buttion"; // Import custom Button component
import comon from "@/styles/comon.module.scss"; // Import common styles
import inquire from "@/styles/inquireNow.module.scss"; // Import specific styles for inquire section
import buttion from "@/styles/buttion.module.scss"; // Import button-specific styles
import { useRouter } from "next/router"; // Import useRouter hook for routing
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";
import style from "@/styles/Investor-relation.module.scss";
import Image from "next/image";

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;
const InquireNow = ({ formtitle }) => {
  // Remove opacity class after image loads
  const handleLoad = (event) => {
    event.target.classList.remove("opacity-0");
  };
  const router = useRouter();

  const [crycform, setOptions] = useState(null);

  useEffect(() => {

    const fetchMyAcfOptions = async (locale) => {
      try {
        const footerPostsData = await getThemeoptions(locale);
        //console.log("Fetched options:", footerPostsData);
        setOptions(footerPostsData);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };
    fetchMyAcfOptions(router.locale);

    const restricSpace = document.querySelectorAll('input[type="tel"], input[type="text"], input[type="email"]');
    restricSpace.forEach(function (input) {
      input.addEventListener('keypress', function (e) {
        if (e.which === 32 && !this.value.length) {
          e.preventDefault();
        }
      });
    });

    const restricSymbols = document.querySelectorAll('#organization-name, #first-name, #last-name');
    restricSymbols.forEach(function (input) {
      input.setAttribute("onkeydown", "return /[a-zA-Z ]/.test(event.key)");
    });

    // Number only script using plain JavaScript
    const phoneInputs = document.querySelectorAll('input[id="phone"], input[id="mobile"]');
    phoneInputs.forEach(function (input) {
      input.addEventListener('keypress', function (e) {
        const key = e.key;
        if (!/^\d$/.test(key) && key !== 'Backspace' && key !== 'Delete') {
          e.preventDefault();
        }
      });

      input.addEventListener('input', function (e) {
        input.value = input.value.replace(/[^\d]/g, '');
      });
    });
    // Number only end
  }, [router]);

  /* Submitting Contact form */
  const [formTouched, setFormTouched] = useState(false);
  const [formValid, setFormValidation] = useState(false);
  const [formSent, setFormSent] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formError, setFormError] = useState("");


  const [validationMessages, setValidationMessages] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
  });

  const firstnameInputRef = useRef();
  const lastnameInputRef = useRef();
  const emailIdInputRef = useRef();
  const phoneNumberInputRef = useRef();
  const messageInputRef = useRef();

  const validateField = (name, value, label) => {
   // console.log(name)
    let message = "";
    if (!value) {
      //message = `${label} is required.`;
       message = router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
    } else if (name === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        message = router.locale === "ar" ? `تنسيق البريد الإلكتروني غير صالح.` : `Invalid email format.`;
      }
    } else if (name === "phone") {
      if (!value || value.replace(/\D/g, "").length <= 2) {
        message = router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
      }
    }    
    return message;
  };

  const contactFormHandler = (e) => {
    e.preventDefault();

    // Reset the error states
    setFormError("");
    setFormSuccess("");
    setFormValidation(false);



    const firstNameMessage = validateField("firstName", firstnameInputRef.current.value, router.locale === "ar" ? `الاسم الكامل` : `First Name`);
    const lastNameMessage = validateField("lastName", lastnameInputRef.current.value, router.locale === "ar" ? `الاسم العائلة` : `Last Name`);
    const emailMessage = validateField("email", emailIdInputRef.current.value, router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`);
    const phoneMessage = validateField("phone", phoneNumberInputRef.current.value, router.locale === "ar" ? `رقم الهاتف` : `Phone Number`);

    setValidationMessages({
      firstName: firstNameMessage,
      lastName: lastNameMessage,
      email: emailMessage,
      phone: phoneMessage,
    });

    const termsCheckbox = document.getElementById("terms-and-conditions"); // Safely query the checkbox
    if (!termsCheckbox || !termsCheckbox.checked) {
      setFormError(
        router.locale === "ar"
          ? `يرجى قبول الشروط والأحكام.` 
          : `Please accept the terms and conditions.`
      );
      // setFormError("Please accept the terms and conditions.");
      return; // Stop submission
    }

    const isValid = !firstNameMessage && !lastNameMessage && !emailMessage && !phoneMessage && termsCheckbox.checked;;
    setFormValidation(isValid);

    if (isValid) {
      let formData = new FormData();
      formData.append("first-name", firstnameInputRef.current.value);
      formData.append("last-name", lastnameInputRef.current.value);
      formData.append("your-email", emailIdInputRef.current.value);
      formData.append("your-phone", phoneNumberInputRef.current.value);
      formData.append("your-message", messageInputRef.current.value);
      formData.append("_wpcf7_unit_tag", "123");

      setFormSent(true);

      fetch(
        `${baseURL}wp-json/contact-form-7/v1/contact-forms/1309/feedback`,
        {
          method: "POST",
          body: formData,
          redirect: "follow",
        }
      )
        .then((response) => response.json())
        .then((data) => {
          if (data.status === "mail_sent") {
            document.getElementById("contact-form").reset();
            // Reset the values of the input fields directly
            firstnameInputRef.current.value = "";
            lastnameInputRef.current.value = "";
            emailIdInputRef.current.value = "";
            phoneNumberInputRef.current.value = "";
            messageInputRef.current.value = "";


            setFormSuccess(data.message);
            // Hide the success message after 5 seconds
            setTimeout(() => {
              setFormSuccess("");
            }, 5000);
          } else {
            setFormError(data.message);
          }
        });
    } else {
      setFormError(
        router.locale === "ar"
          ? `يوجد خطأ في حقل واحد أو أكثر. يرجى المراجعة والمحاولة مرة أخرى` 
          : `One or more fields have an error. Please check and try again.`
      );
      setFormTouched(true);
    }
  };

  const fieldChangeHandler = (label) => (e) => {
    const message = validateField(e.target.name, e.target.value, label);
    setValidationMessages((prev) => ({
      ...prev,
      [e.target.name]: message
    }));

    if (message) {
      e.target.classList.add("form-invalid");
      e.target.classList.remove("form-valid");
      setFormValidation(false);
    } else {
      e.target.classList.remove("form-invalid");
      e.target.classList.add("form-valid");
      setFormValidation(true);
    }
  };

  if (!crycform) {
    return null;
  }


  return (
    <>

      <section className={`${style.contact_form_section}`}>
        <div
          className={`${style.contact_form_container} ${comon.wrap} ${comon.pt_100}  ${comon.pb_100}`}
        >
          <div className={`${style.contact_form_left_sec} `}>
            {crycform?.ir_form_title &&
              <h3 data-aos="fade-up" data-aos-duration="1000">
                {parse(crycform?.ir_form_title)}
              </h3>
            }
            {crycform?.ir_form_content &&
              <p data-aos="fade-up" data-aos-duration="1000">
                {parse(crycform?.ir_form_content)}
              </p>
            }
            {crycform?.ircontact_email &&
              <div
                className={`${style.contact_form_contact_sec} `}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <div className={`${style.contact_form_icon} `}>
                  {" "}
                  <Image
                    src={"/images/email-icon.svg"}
                    height={42}
                    width={42}
                    alt=""
                  />
                </div>
                <h5><Link href={`mailto:${crycform?.ircontact_email}`}>{crycform?.ircontact_email}</Link></h5>
              </div>
            }
            {crycform?.ir_phone &&
              <div
                className={`${style.contact_form_contact_sec} `}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <div className={`${style.contact_form_icon} ${comon.phone_icon}`}>
                  {" "}
                  <Image
                    src={"/images/phone-icon.svg"}
                    height={42}
                    width={42}
                    alt=""
                  />
                </div>
                <h5><Link href={`tel:${crycform?.ir_phone}`}>{crycform?.ir_phone}</Link></h5>
              </div>
            }
          </div>
          <form action="" className={`contact-form inv_form ${style.contact_form_right_sec}`} onSubmit={contactFormHandler} id="contact-form">
            <ul>
              <li
                className={`${style.contact_form_input_li} `}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <input
                  type="text"
                  name="firstName"
                  id="first-name"
                  placeholder={crycform?.ir_full_name && crycform?.ir_full_name}
                  ref={firstnameInputRef}
                  onChange={fieldChangeHandler(router.locale === "ar" ? `الاسم الكامل` : `First Name`)}
                  className={`${style.contact_form_input} `}
                />

                {validationMessages.firstName && <span className="form-error">{validationMessages.firstName}</span>}
              </li>
              <li
                className={`${style.contact_form_input_li} ${comon.displaynone} ${comon.d_none}`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <input
                  type="text"
                  name="lastName"
                  id="last-name"
                  placeholder={crycform?.last_name && crycform?.last_name}
                  ref={lastnameInputRef}
                  onChange={fieldChangeHandler(router.locale === "ar" ? `الاسم العائلة` : `Last Name`)}
                  className={`${style.contact_form_input} `}
                  value={formtitle}
                />
                {validationMessages.lastName && <span className="form-error">{validationMessages.lastName}</span>}
              </li>
              <li
                className={`${style.contact_form_input_li} `}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <input
                  type="email"
                  name="email"
                  id="email-id"
                  placeholder={crycform?.ir_email && crycform?.ir_email}
                  ref={emailIdInputRef}
                  onChange={fieldChangeHandler(router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`)}
                  className={`${style.contact_form_input} `}
                />
                {validationMessages.email && <span className="form-error">{validationMessages.email}</span>}
              </li>
              <li
                className={`${style.contact_form_input_li} `}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <input
                  type="text"
                  name="phone"
                  id="phone"
                  maxLength="15"
                  placeholder={crycform?.ir_phone_number && crycform?.ir_phone_number}
                  ref={phoneNumberInputRef}
                  onChange={fieldChangeHandler(router.locale === "ar" ? `رقم الهاتف` : `Phone Number`)}
                  className={`${style.contact_form_input} `}
                />
                {validationMessages.phone && <span className="form-error">{validationMessages.phone}</span>}
              </li>

              <li
                className={`${style.contact_form_input_li} `}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                <textarea
                  name="Message"
                  id="message"
                  placeholder={crycform?.ir_message && crycform?.ir_message}
                  className={`${style.contact_form_textarea} `}
                  ref={messageInputRef}
                  onChange={fieldChangeHandler}
                />
              </li>
              <li className={`${style.contact_form_terms_and_condition} `}
                data-aos="fade-up"
                data-aos-duration="1000">
                  <div className={style.form_group}>
                    <input
                    type="checkbox"
                    id="terms-and-conditions"
                    className={`${style.contact_form_checkboxnew} `}
                  />
                  <label for="terms-and-conditions" className={style.agree_label}></label>
                  </div>
               
                {crycform?.ir_privacy_text && parse(crycform?.ir_privacy_text)}

              </li>
              <li className={`${style.contact_form_input_li} `}
                data-aos="fade-up"
                data-aos-duration="1000">
                {" "}
                <button className={`${style.contact_form_input_submit_btn} ${comon.contact_form_input_submit_btn} `}>
                  {crycform?.ir_submit}{" "}
                  <div className={`${style.contact_form_btn_icon} `}>
                    <Image
                      src={"/images/buttion_arrow_white.svg"}
                      height={12}
                      width={12}
                      alt=""
                    />
                  </div>
                </button>
              </li>

            </ul>

            {formSuccess && formValid === true && <div className="msg_success"><span className="form-success">{formSuccess}</span></div>}
            {formError && formValid === false && <div className="msg_error"><span className="form-error">{formError}</span></div>}
          </form>


        </div>

        <div className={`${style.side_image_logo} ${comon.side_image_logo} `}>
          <Image
            src={"/images/invest_shadow_bg.png"}
            height={500}
            width={200}
            alt=""
          />
        </div>
      </section>


    </>
  );
};

export default InquireNow;
