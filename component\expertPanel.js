import React from 'react';
import styles from '@/styles/experts.module.scss';
import Image from 'next/image';
import parse from "html-react-parser";

const ExpertPanel = ({image,name}) => {
  return (
    <div className={styles.expert_card}>
      {image &&
        <div className={styles.expert_pic}>
          <Image src={image} width={192} height={260} alt='image' />
        </div>
      }
      {name && <h3>{name && parse(name)}</h3>}
    </div>
  )
}

export default ExpertPanel