import React from "react";
import Head from "next/head";
const Yoast = (props) => {

  const baseUrl = "https://tam.sa/";
  const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL 

  let canonicalUrl;  
  // If a canonical URL is provided in meta
  if (props?.meta?.canonical) {
    // Check if it contains the API domain and replace it
    if (props?.meta?.canonical.includes(apiUrl)) {
      canonicalUrl = props?.meta?.canonical.replace(apiUrl, baseUrl);
    } else {
      canonicalUrl = props?.meta?.canonical;
    }
  }

  if (props.meta) {
    return (
      <Head>
       {props.meta.title && <title>{props.meta.title}</title>}
        <meta
          name="robots"
          content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
        />
        {/* <meta name="robots" content="noindex, nofollow" />         */}
        <meta name="description" content={props.meta.og_description} />
        <meta property="og:locale" content={props.meta.og_locale} />
        <meta property="og:type" content={props.meta.og_type} />
        <meta property="og:title" content={props.meta.og_title} />
        <meta property="og:description" content={props.meta.og_description} />
        <meta property="og:site_name" content={props.meta.og_site_name} />
        <meta
          property="article:modified_time"
          content={props.meta.article_modified_time}
        />
        <meta name="google-site-verification" content="" />

        <link rel="canonical" href={canonicalUrl || "/"} />
        <meta property="og:url" content="/" />
        <link rel="icon" type="image/png" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/favicon.ico" />

        {props.meta.og_image && (
          <meta property="og:image" content={props.meta.og_image[0].url} />
        )}

        <meta name="twitter:card" content="summary" />
        <meta property="twitter:url" content={props.meta.og_url}></meta>
        <meta name="twitter:title" content={props.meta.og_title} />
        <meta name="twitter:description" content={props.meta.og_description} />
      </Head>
    );
  } else {
    return null;
  }
};

export default Yoast;
