@import "variable", "base";

.form_ul {
  margin: 0 -1%;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    // padding: 0% 1%;  

    &.form_Cl_01 {
      width: 67.5%;

      @media #{$media-700} {
        width: 100%;
      }
    }

    &.form_Cl_02 {
      width: 31.6%;
      padding-inline-start: 1.5%;
      @media #{$media-700} {
        width: 100%;
      }
    }

    .form_cl_set_2 {
      li {
        padding-bottom:25px;
        @media #{$media-700} {
          padding-bottom:15px;
        }
      }
    }
  }
}

.form_cl_set_2 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1%;
  margin-inline-start: 0;

  li {
    width: 50%;
    padding: 0 1.2%;
    position: relative;

    @media #{$media-700} {
      width: 100%;
    }
  }
}
.inq_btn{
    height: 60px;
    padding-left: 30px;
    padding-right: 30px;
    border-radius: 35px;
    display: inline-block;
    justify-content: center;
    font-size: 16px;
    display: inline-flex;
    font-family: var(--helveticaNeueMedium);
    align-items: center;
    line-height: 20px;
    border: 1px solid #fff;
    color: #fff;
    background: transparent;
    transition: all .3s ease-in-out;
    position: relative;
    cursor: pointer;
    .contact_form_btn_icon{
        margin-inline-start: 10px;
    }
    &:hover{
      background-color: #212035;
      border: 1px solid #212035;
    }
    @media #{$media-1600} {
      height: 50px;
    padding-left: 25px;
    padding-right: 25px;
    }
    @media #{$media-820} {
        height: 50px;
        font-size: 14px;
        padding-left: 20px;
        padding-right: 20px;
    }
    @media #{$media-820} {
      height: 40px;
      padding-left: 15px;
      padding-right: 15px;
  }
}
.form_contact{
  @media #{$media-700} {
    margin-top: 20px;
  }
}