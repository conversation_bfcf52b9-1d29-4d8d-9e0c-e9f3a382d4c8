import React, { useEffect, useState } from "react";
import header from "@/styles/header.module.scss";
import common from "@/styles/comon.module.scss";
import Marquee from "react-fast-marquee";
import parse from "html-react-parser";
import { useRouter } from "next/router";

const Headermarquee = () => {
  const router = useRouter();
  const language = router.locale === "ar" ? "ar" : "en";
  const direction = language === "ar" ? "right" : "left";
  const [mardueeitems, setMardueeitems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let intervalId; // Store interval ID for cleanup

    const fetchTadawulData = async () => {
      try {
        const response = await fetch('/api/tadawul');
        
        if (!response.ok) {
          throw new Error(`API responded with status: ${response.status}`);
        }

        const result = await response.json();
        // console.log('API response:', result);
        
        if (!result.success) {
          throw new Error(result.message || "API returned an error");
        }

        const companyData = result.data;

        // Create a new marquee item with the fetched data
        const newItem = {
          marquee_title: router.locale === 'en' ? companyData.companyLongName : companyData.companyLongNameAr,          
          marquee_content: `<span>${router.locale === 'en' ? 'A Digital Solutions and Advisory Services' : 'شركة حلول رقمية وخدمات استشارية'}</span>`,
          marquee_date: `${router.locale === 'en' ? companyData.companyLongName : companyData.companyLongNameAr} <span>${companyData.lastUpdateTime}</span>`,
          marquee_last: `<span>${parseFloat(companyData?.lastTradePrice || 0).toFixed(2)} ${router.locale === 'en' ? 'SAR' : 'ريال سعودي'}</span>`,
          marquee_amount: `<span>(${parseFloat(companyData?.changePercentage || 0).toFixed(2)}) / (${parseFloat(companyData?.changePercentage || 0).toFixed(2)}%)</span>`,        
          marquee_size: `<span> ${router.locale === 'en' ? 'Size :' : 'الحجم :'} ${parseFloat(companyData?.avgTradeSize || 0).toFixed(2)}</span>`,
        };

        // Update the marquee items with the new data
        setMardueeitems([newItem]);
        setError(null);
      } catch (error) {
        console.error("Error fetching Tadawul data:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    // Fetch immediately
    fetchTadawulData();

    // Set interval to fetch data every 10 seconds
    intervalId = setInterval(fetchTadawulData, 10000);

    // Cleanup interval on unmount
    return () => clearInterval(intervalId);
  }, [router.locale]); // Dependency array ensures refetching when locale changes

  return (
    <section className={`${header.marquee}`} role="marquee">
      <div className={`${header.about_hero__marquee_row}`}>
        
        <Marquee speed={100} pauseOnHover={true} loop={0} direction={direction}>
          {loading ? (
          <></>
          ) : (
              Array.from({ length: 3 }).map((_, repeatIndex) => (
            mardueeitems && mardueeitems.map((itemObj, index) => (
              <div
                className={`${header.marquee_item} ${common.marquee_item} about_hero__marquee_item`}
                key={index}
              >   
                <div className={common.marq_data}>{itemObj.marquee_title && parse(itemObj.marquee_title)} {itemObj.marquee_content && parse(itemObj.marquee_content)} </div>
               <div className={common.marq_data}>{itemObj.marquee_date && parse(itemObj.marquee_date)} 
                {itemObj.marquee_last && parse(itemObj.marquee_last)}
                  {itemObj.marquee_amount && parse(itemObj.marquee_amount)}                
                {itemObj.marquee_size && parse(itemObj.marquee_size)}</div>
              </div>
            ))
          ))
        )}
          </Marquee>
       
      </div>
    </section>
  );
};

export default Headermarquee;